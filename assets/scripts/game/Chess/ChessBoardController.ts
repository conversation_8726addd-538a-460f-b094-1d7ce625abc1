// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { RoomUser, HexCoord } from "../../bean/GameBean";
import { GlobalBean } from "../../bean/GlobalBean";
import PlayerGameController from "../../pfb/PlayerGameController ";

const {ccclass, property} = cc._decorator;

// 棋盘格子数据接口
export interface GridData {
    x: number;  // 格子的x坐标 (0-7)
    y: number;  // 格子的y坐标 (0-7)
    worldPos: cc.Vec2;  // 格子在世界坐标系中的位置
    hasPlayer: boolean;  // 是否已经放置了玩家预制体
    playerNode?: cc.Node;  // 放置的玩家节点引用
}

@ccclass
export default class ChessBoardController extends cc.Component {

    @property(cc.Prefab)
    playerGamePrefab: cc.Prefab = null;

    @property(cc.Prefab)
    boomPrefab: cc.Prefab = null;

    @property(cc.Prefab)
    biaojiPrefab: cc.Prefab = null;

    @property(cc.Prefab)
    boom1Prefab: cc.Prefab = null;

    @property(cc.Prefab)
    boom2Prefab: cc.Prefab = null;

    @property(cc.Prefab)
    boom3Prefab: cc.Prefab = null;

    @property(cc.Prefab)
    boom4Prefab: cc.Prefab = null;

    @property(cc.Prefab)
    boom5Prefab: cc.Prefab = null;

    @property(cc.Prefab)
    boom6Prefab: cc.Prefab = null;

    @property(cc.Prefab)
    boom7Prefab: cc.Prefab = null;

    @property(cc.Prefab)
    boom8Prefab: cc.Prefab = null;  // player_game_pfb 预制体

    @property(cc.Node)
    boardNode: cc.Node = null;  // 棋盘节点



    // 棋盘配置
    private readonly BOARD_SIZE = 8;  // 8x8棋盘
    private readonly BOARD_WIDTH = 750;  // 棋盘总宽度
    private readonly BOARD_HEIGHT = 750;  // 棋盘总高度
    private readonly GRID_SIZE = 88;  // 每个格子的大小 88x88

    // 格子数据存储
    private gridData: GridData[][] = [];  // 二维数组存储格子数据
    private gridNodes: cc.Node[][] = [];  // 二维数组存储格子节点

    onLoad() {
        this.initBoard();
    }

    start() {
       

        // 延迟一帧后再次尝试启用触摸事件，确保所有节点都已创建完成
        this.scheduleOnce(() => {
            this.enableTouchForExistingGrids();
        }, 0.1);
    }

    // 初始化棋盘
    private initBoard() {
        // 初始化数据数组
        for (let x = 0; x < this.BOARD_SIZE; x++) {
            this.gridData[x] = [];
            this.gridNodes[x] = [];
            for (let y = 0; y < this.BOARD_SIZE; y++) {
                this.gridData[x][y] = {
                    x: x,
                    y: y,
                    worldPos: this.getGridWorldPosition(x, y),
                    hasPlayer: false
                };
            }
        }

        this.createGridNodes();
    }

    // 启用现有格子的触摸事件
    private createGridNodes() {
        if (!this.boardNode) {
            console.error("棋盘节点未设置！");
            return;
        }

        // 如果格子已经存在，直接启用触摸事件
        this.enableTouchForExistingGrids();
    }

    // 为现有格子启用触摸事件
    private enableTouchForExistingGrids() {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法启用触摸事件！");
            return;
        }

        // 遍历棋盘节点的所有子节点
        let children = this.boardNode.children;


        for (let i = 0; i < children.length; i++) {
            let child = children[i];

            // 尝试从节点名称解析坐标
            let coords = this.parseGridCoordinateFromName(child.name);
            if (coords) {
                this.setupGridTouchEvents(child, coords.x, coords.y);
                this.gridNodes[coords.x] = this.gridNodes[coords.x] || [];
                this.gridNodes[coords.x][coords.y] = child;
            } else {
                // 如果无法从名称解析，尝试从位置计算
                let pos = child.getPosition();
                let coords = this.getGridCoordinateFromPosition(pos);
                if (coords) {
                    this.setupGridTouchEvents(child, coords.x, coords.y);
                    this.gridNodes[coords.x] = this.gridNodes[coords.x] || [];
                    this.gridNodes[coords.x][coords.y] = child;
                }
            }
        }

    }

    // 从节点名称解析格子坐标
    private parseGridCoordinateFromName(nodeName: string): {x: number, y: number} | null {
        // 尝试匹配 Grid_x_y 格式
        let match = nodeName.match(/Grid_(\d+)_(\d+)/);
        if (match) {
            return {x: parseInt(match[1]), y: parseInt(match[2])};
        }
        return null;
    }

    // 从位置计算格子坐标
    private getGridCoordinateFromPosition(pos: cc.Vec2): {x: number, y: number} | null {
        let x = Math.floor((pos.x + this.BOARD_WIDTH / 2) / this.GRID_SIZE);
        let y = Math.floor((pos.y + this.BOARD_HEIGHT / 2) / this.GRID_SIZE);

        if (this.isValidCoordinate(x, y)) {
            return {x: x, y: y};
        }
        return null;
    }

    // 为格子节点设置触摸事件
    private setupGridTouchEvents(gridNode: cc.Node, x: number, y: number) {
        // 安全检查：确保坐标有效
        if (!this.isValidCoordinate(x, y)) {
            console.error(`❌ setupGridTouchEvents: 尝试为无效坐标(${x},${y})设置触摸事件`);
            return;
        }

 

        // 长按相关变量
        let isLongPressing = false;
        let longPressTimer = 0;
        let longPressCallback: Function = null;
        const LONG_PRESS_TIME = 1.0; // 1秒长按时间

        // 触摸开始事件
        gridNode.on(cc.Node.EventType.TOUCH_START, (_event: cc.Event.EventTouch) => {
            isLongPressing = true;
            longPressTimer = 0;

            // 开始长按检测
            longPressCallback = () => {
                if (isLongPressing) {
                    longPressTimer += 0.1;
                    if (longPressTimer >= LONG_PRESS_TIME) {
                
                        this.onGridLongPress(x, y);
                        isLongPressing = false;
                        if (longPressCallback) {
                            this.unschedule(longPressCallback);
                        }
                    }
                }
            };
            this.schedule(longPressCallback, 0.1);
        }, this);

        // 触摸结束事件
        gridNode.on(cc.Node.EventType.TOUCH_END, (event: cc.Event.EventTouch) => {
            // 如果不是长按，则执行点击事件
            if (isLongPressing && longPressTimer < LONG_PRESS_TIME) {
   
                this.onGridClick(x, y, event);
            }

            isLongPressing = false;
            if (longPressCallback) {
                this.unschedule(longPressCallback);
            }
        }, this);

        // 触摸取消事件
        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, (_event: cc.Event.EventTouch) => {
            isLongPressing = false;
            if (longPressCallback) {
                this.unschedule(longPressCallback);
            }
        }, this);

        // 添加Button组件以确保触摸响应
        let button = gridNode.getComponent(cc.Button);
        if (!button) {
            button = gridNode.addComponent(cc.Button);
            button.transition = cc.Button.Transition.SCALE;
            button.zoomScale = 0.95;
        }
    }

    // 计算格子的世界坐标位置（左下角为(0,0)）
    private getGridWorldPosition(x: number, y: number): cc.Vec2 {
        // 计算格子中心点位置
        // 左下角为(0,0)，所以y坐标需要从下往上计算
        let posX = (x * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_WIDTH / 2);
        let posY = (y * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_HEIGHT / 2);
        
        return cc.v2(posX, posY);
    }

    // 格子点击事件 - 发送挖掘操作
    private onGridClick(x: number, y: number, _event?: cc.Event.EventTouch) {
       

        // 检查坐标是否有效（确保在8x8棋盘范围内）
        if (!this.isValidCoordinate(x, y)) {
            
            return;
        }

        // 检查该位置是否已经有玩家预制体
        if (this.gridData[x][y].hasPlayer) {
          
            return;
        }

        // 发送挖掘操作事件 (action = 1)
        // 只发送事件，不直接生成预制体，等待GamePageController确认后再生成
        this.node.emit('chess-board-click', {
            x: x,
            y: y,
            action: 1  // 1 = 挖掘
        });
    }

    // 格子长按事件 - 发送标记操作
    private onGridLongPress(x: number, y: number) {
        

        // 检查坐标是否有效（确保在8x8棋盘范围内）
        if (!this.isValidCoordinate(x, y)) {
            
            return;
        }

        // 检查该位置是否已经有玩家预制体
        if (this.gridData[x][y].hasPlayer) {
           
            return;
        }

        // 发送标记操作事件 (action = 2)
        // 只发送事件，不直接生成预制体，等待GamePageController确认后再生成
        this.node.emit('chess-board-click', {
            x: x,
            y: y,
            action: 2  // 2 = 标记
        });
    }

    // 在格子上放置玩家预制体
    public placePlayerOnGrid(x: number, y: number, withFlag: boolean = false) {
        // 双重检查：确保坐标有效
        if (!this.isValidCoordinate(x, y)) {
            console.error(`❌ placePlayerOnGrid: 无效坐标(${x},${y})`);
            return;
        }

        // 双重检查：确保格子为空
        let gridData = this.gridData[x][y];
        if (gridData.hasPlayer) {
            console.error(`❌ placePlayerOnGrid: 格子(${x},${y})已有玩家，不能重复放置`);
            return;
        }

        if (!this.playerGamePrefab) {
            console.error("❌ 玩家预制体未设置！");
            return;
        }

        if (!this.boardNode) {
            console.error("❌ 棋盘节点未设置！");
            return;
        }

        // 实例化玩家预制体
        let playerNode = cc.instantiate(this.playerGamePrefab);

        // 计算正确的位置
        let correctPosition = this.calculateCorrectPosition(x, y);
        playerNode.setPosition(correctPosition);

      

        // 先隐藏节点，等头像加载完成后再显示
        playerNode.active = false;

        // 处理Layout限制问题
        this.addPlayerNodeSafely(playerNode);

        // 设置头像和用户数据（异步加载）
        this.setupPlayerAvatarAsync(playerNode, x, y, withFlag, () => {
            // 头像加载完成的回调，播放生成动画（点击生成和单人格子）
            this.playAvatarSpawnAnimation(playerNode);
        });

        // 更新格子数据
        gridData.hasPlayer = true;
        gridData.playerNode = playerNode;
    }

    // 计算正确的位置（格子中心偏移(0, -16)）
    private calculateCorrectPosition(x: number, y: number): cc.Vec2 {
        // 使用自定义偏移量
        let offsetX = this.customOffsetX;
        let offsetY = this.customOffsetY;

        // 方法1: 如果能找到对应的格子节点，使用其位置并添加偏移
        let targetGridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (targetGridNode) {
            let gridPos = targetGridNode.getPosition();
            let finalPos = cc.v2(gridPos.x + offsetX, gridPos.y + offsetY);
        
            return finalPos;
        }

        // 方法2: 基于棋盘的实际位置计算
       

        // 计算格子中心位置
        let centerX = (x * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_WIDTH / 2);
        let centerY = (y * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_HEIGHT / 2);

        // 添加偏移
        let finalX = centerX + offsetX;
        let finalY = centerY + offsetY;

        let calculatedPos = cc.v2(finalX, finalY);
       

        return calculatedPos;
    }

    /**
     * 计算预制体的精确位置（根据您提供的坐标规律）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 预制体应该放置的精确位置
     */
    private calculatePrefabPosition(x: number, y: number): cc.Vec2 {
        // 根据您提供的坐标规律计算：
        // (0,0) → (-314, -310)
        // (1,0) → (-224, -310)  // x增加90
        // (0,1) → (-314, -222)  // y增加88
        // (7,7) → (310, 312)

        const startX = -314;  // 起始X坐标
        const startY = -310;  // 起始Y坐标
        const stepX = 90;     // X方向步长
        const stepY = 88;     // Y方向步长

        const finalX = startX + (x * stepX);
        const finalY = startY + (y * stepY);

        const position = cc.v2(finalX, finalY);
       

        return position;
    }

    /**
     * 播放头像生成动画（由大变小）
     * @param playerNode 玩家节点
     */
    private playAvatarSpawnAnimation(playerNode: cc.Node) {
        if (!playerNode) {
            console.warn("播放生成动画失败：节点为空");
            return;
        }

       

        // 显示节点
        playerNode.active = true;

        // 设置初始缩放为1.5倍（比正常大）
        const originalScale = playerNode.scaleX;
        const startScale = originalScale * 1.5;
        playerNode.setScale(startScale);

        // 使用cc.Tween创建由大变小的缩放动画
        cc.tween(playerNode)
            .to(0.3, { scaleX: originalScale, scaleY: originalScale }, { easing: 'backOut' })
            .start();

      
    }

    /**
     * 播放头像调整动画（平滑移动和缩小）
     * @param playerNode 玩家节点
     * @param newPosition 新位置
     * @param newScale 新缩放
     */
    private playAvatarAdjustAnimation(playerNode: cc.Node, newPosition: cc.Vec2, newScale: number) {
        if (!playerNode) {
            console.warn("播放调整动画失败：节点为空");
            return;
        }

       

        // 使用cc.Tween同时播放移动和缩放动画
        cc.tween(playerNode)
            .to(0.3, {
                x: newPosition.x,
                y: newPosition.y,
                scaleX: newScale,
                scaleY: newScale
            }, { easing: 'sineOut' })
            .start();

       
    }

    /**
     * 根据格子总人数计算基础位置（统一逻辑）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param totalPlayers 该格子的总人数
     * @returns 基础位置
     */
    private calculateBasePositionByPlayerCount(x: number, y: number, totalPlayers: number): cc.Vec2 {
        let offsetX = this.customOffsetX;
        let offsetY: number;

        if (totalPlayers === 1) {
            // 一个格子里只有一个人：需要偏移
            offsetY = this.customOffsetY; // -16
           
        } else {
            // 一个格子里有两个及以上：不偏移
            offsetY = 0;
           
        }

        // 方法1: 如果能找到对应的格子节点，使用其位置并添加偏移
        let targetGridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (targetGridNode) {
            let gridPos = targetGridNode.getPosition();
            let finalPos = cc.v2(gridPos.x + offsetX, gridPos.y + offsetY);
           
            return finalPos;
        }

        // 方法2: 基于棋盘的实际位置计算
        let centerX = (x * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_WIDTH / 2);
        let centerY = (y * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_HEIGHT / 2);

        let finalX = centerX + offsetX;
        let finalY = centerY + offsetY;

        let calculatedPos = cc.v2(finalX, finalY);
        

        return calculatedPos;
    }

    /**
     * 计算多人情况下的基础位置（不包含往下偏移，逻辑分开）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 格子中心位置（多人专用，不偏移）
     */
    private calculateMultiPlayerBasePosition(x: number, y: number): cc.Vec2 {
        // 多人情况使用独立的偏移逻辑
        let offsetX = this.customOffsetX;
        let offsetY = 0; // 多人时不往下偏移，逻辑分开

        // 方法1: 如果能找到对应的格子节点，使用其位置并添加偏移
        let targetGridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (targetGridNode) {
            let gridPos = targetGridNode.getPosition();
            let finalPos = cc.v2(gridPos.x + offsetX, gridPos.y + offsetY);
          
            return finalPos;
        }

        // 方法2: 基于棋盘的实际位置计算
        // 计算格子中心位置
        let centerX = (x * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_WIDTH / 2);
        let centerY = (y * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_HEIGHT / 2);

        // 添加偏移（不包含往下偏移）
        let finalX = centerX + offsetX;
        let finalY = centerY + offsetY;

        let calculatedPos = cc.v2(finalX, finalY);
       

        return calculatedPos;
    }

    // 安全地添加玩家节点（处理Layout限制）
    private addPlayerNodeSafely(playerNode: cc.Node) {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法添加玩家节点！");
            return;
        }

        // 检查棋盘节点是否有Layout组件
        let layout = this.boardNode.getComponent(cc.Layout);
        if (layout) {
           

            // 方案1: 临时禁用Layout
            layout.enabled = false;
           

            // 添加节点
            this.boardNode.addChild(playerNode);


        } else {
           
            this.boardNode.addChild(playerNode);
        }

        // 方案2备选：添加到Layout外部
        // this.addToParentNode(playerNode);
    }

    // 备选方案：添加到父节点（Layout外部）
    private addToParentNode(playerNode: cc.Node) {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法添加玩家节点！");
            return;
        }

        if (this.boardNode.parent) {
            // 需要转换坐标系
            let worldPos = this.boardNode.convertToWorldSpaceAR(playerNode.getPosition());
            let localPos = this.boardNode.parent.convertToNodeSpaceAR(worldPos);

            playerNode.setPosition(localPos);
            this.boardNode.parent.addChild(playerNode);

            
        } else {
            console.error(`❌ 棋盘节点没有父节点`);
            // 回退到直接添加
            this.boardNode.addChild(playerNode);
        }
    }

    // 异步设置玩家头像（带回调）
    private setupPlayerAvatarAsync(playerNode: cc.Node, x: number, y: number, withFlag: boolean, onComplete: () => void) {
        // 查找PlayerGameController组件
        let playerController = playerNode.getComponent("PlayerGameController") ||
                              playerNode.getComponent("PlayerGameController ") ||
                              playerNode.getComponentInChildren("PlayerGameController");

        if (playerController) {
            // 检查avatar节点是否存在
            if (playerController.avatar) {
                // 检查avatar节点是否有Sprite组件
                let avatarSprite = playerController.avatar.getComponent(cc.Sprite);
                if (!avatarSprite) {
                    avatarSprite = playerController.avatar.addComponent(cc.Sprite);
                }

                // 确保avatar节点可见
                playerController.avatar.active = true;
                playerController.avatar.opacity = 255;
            } else {
                console.error("❌ PlayerGameController中的avatar节点为null");
                onComplete();
                return;
            }

            // 设置旗子节点的显示状态 - 重点检查
            if (playerController.flagNode) {
              
                playerController.flagNode.active = withFlag;
              

                // 额外检查旗子节点的可见性
                if (withFlag) {
                    playerController.flagNode.opacity = 255;
                   

                    // 确保旗子节点的父节点也是可见的
                    let parent = playerController.flagNode.parent;
                    while (parent && parent !== playerNode) {
                        
                        parent.active = true;
                        parent = parent.parent;
                    }

                    // 延迟检查旗子是否真的显示了
                    this.scheduleOnce(() => {
                       
                    }, 1.0);
                }
            } else {
                console.warn(`⚠️ 找不到旗子节点 (${x},${y})`);
            }

            // 创建用户数据并设置头像
            let userData = {
                userId: `player_${x}_${y}`,
                nickName: `玩家(${x},${y})`,
                avatar: this.getDefaultAvatarUrl(),
                score: 0,
                pos: 0,
                coin: 0,
                status: 0,
                rank: 0
            } as RoomUser;

            // 使用PlayerGameController的setData方法来设置头像
            try {
                playerController.setData(userData);

                // 延迟设置旗子状态，确保在PlayerGameController初始化之后
                this.scheduleOnce(() => {
                    if (playerController.flagNode) {
                        playerController.flagNode.active = withFlag;
                       
                    }
                    onComplete();
                }, 0.1);
            } catch (error) {
                console.error("设置头像数据失败:", error);
                onComplete();
            }

        } else {
            console.warn("⚠️ 找不到PlayerGameController组件");
            this.tryDirectAvatarSetupAsync(playerNode, x, y, onComplete);
        }
    }

   
    // 设置玩家头像（保留原方法用于其他地方）
    private setupPlayerAvatar(playerNode: cc.Node, x: number, y: number) {
       

        // 查找PlayerGameController组件
        let playerController = playerNode.getComponent("PlayerGameController") ||
                              playerNode.getComponent("PlayerGameController ") ||
                              playerNode.getComponentInChildren("PlayerGameController");

        if (playerController) {
         

            // 检查avatar节点是否存在
            if (playerController.avatar) {
                

                // 检查avatar节点是否有Sprite组件
                let avatarSprite = playerController.avatar.getComponent(cc.Sprite);
                if (!avatarSprite) {
                    console.warn("⚠️ avatar节点缺少Sprite组件，正在添加...");
                    avatarSprite = playerController.avatar.addComponent(cc.Sprite);
                }

                // 确保avatar节点可见
                playerController.avatar.active = true;
                playerController.avatar.opacity = 255;

               
            } else {
                console.error("❌ PlayerGameController中的avatar节点为null");
                return;
            }

            // 创建用户数据
            let userData = {
                userId: `player_${x}_${y}`,
                nickName: `玩家(${x},${y})`,
                avatar: this.getDefaultAvatarUrl(),
                score: 0,
                pos: 0,
                coin: 0,
                status: 0,
                rank: 0
            } as RoomUser;

            

            // 安全地调用setData
            try {
                playerController.setData(userData);
               
                // 延迟检查头像是否加载成功
                this.scheduleOnce(() => {
                    this.checkAvatarLoaded(playerController.avatar, x, y);
                }, 2.0);

            } catch (error) {
                console.error("❌ 设置头像时出错:", error);
            }
        } else {
            console.warn("⚠️ 找不到PlayerGameController组件，跳过头像设置");

            // 尝试直接在节点上查找avatar子节点
            this.tryDirectAvatarSetup(playerNode, x, y);
        }
    }

    // 检查头像是否加载成功
    private checkAvatarLoaded(avatarNode: cc.Node, x: number, y: number) {
        if (!avatarNode) {
            console.error(`❌ 位置(${x},${y})的avatar节点为null`);
            return;
        }

        let sprite = avatarNode.getComponent(cc.Sprite);
        if (!sprite) {
            console.error(`❌ 位置(${x},${y})的avatar节点没有Sprite组件`);
            return;
        }

        if (!sprite.spriteFrame) {
            console.warn(`⚠️ 位置(${x},${y})的头像可能加载失败，spriteFrame为null`);

            // 尝试设置一个默认的颜色作为备用显示
            this.setFallbackAvatar(avatarNode, x, y);
        } else {
           
        }

        
    }

    // 设置备用头像（纯色方块）
    private setFallbackAvatar(avatarNode: cc.Node, x: number, y: number) {
       
        let sprite = avatarNode.getComponent(cc.Sprite);
        if (!sprite) {
            sprite = avatarNode.addComponent(cc.Sprite);
        }

        // 创建一个简单的纯色纹理
        let texture = new cc.Texture2D();
        let colors = [
            [255, 107, 107, 255], // 红色
            [78, 205, 196, 255],  // 青色
            [69, 183, 209, 255],  // 蓝色
            [150, 206, 180, 255], // 绿色
            [255, 234, 167, 255]  // 黄色
        ];

        let colorIndex = (x + y) % colors.length;
        let color = colors[colorIndex];

        texture.initWithData(new Uint8Array(color), cc.Texture2D.PixelFormat.RGBA8888, 1, 1);
        sprite.spriteFrame = new cc.SpriteFrame(texture);

        // 设置大小
        avatarNode.setContentSize(80, 80);
        avatarNode.active = true;

       
    }

    // 尝试直接设置头像（当找不到PlayerGameController时）
    private tryDirectAvatarSetup(playerNode: cc.Node, x: number, y: number) {
        
        // 查找名为"avatar"的子节点
        let avatarNode = playerNode.getChildByName("avatar");
        if (avatarNode) {
           
            this.setFallbackAvatar(avatarNode, x, y);
        } else {
            console.warn("⚠️ 未找到avatar子节点");

            // 列出所有子节点名称
        
        }
    }

    // 获取默认头像URL
    private getDefaultAvatarUrl(): string {
        // 使用真实的头像URL
        return "https://static.gooplay.com/online/user-avatar/1732786296530322669.jpg";
    }

    // 保存格子坐标（用于后续发送给后端）
    private saveGridCoordinate(x: number, y: number) {
        // 这里可以将坐标保存到数组或发送给后端
       

        // 示例：可以调用网络管理器发送坐标
        this.sendCoordinateToServer(x, y);

        // 或者保存到本地数组以备后用
        this.addToCoordinateHistory(x, y);
    }

    // 发送坐标到服务器
    private sendCoordinateToServer(x: number, y: number) {
        // 构造发送数据
        let moveData = {
            x: x,
            y: y,
            timestamp: Date.now(),
            playerId: this.getCurrentPlayerId()
        };


        // 暂时只是打印，避免未使用变量警告
        return moveData;
    }

    // 添加到坐标历史记录
    private coordinateHistory: {x: number, y: number, timestamp: number}[] = [];

    private addToCoordinateHistory(x: number, y: number) {
        this.coordinateHistory.push({
            x: x,
            y: y,
            timestamp: Date.now()
        });

       
    }

    // 获取当前玩家ID（示例）
    private getCurrentPlayerId(): string {
        // 这里应该从全局状态或用户数据中获取
        return "player_001";  // 示例ID
    }

    // 获取指定坐标的格子数据
    public getGridData(x: number, y: number): GridData | null {
        if (x < 0 || x >= this.BOARD_SIZE || y < 0 || y >= this.BOARD_SIZE) {
            return null;
        }
        return this.gridData[x][y];
    }

    // 清除指定格子的玩家
    public clearGridPlayer(x: number, y: number): boolean {
        let gridData = this.getGridData(x, y);
        if (!gridData || !gridData.hasPlayer) {
            return false;
        }

        // 移除玩家节点
        if (gridData.playerNode) {
            gridData.playerNode.removeFromParent();
            gridData.playerNode = null;
        }

        // 更新数据
        gridData.hasPlayer = false;
        
       
        return true;
    }

    // 清除所有玩家
    public clearAllPlayers() {
        for (let x = 0; x < this.BOARD_SIZE; x++) {
            for (let y = 0; y < this.BOARD_SIZE; y++) {
                this.clearGridPlayer(x, y);
            }
        }
       
    }

    // 获取所有已放置玩家的坐标
    public getAllPlayerCoordinates(): {x: number, y: number}[] {
        let coordinates: {x: number, y: number}[] = [];

        for (let x = 0; x < this.BOARD_SIZE; x++) {
            for (let y = 0; y < this.BOARD_SIZE; y++) {
                if (this.gridData[x][y].hasPlayer) {
                    coordinates.push({x: x, y: y});
                }
            }
        }

        return coordinates;
    }

    // 检查坐标是否有效
    public isValidCoordinate(x: number, y: number): boolean {
        return x >= 0 && x < this.BOARD_SIZE && y >= 0 && y < this.BOARD_SIZE;
    }

    // 检查格子是否为空
    public isGridEmpty(x: number, y: number): boolean {
        if (!this.isValidCoordinate(x, y)) {
            return false;
        }
        return !this.gridData[x][y].hasPlayer;
    }

    // 获取坐标历史记录
    public getCoordinateHistory(): {x: number, y: number, timestamp: number}[] {
        return [...this.coordinateHistory];  // 返回副本
    }

    // 清除坐标历史记录
    public clearCoordinateHistory() {
        this.coordinateHistory = [];
      
    }

    // 根据世界坐标获取格子坐标
    public getGridCoordinateFromWorldPos(worldPos: cc.Vec2): {x: number, y: number} | null {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法获取格子坐标！");
            return null;
        }

        // 将世界坐标转换为相对于棋盘的坐标
        let localPos = this.boardNode.convertToNodeSpaceAR(worldPos);

        // 计算格子坐标
        let x = Math.floor((localPos.x + this.BOARD_WIDTH / 2) / this.GRID_SIZE);
        let y = Math.floor((localPos.y + this.BOARD_HEIGHT / 2) / this.GRID_SIZE);

        if (this.isValidCoordinate(x, y)) {
            return {x: x, y: y};
        }

        return null;
    }

    // 高亮显示格子（可选功能）
    public highlightGrid(x: number, y: number, highlight: boolean = true) {
        if (!this.isValidCoordinate(x, y)) {
            return;
        }

        let gridNode = this.gridNodes[x][y];
        if (gridNode) {
            // 这里可以添加高亮效果，比如改变颜色或添加边框
            if (highlight) {
                gridNode.color = cc.Color.YELLOW;
                
            } else {
                gridNode.color = cc.Color.WHITE;
              
            }
        }
    }

    // 批量放置玩家（用于从服务器同步数据）
    public batchPlacePlayers(coordinates: {x: number, y: number}[]) {
       

        coordinates.forEach(coord => {
            if (this.isValidCoordinate(coord.x, coord.y) && this.isGridEmpty(coord.x, coord.y)) {
                this.placePlayerOnGrid(coord.x, coord.y);
            }
        });
    }

    // 手动启用触摸事件（调试用）
    public manualEnableTouch() {
        
        this.enableTouchForExistingGrids();
    }

    // 测试点击功能（调试用）
    public testClick(x: number, y: number) {
        
        this.onGridClick(x, y);
    }

    // 获取棋盘状态信息（调试用）
    public getBoardInfo() {
        let info = {
            boardSize: this.BOARD_SIZE,
            gridSize: this.GRID_SIZE,
            boardWidth: this.BOARD_WIDTH,
            boardHeight: this.BOARD_HEIGHT,
            totalGrids: this.BOARD_SIZE * this.BOARD_SIZE,
            boardNodeChildren: this.boardNode ? this.boardNode.children.length : 0,
            playerCount: this.getAllPlayerCoordinates().length,
            hasPlayerGamePrefab: !!this.playerGamePrefab,
            hasBoardNode: !!this.boardNode
        };

 
        return info;
    }

    // 简单测试方法 - 只测试位置，不加载头像
    public simpleTest(x: number, y: number) {
       

        if (!this.boardNode) {
            console.error("❌ 棋盘节点未设置");
            return;
        }

        // 创建一个简单的彩色方块
        let testNode = new cc.Node(`Test_${x}_${y}`);

        // 添加一个彩色方块
        let sprite = testNode.addComponent(cc.Sprite);
        let texture = new cc.Texture2D();
        let color = [Math.random() * 255, Math.random() * 255, Math.random() * 255, 255];
        texture.initWithData(new Uint8Array(color), cc.Texture2D.PixelFormat.RGBA8888, 1, 1);
        sprite.spriteFrame = new cc.SpriteFrame(texture);

        // 设置大小
        testNode.setContentSize(60, 60);

        // 计算位置
        let pos = this.calculateCorrectPosition(x, y);
        testNode.setPosition(pos);

        // 添加坐标标签
        let labelNode = new cc.Node("Label");
        let label = labelNode.addComponent(cc.Label);
        label.string = `(${x},${y})`;
        label.fontSize = 16;
        label.node.color = cc.Color.WHITE;
        labelNode.setPosition(0, 0);
        testNode.addChild(labelNode);

        // 添加到棋盘（处理Layout问题）
        this.addPlayerNodeSafely(testNode);

        
    }

    // 清除所有测试节点
    public clearTestNodes() {
        if (this.boardNode) {
            let children = this.boardNode.children.slice();
            children.forEach(child => {
                if (child.name.startsWith("Test_")) {
                    child.removeFromParent();
                }
            });
        }
        
    }

    // 切换到父节点添加模式（如果Layout问题仍然存在）
    public useParentNodeMode() {
     
        // 重新定义添加方法
        this.addPlayerNodeSafely = this.addToParentNode;
    }

    // 重新启用Layout（如果需要）
    public reEnableLayout() {
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法重新启用Layout！");
            return;
        }

        let layout = this.boardNode.getComponent(cc.Layout);
        if (layout) {
            layout.enabled = true;

        }
    }

    // 永久禁用Layout
    public disableLayout() {
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法禁用Layout！");
            return;
        }

        let layout = this.boardNode.getComponent(cc.Layout);
        if (layout) {
            layout.enabled = false;

        }
    }

    // 自定义偏移量（如果需要调整位置）
    private customOffsetX: number = 0;
    private customOffsetY: number = -16; // 恢复原来的值，保持点击生成位置正确

    // 设置自定义偏移量
    public setCustomOffset(offsetX: number, offsetY: number) {
        this.customOffsetX = offsetX;
        this.customOffsetY = offsetY;
      
    }

    // 获取当前偏移量
    public getCurrentOffset(): {x: number, y: number} {
        return {x: this.customOffsetX, y: this.customOffsetY};
    }

    // 测试不同偏移量
    public testWithOffset(x: number, y: number, offsetX: number, offsetY: number) {
       

        // 临时保存当前偏移
        let originalOffsetX = this.customOffsetX;
        let originalOffsetY = this.customOffsetY;

        // 设置测试偏移
        this.setCustomOffset(offsetX, offsetY);

        // 执行测试
        this.simpleTest(x, y);

        // 恢复原偏移
        this.setCustomOffset(originalOffsetX, originalOffsetY);
    }

    // 测试头像显示功能
    public testAvatarDisplay(x: number, y: number) {
     

        if (!this.isValidCoordinate(x, y)) {
            console.error("❌ 无效坐标");
            return;
        }

        if (this.gridData[x][y].hasPlayer) {
            console.warn("⚠️ 该位置已有玩家");
            return;
        }

        // 直接调用放置玩家方法
        this.placePlayerOnGrid(x, y);

        // 延迟检查结果
        this.scheduleOnce(() => {
            let gridData = this.gridData[x][y];
            if (gridData.playerNode) {
               

                // 检查PlayerGameController
                let controller = gridData.playerNode.getComponent("PlayerGameController");
                if (controller && controller.avatar) {
                 

                    let sprite = controller.avatar.getComponent(cc.Sprite);
                    if (sprite && sprite.spriteFrame) {
                       
                    } else {
                        console.warn("⚠️ 头像SpriteFrame不存在");
                    }
                } else {
                    console.warn("⚠️ PlayerGameController或avatar节点不存在");
                }
            } else {
                console.error("❌ 玩家节点创建失败");
            }
        }, 3.0);
    }

    // 调试预制体结构
    public debugPrefabStructure() {
        

        if (!this.playerGamePrefab) {
            console.error("❌ playerGamePrefab为null");
            return;
        }

        // 实例化一个临时节点来检查结构
        let tempNode = cc.instantiate(this.playerGamePrefab);

       

        // 检查组件
        let controller = tempNode.getComponent("PlayerGameController");
        if (controller) {
           
            if (controller.avatar) {
               

                let sprite = controller.avatar.getComponent(cc.Sprite);
               
            } else {
                console.error("❌ avatar节点不存在");
            }
        } else {
            console.error("❌ 找不到PlayerGameController组件");
        }

        // 列出所有子节点
      
        this.logNodeHierarchy(tempNode, 0);

        // 清理临时节点
        tempNode.destroy();
    }

    // 递归打印节点层级
    private logNodeHierarchy(node: cc.Node, depth: number) {
        let indent = "  ".repeat(depth);
     

        for (let child of node.children) {
            this.logNodeHierarchy(child, depth + 1);
        }
    }

    // 异步加载头像
    private loadAvatarAsync(avatarNode: cc.Node, url: string, onComplete: () => void) {
       
        if (!avatarNode) {
            console.error("❌ avatar节点为null");
            onComplete();
            return;
        }

        let avatarSprite = avatarNode.getComponent(cc.Sprite);
        if (!avatarSprite) {
            console.warn("⚠️ avatar节点没有Sprite组件，正在添加...");
            avatarSprite = avatarNode.addComponent(cc.Sprite);
        }

        if (!url || url === '') {
            console.warn("⚠️ URL为空，设置备用头像");
            this.setFallbackAvatar(avatarNode, 0, 0);
            onComplete();
            return;
        }

        // 根据URL判断文件扩展名
        let ext = '.png';
        if (url.toLowerCase().includes('.jpg') || url.toLowerCase().includes('.jpeg')) {
            ext = '.jpg';
        } else if (url.toLowerCase().includes('.png')) {
            ext = '.png';
        }

       

        cc.assetManager.loadRemote(url, { ext: ext }, (err, texture: cc.Texture2D) => {
            if (err) {
                console.error(`❌ 头像加载失败: ${err.message || err}`);
                console.error(`❌ 失败的URL: ${url}`);

                // 设置备用头像
                this.setFallbackAvatar(avatarNode, 0, 0);
                onComplete();
                return;
            }

            

            texture.setPremultiplyAlpha(true);
            texture.packable = false;
            avatarSprite.spriteFrame = new cc.SpriteFrame(texture);

            // 确保节点可见
            avatarNode.active = true;
            avatarNode.opacity = 255;

          
            onComplete();
        });
    }

    // 异步直接设置头像（当找不到PlayerGameController时）
    private tryDirectAvatarSetupAsync(playerNode: cc.Node, x: number, y: number, onComplete: () => void) {
     

        // 查找名为"avatar"的子节点
        let avatarNode = playerNode.getChildByName("avatar");
        if (avatarNode) {
           
            this.setFallbackAvatar(avatarNode, x, y);
            onComplete();
        } else {
            console.warn("⚠️ 未找到avatar子节点");

            // 列出所有子节点名称
           
           
            onComplete();
        }
    }

    /**
     * 显示玩家游戏加减分效果
     * @param userId 用户ID
     * @param score 分数变化（正数为加分，负数为减分）
     */
    showPlayerGameScore(userId: string, score: number) {
        const currentUserId = this.getCurrentUserId();
        let foundPlayer = false;

        // 1. 如果是当前用户，查找自己的玩家节点（存储在gridData中）
        if (userId === currentUserId) {
            foundPlayer = this.showScoreForCurrentUser(score);
        } else {
            // 2. 如果是其他用户，查找对应的玩家头像节点
            foundPlayer = this.showScoreForOtherUser(userId, score);
        }

        if (!foundPlayer) {
            console.warn(`未找到用户 ${userId} 的头像节点来显示分数效果`);
        }
    }

    /**
     * 获取当前用户ID
     */
    private getCurrentUserId(): string {
        return GlobalBean.GetInstance().loginData?.userInfo?.userId || "";
    }

    /**
     * 为当前用户显示分数效果
     */
    private showScoreForCurrentUser(score: number): boolean {
        for (let x = 0; x < this.BOARD_SIZE; x++) {
            for (let y = 0; y < this.BOARD_SIZE; y++) {
                let gridData = this.gridData[x][y];
                if (gridData.hasPlayer && gridData.playerNode) {
                    let playerController = gridData.playerNode.getComponent("PlayerGameController") ||
                                         gridData.playerNode.getComponent("PlayerGameController ");

                    if (playerController) {
                        this.showScoreOnPlayerController(playerController, score);
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * 为其他用户显示分数效果
     */
    private showScoreForOtherUser(userId: string, score: number): boolean {
        if (!this.boardNode) {
            return false;
        }

        // 遍历棋盘上的所有玩家头像节点
        // 由于目前没有在节点上存储userId，我们需要通过其他方式匹配
        // 临时方案：根据最近的操作位置来匹配
        return this.findPlayerNodeByRecentAction(userId, score);
    }

    /**
     * 根据userId查找对应的玩家节点
     */
    private findPlayerNodeByRecentAction(userId: string, score: number): boolean {
        if (!this.boardNode) {
            console.warn(`棋盘节点不存在，无法查找用户 ${userId} 的头像`);
            return false;
        }

       

        // 遍历棋盘上的所有玩家头像节点，根据存储的userId精确匹配
        for (let i = 0; i < this.boardNode.children.length; i++) {
            const child = this.boardNode.children[i];

            // 尝试多种方式获取PlayerGameController组件
            let playerController = child.getComponent("PlayerGameController");
            if (!playerController) {
                playerController = child.getComponent("PlayerGameController ");  // 注意末尾有空格
            }
            if (!playerController) {
                // 尝试通过类名获取
                const components = child.getComponents(cc.Component);
                playerController = components.find(comp =>
                    comp.constructor.name === 'PlayerGameController' ||
                    comp.constructor.name === 'PlayerGameController '
                );
            }

            const storedUserId = child['userId'];

          

            // 先输出组件列表，帮助诊断问题
            if (storedUserId && (storedUserId === userId || i < 5)) {  // 为前5个节点或匹配的节点输出组件列表
                const allComponents = child.getComponents(cc.Component);
               
            }

            if (storedUserId === userId) {
                if (playerController) {
                    // 找到匹配的用户ID和组件，显示分数效果
                    
                    this.showScoreOnPlayerController(playerController, score);
                    return true;
                } else {
                    // 找到匹配的用户ID但没有组件
                    console.warn(`⚠️ 找到用户 ${userId} 的节点但没有PlayerGameController组件`);
                    return false;  // 找到节点但没有组件，返回false
                }
            }
        }

        console.warn(`❌ 未找到用户 ${userId} 的头像节点`);
        return false;
    }

    /**
     * 在PlayerController上显示分数效果
     */
    private showScoreOnPlayerController(playerController: any, score: number) {
        // 临时提升节点层级，避免被其他头像遮挡
        const playerNode = playerController.node;
        const originalSiblingIndex = playerNode.getSiblingIndex();

        // 将节点移到最上层
        playerNode.setSiblingIndex(-1);

        if (score > 0) {
            playerController.showAddScore(score);
        } else if (score < 0) {
            playerController.showSubScore(Math.abs(score));
        }

        // 延迟恢复原始层级（等分数动画播放完成）
        this.scheduleOnce(() => {
            if (playerNode && playerNode.isValid) {
                playerNode.setSiblingIndex(originalSiblingIndex);
            }
        }, 2.0); // 2秒后恢复，根据分数动画时长调整
    }

    /**
     * 显示玩家游戏减分效果
     * @param userId 用户ID
     * @param subScore 减分数值
     */
    showPlayerGameSubScore(userId: string, subScore: number) {
     

        let foundPlayer = false;

        // 遍历所有格子，查找玩家节点
        for (let x = 0; x < this.BOARD_SIZE; x++) {
            for (let y = 0; y < this.BOARD_SIZE; y++) {
                let gridData = this.gridData[x][y];
                if (gridData.hasPlayer && gridData.playerNode) {
                    let playerController = gridData.playerNode.getComponent("PlayerGameController") ||
                                         gridData.playerNode.getComponent("PlayerGameController ");

                    if (playerController) {
                        playerController.showSubScore(subScore);
                        foundPlayer = true;
                       
                        break;
                    }
                }
            }
            if (foundPlayer) break;
        }

        if (!foundPlayer) {
            console.warn(`未找到玩家节点来显示减分效果: userId=${userId}`);
        }
    }

    /**
     * 重置游戏场景（游戏开始时调用）
     * 清除数字、炸弹、标记预制体，重新显示所有小格子
     */
    public resetGameScene() {
  

        if (!this.boardNode) {
            console.error("❌ 棋盘节点不存在，无法重置");
            return;
        }

       

        // 列出所有子节点
        for (let i = 0; i < this.boardNode.children.length; i++) {
            const child = this.boardNode.children[i];
            
        }

        // 清除所有游戏元素（数字、炸弹、标记等）
        
        this.clearAllGameElements();

        // 显示所有小格子
     
        this.showAllGrids();

        // 重新初始化棋盘数据
       
        this.reinitializeBoardData();

     

        // 列出所有子节点
        for (let i = 0; i < this.boardNode.children.length; i++) {
            const child = this.boardNode.children[i];
            
        }

     
    }

    /**
     * 测试重置功能（可以在浏览器控制台手动调用）
     */
    public testReset() {
      
        this.resetGameScene();
    }

    /**
     * 清除所有游戏元素（数字、炸弹、标记、玩家头像等），但保留小格子
     */
    private clearAllGameElements() {
        if (!this.boardNode) {
            return;
        }

        const childrenToRemove: cc.Node[] = [];
        const totalChildren = this.boardNode.children.length;

      
        // 遍历棋盘的所有子节点
        for (let i = 0; i < this.boardNode.children.length; i++) {
            const child = this.boardNode.children[i];
            const nodeName = child.name;

            

            // 检查是否是需要清除的游戏元素（不包括小格子）
            if (this.isGameElement(child, nodeName)) {
                childrenToRemove.push(child);
               
            } else {
             
            }
        }

        // 移除找到的游戏元素
        childrenToRemove.forEach((child, index) => {
           
            child.removeFromParent();
        });

       

        // 暂时禁用强制清理，避免误删小格子
        // this.forceCleanNonGridNodes();
    }

    /**
     * 强制清理所有游戏预制体（除了Grid_开头的节点和分数控制器）
     */
    private forceCleanNonGridNodes() {
        if (!this.boardNode) {
            return;
        }

        const childrenToRemove: cc.Node[] = [];

       

        // 再次遍历，强制清除所有游戏预制体
        for (let i = 0; i < this.boardNode.children.length; i++) {
            const child = this.boardNode.children[i];
            const nodeName = child.name;

            // 保留条件：
            // 1. Grid_开头的节点（小格子）
            // 2. 包含Score的节点（分数控制器）
            // 3. UI相关节点
            const shouldKeep = nodeName.startsWith("Grid_") ||
                             nodeName.includes("Score") ||
                             nodeName.includes("score") ||
                             nodeName.includes("UI") ||
                             nodeName.includes("ui") ||
                             nodeName.includes("Canvas") ||
                             nodeName.includes("Background");

            if (!shouldKeep) {
                childrenToRemove.push(child);
               
        }

        // 移除找到的节点
        childrenToRemove.forEach(child => {
            child.removeFromParent();
        });

    
    }
    }
    /**
     * 判断节点是否是游戏元素（需要清除的），小格子和分数控制器不会被清除
     */
    private isGameElement(node: cc.Node, nodeName: string): boolean {
        

        //  绝对不清除的节点（小格子）
        if (nodeName.startsWith("Grid_") || nodeName === "block") {
          
            return false;
        }

        //  分数控制器不清除
        if (nodeName.includes("Score") || nodeName.includes("score")) {
            
            return false;
        }

        //  UI相关节点不清除
        if (nodeName.includes("UI") || nodeName.includes("ui")) {
            
            return false;
        }

        // 🗑️明确需要清除的游戏预制体

        // 炸弹预制体
        if (nodeName === "Boom") {
           
            return true;
        }

        // 数字预制体（Boom1, Boom2, Boom3 等）
        if (nodeName.match(/^Boom\d+$/)) {
            
            return true;
        }

        // 临时数字节点（NeighborMines_1, NeighborMines_2 等）
        if (nodeName.match(/^NeighborMines_\d+$/)) {
          
            return true;
        }

        // 测试节点（Test_x_y 格式）
        if (nodeName.match(/^Test_\d+_\d+$/)) {
          
            return true;
        }

        // 玩家预制体（通过组件判断）
        if (node.getComponent("PlayerGameController")) {
            
            return true;
        }

        // 标记预制体
        if (nodeName.includes("Flag") || nodeName.includes("Mark") || nodeName.includes("flag") ||
            nodeName === "Biaoji" || nodeName.includes("Biaoji")) {
            
            return true;
        }

        // 玩家头像预制体
        if (nodeName.includes("Player") || nodeName.includes("Avatar")) {
            
            return true;
        }

        //  默认保留未知节点（保守策略）
       
        return false;
    }

    /**
     * 显示所有小格子（第二把游戏开始时恢复被隐藏的小格子）
     */
    private showAllGrids() {
        if (!this.boardNode) {
            return;
        }

        let totalGrids = 0;
        let restoredGrids = 0;
        let alreadyVisibleGrids = 0;

      
        // 遍历棋盘的所有子节点，找到小格子并显示
        for (let i = 0; i < this.boardNode.children.length; i++) {
            const child = this.boardNode.children[i];

            // 如果是小格子节点
            if (child.name.startsWith("Grid_") || child.name === "block") {
                totalGrids++;

                // 记录恢复前的状态
                const wasHidden = !child.active || child.opacity < 255 || child.scaleX < 1 || child.scaleY < 1;

                if (wasHidden) {
                   
                    restoredGrids++;
                } else {
                    alreadyVisibleGrids++;
                }

                // 停止所有可能正在进行的动画
                child.stopAllActions();

                // 恢复显示状态
                child.active = true;
                child.opacity = 255;
                child.scaleX = 1;
                child.scaleY = 1;

                // 确保格子可以交互
                const button = child.getComponent(cc.Button);
                if (button) {
                    button.enabled = true;
                }
            }
        }

    }

    /**
     * 隐藏指定位置的小格子（点击时调用）
     */
    public hideGridAt(x: number, y: number) {
        if (!this.isValidCoordinate(x, y)) {
            console.warn(`隐藏格子失败：坐标(${x}, ${y})无效`);
            return;
        }

        // 获取格子节点
        const gridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (gridNode) {
            // 使用动画隐藏格子
            cc.tween(gridNode)
                .to(0.3, { opacity: 0, scaleX: 0, scaleY: 0 }, { easing: 'sineIn' })
                .call(() => {
                    gridNode.active = false;
                })
                .start();
        }
    }

    /**
     * 重新初始化棋盘数据
     */
    private reinitializeBoardData() {
        // 重置gridData中的玩家状态
        for (let x = 0; x < this.BOARD_SIZE; x++) {
            for (let y = 0; y < this.BOARD_SIZE; y++) {
                if (this.gridData[x][y]) {
                    this.gridData[x][y].hasPlayer = false;
                    this.gridData[x][y].playerNode = null;
                }
            }
        }

        // 清除坐标历史记录
        this.clearCoordinateHistory();

      
    }

    /**
     * 清理所有玩家预制体（新回合开始时调用）
     * 包括自己的头像和其他玩家的头像
     */
    public clearAllPlayerNodes() {


        if (!this.boardNode) {
            console.warn("棋盘节点不存在，无法清理");
            return;
        }

        let totalCleared = 0;

        // 方法1: 清理存储在gridData中的玩家节点（自己的头像）
        for (let x = 0; x < this.BOARD_SIZE; x++) {
            for (let y = 0; y < this.BOARD_SIZE; y++) {
                if (this.gridData[x][y].hasPlayer && this.gridData[x][y].playerNode) {
                    // 移除玩家节点
                    this.gridData[x][y].playerNode.removeFromParent();
                    this.gridData[x][y].playerNode = null;
                    this.gridData[x][y].hasPlayer = false;
                    totalCleared++;
                  
                }
            }
        }

        // 方法2: 清理棋盘上所有的玩家预制体节点（包括其他玩家的头像）
        const childrenToRemove: cc.Node[] = [];

        // 遍历棋盘的所有子节点
        for (let i = 0; i < this.boardNode.children.length; i++) {
            const child = this.boardNode.children[i];

            // 检查是否是玩家预制体（通过组件判断）
            const playerController = child.getComponent(PlayerGameController);
            if (playerController) {
                childrenToRemove.push(child);
                totalCleared++;
              
            }
        }

        // 移除找到的玩家预制体
        childrenToRemove.forEach(child => {
            child.removeFromParent();
           
        });

    
    }

    /**
     * 在指定位置显示其他玩家的操作（参考自己头像的生成逻辑）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param actions 该位置的其他玩家操作列表
     */
    public displayOtherPlayersAtPosition(x: number, y: number, actions: any[]) {
        if (!this.isValidCoordinate(x, y) || !actions || actions.length === 0) {
            console.warn(`无效参数: (${x}, ${y}), actions: ${actions?.length || 0}`);
            return;
        }

        
        // 检查该位置是否已经有自己的头像
        if (this.gridData[x][y].hasPlayer) {
       
            // 只有当真的有其他玩家时，才调整自己的头像位置
            if (actions.length > 0) {
                // 如果已有自己的头像且有其他玩家，需要使用多人布局策略
                this.addOtherPlayersToExistingGrid(x, y, actions);
            } else {
               
            }
        } else {
          
            // 如果没有自己的头像，直接添加其他玩家头像
            this.addOtherPlayersToEmptyGrid(x, y, actions);
        }
    }

    /**
     * 在已有自己头像的格子上添加其他玩家头像，并调整自己的头像位置和缩放
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param actions 其他玩家操作列表
     */
    private addOtherPlayersToExistingGrid(x: number, y: number, actions: any[]) {
        // 总玩家数 = 自己(1) + 其他玩家数量
        const totalPlayers = 1 + actions.length;
        const positions = this.getPlayerPositions(totalPlayers);

       
        // 第一步：调整自己的头像位置和缩放
        // 注意：如果自己的头像是通过点击生成的，位置是正确的，应该调整
        // 如果是通过后端消息生成的，也应该参与多人布局
        const myPosition = positions[0]; // 第一个位置是自己的
        this.adjustMyAvatarPosition(x, y, myPosition, actions);

        // 第二步：从第二个位置开始放置其他玩家
        for (let i = 0; i < actions.length; i++) {
            const action = actions[i];
            const position = positions[i + 1]; // 跳过第一个位置（自己的位置）

         
            // 使用棋盘坐标系创建其他玩家头像
            this.createOtherPlayerAtBoardPosition(x, y, action, position, totalPlayers);
        }
    }

    /**
     * 调整自己的头像位置和缩放（当多人在同一格子时）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param position 新的位置和缩放信息
     * @param actions 其他玩家操作列表
     */
    private adjustMyAvatarPosition(x: number, y: number, position: {x: number, y: number, scale: number}, actions: any[]) {
      
        // 查找自己的头像节点
        if (!this.gridData[x][y].hasPlayer || !this.gridData[x][y].playerNode) {
            console.warn(`在位置(${x}, ${y})找不到自己的头像节点`);
            return;
        }

        const myPlayerNode = this.gridData[x][y].playerNode;

        // 计算该格子的总人数（自己 + 其他玩家）
        const totalPlayers = 1 + (actions ? actions.length : 0);

        // 根据总人数计算基础位置
        const basePosition = this.calculateBasePositionByPlayerCount(x, y, totalPlayers);

        // 计算新的最终位置
        const newPosition = cc.v2(
            basePosition.x + position.x,
            basePosition.y + position.y
        );

        // 播放平滑移动和缩小动画（多人格子情况）
        this.playAvatarAdjustAnimation(myPlayerNode, newPosition, position.scale);

      
    }

    /**
     * 在空格子上添加其他玩家头像
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param actions 其他玩家操作列表
     */
    private addOtherPlayersToEmptyGrid(x: number, y: number, actions: any[]) {
        const totalPlayers = actions.length; // 空格子上只有其他玩家
        const positions = this.getPlayerPositions(totalPlayers);

        for (let i = 0; i < actions.length; i++) {
            const action = actions[i];
            const position = positions[i];

          

            // 使用棋盘坐标系创建其他玩家头像
            this.createOtherPlayerAtBoardPosition(x, y, action, position, totalPlayers);
        }
    }

    /**
     * 在棋盘坐标系中创建其他玩家头像（参考自己头像的生成逻辑）
     * @param gridX 格子x坐标
     * @param gridY 格子y坐标
     * @param action 玩家操作数据
     * @param relativePosition 相对于格子中心的位置和缩放
     * @param totalPlayers 该格子的总人数
     */
    private createOtherPlayerAtBoardPosition(gridX: number, gridY: number, action: any, relativePosition: {x: number, y: number, scale: number}, totalPlayers: number) {
        if (!this.playerGamePrefab) {
            console.error("playerGamePrefab 预制体未设置");
            return;
        }

        if (!this.boardNode) {
            console.error("棋盘节点未设置");
            return;
        }

        // 创建玩家预制体实例
        const playerNode = cc.instantiate(this.playerGamePrefab);
       

        // 根据总人数计算基础位置（统一逻辑）
        const basePosition = this.calculateBasePositionByPlayerCount(gridX, gridY, totalPlayers);

        // 添加相对偏移
        const finalPosition = cc.v2(
            basePosition.x + relativePosition.x,
            basePosition.y + relativePosition.y
        );

        playerNode.setPosition(finalPosition);
        playerNode.setScale(relativePosition.scale);

       

        // 先隐藏节点，等头像加载完成后再显示
        playerNode.active = false;

        // 安全地添加到棋盘节点（参考自己头像的添加逻辑）
        this.addPlayerNodeSafely(playerNode);

        // 设置其他玩家的头像和数据
        this.setupOtherPlayerData(playerNode, action, () => {
            // 头像加载完成的回调
            if (totalPlayers === 1) {
                // 单人格子：播放生成动画
                this.playAvatarSpawnAnimation(playerNode);
            } else {
                // 多人格子：直接显示（其他人是新生成的，不需要动画）
                playerNode.active = true;
            }
           
        });
    }

    /**
     * 设置其他玩家的数据（参考自己头像的设置逻辑）
     * @param playerNode 玩家节点
     * @param action 玩家操作数据
     * @param onComplete 完成回调
     */
    private setupOtherPlayerData(playerNode: cc.Node, action: any, onComplete: () => void) {
        try {
           

            const playerController = playerNode.getComponent(PlayerGameController);
            if (!playerController) {
                console.error("❌ 找不到PlayerGameController组件");
                return;
            }

          

            // 从GlobalBean中获取真实的玩家数据
            const realUserData = this.getRealUserData(action.userId);
            if (!realUserData) {
                console.warn(`找不到用户 ${action.userId} 的真实数据`);
                return;
            }

           

            // 在节点上存储userId信息，用于后续分数显示匹配
            playerNode['userId'] = action.userId;
           

            // 使用延迟设置，参考自己头像的设置逻辑
            this.scheduleOnce(() => {
                if (typeof playerController.setData === 'function') {
                    playerController.setData(realUserData);
                }

                // 根据操作类型设置旗子显示
                const withFlag = (action.action === 2); // action=2表示标记操作，显示旗子
                if (playerController.flagNode) {
                    playerController.flagNode.active = withFlag;

                }

                // 调用完成回调
                if (onComplete) {
                    onComplete();
                }
            }, 0.1);

        } catch (error) {
            console.warn(`设置其他玩家数据时出错: ${error}`);
        }
    }

    /**
     * 根据玩家数量获取布局位置
     * @param playerCount 玩家数量
     * @returns 位置数组 {x: number, y: number, scale: number}[]
     */
    private getPlayerPositions(playerCount: number): {x: number, y: number, scale: number}[] {
        switch (playerCount) {
            case 1:
                // 单个玩家，居中显示，正常大小
                return [{x: 0, y: 0, scale: 1.0}];

            case 2:
                // 两个玩家，左右分布，缩放0.5
                return [
                    {x: -22, y: -8, scale: 0.5}, // 左
                    {x: 22, y: -8, scale: 0.5}   // 右
                ];

            case 3:
                // 三个玩家，上中下分布，缩放0.5
                return [
                    {x: 0, y: 12, scale: 0.5},    // 上
                    {x: -23, y: -27, scale: 0.5}, // 左下
                    {x: 23, y: -27, scale: 0.5}   // 右下
                ];

            case 4:
                // 四个玩家，四角分布，缩放0.5
                return [
                    {x: -22, y: 12, scale: 0.5},  // 左上
                    {x: 22, y: 12, scale: 0.5},   // 右上
                    {x: -22, y: -30, scale: 0.5}, // 左下
                    {x: 22, y: -30, scale: 0.5}   // 右下
                ];

            default:
                // 超过4个玩家，只显示前4个
                console.warn(`玩家数量过多: ${playerCount}，只显示前4个`);
                return this.getPlayerPositions(4);
        }
    }

    /**
     * 获取指定位置的格子节点
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 格子节点或null
     */
    private getGridNode(x: number, y: number): cc.Node | null {
        if (!this.boardNode || !this.isValidCoordinate(x, y)) {
            return null;
        }

        // 计算在棋盘子节点中的索引 (8x8棋盘，从左到右，从上到下)
        const index = y * this.BOARD_SIZE + x;

        if (index >= 0 && index < this.boardNode.children.length) {
            return this.boardNode.children[index];
        }

        return null;
    }

    /**
     * 在指定位置创建玩家预制体节点
     * @param gridNode 格子节点
     * @param action 玩家操作数据
     * @param position 相对位置和缩放
     */
    private createPlayerNodeAtPosition(gridNode: cc.Node, action: any, position: {x: number, y: number, scale: number}) {
        if (!this.playerGamePrefab) {
            console.error("playerGamePrefab 预制体未设置");
            return;
        }

        // 创建玩家预制体实例
        const playerNode = cc.instantiate(this.playerGamePrefab);
      

        // 检查预制体上的组件
        const components = playerNode.getComponents(cc.Component);
     
        components.forEach((comp, index) => {
            
        });

        // 设置位置和缩放
        playerNode.setPosition(position.x, position.y);
        playerNode.setScale(position.scale);
       

        // 添加到格子节点
        gridNode.addChild(playerNode);

        // 设置玩家数据
        this.setupPlayerNodeData(playerNode, action);

        
    }

    /**
     * 设置玩家节点数据
     * @param playerNode 玩家节点
     * @param action 玩家操作数据
     */
    private setupPlayerNodeData(playerNode: cc.Node, action: any) {
        try {
           

            const playerController = playerNode.getComponent(PlayerGameController);
            if (!playerController) {
                console.error("❌ 找不到PlayerGameController组件");
             
                const allComponents = playerNode.getComponents(cc.Component);
                allComponents.forEach((comp, index) => {
                   
                });
                return;
            }

         
            // 从GlobalBean中获取真实的玩家数据
            const realUserData = this.getRealUserData(action.userId);
            if (!realUserData) {
                console.warn(`找不到用户 ${action.userId} 的真实数据`);
                return;
            }

           

            if (typeof playerController.setData === 'function') {
                playerController.setData(realUserData);
            }

            // 根据操作类型设置旗子显示
            const withFlag = (action.action === 2); // action=2表示标记操作，显示旗子
            if (playerController.flagNode) {
                playerController.flagNode.active = withFlag;
               
            } else {
                console.warn("找不到flagNode节点");
            }

        } catch (error) {
            console.warn(`设置玩家节点数据时出错: ${error}`);
        }
    }

    /**
     * 从GlobalBean中获取真实的用户数据
     * @param userId 用户ID
     * @returns RoomUser 或 null
     */
    private getRealUserData(userId: string): RoomUser | null {
        try {
            if (!GlobalBean.GetInstance().noticeStartGame || !GlobalBean.GetInstance().noticeStartGame.users) {
                console.warn("没有游戏数据，无法获取用户信息");
                return null;
            }

            const users = GlobalBean.GetInstance().noticeStartGame.users;
            const user = users.find((u: RoomUser) => u.userId === userId);

            if (user) {
                
                return user;
            } else {
                console.warn(`未找到用户 ${userId} 的数据`);
                return null;
            }
        } catch (error) {
            console.error(`获取用户数据时出错: ${error}`);
            return null;
        }
    }

    /**
     * 在指定位置的玩家节点上显示分数
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param score 分数
     * @param showPlusOne 是否显示+1（先手奖励）
     */
    public showScoreOnPlayerNode(x: number, y: number, score: number, showPlusOne: boolean) {
       
        if (!this.isValidCoordinate(x, y)) {
            console.warn(`无效坐标: (${x}, ${y})`);
            return;
        }

        // 查找该位置的玩家节点
        const playerNode = this.findPlayerNodeAtPosition(x, y);
        if (!playerNode) {
            console.warn(`在位置(${x}, ${y})找不到玩家节点`);
            return;
        }

        // 获取PlayerGameController组件
        const playerController = playerNode.getComponent(PlayerGameController);
        if (!playerController) {
            console.warn("找不到PlayerGameController组件");
            return;
        }

        // 显示分数动画
        if (showPlusOne) {
            // 先显示+1，再显示本回合得分
            this.showScoreAnimationOnNode(playerController, 1, () => {
                this.scheduleOnce(() => {
                    this.showScoreAnimationOnNode(playerController, score, null);
                }, 1.0);
            });
        } else {
            // 只显示本回合得分
            this.showScoreAnimationOnNode(playerController, score, null);
        }
    }

    /**
     * 查找指定位置的玩家节点
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 玩家节点或null
     */
    private findPlayerNodeAtPosition(x: number, y: number): cc.Node | null {
        // 方法1: 从gridData中查找（自己的头像）
        if (this.gridData[x][y].hasPlayer && this.gridData[x][y].playerNode) {
            return this.gridData[x][y].playerNode;
        }

        // 方法2: 在棋盘上查找其他玩家的头像
        if (!this.boardNode) {
            return null;
        }

        // 计算该位置的世界坐标
        const targetPosition = this.calculateCorrectPosition(x, y);

        // 遍历棋盘上的所有玩家节点，找到最接近目标位置的
        let closestNode: cc.Node | null = null;
        let minDistance = Number.MAX_VALUE;

        for (let i = 0; i < this.boardNode.children.length; i++) {
            const child = this.boardNode.children[i];
            const playerController = child.getComponent(PlayerGameController);

            if (playerController) {
                const distance = cc.Vec2.distance(child.getPosition(), targetPosition);
                if (distance < minDistance && distance < 50) { // 50像素的容差
                    minDistance = distance;
                    closestNode = child;
                }
            }
        }

        return closestNode;
    }

    /**
     * 在节点上显示分数动画
     * @param playerController 玩家控制器
     * @param score 分数
     * @param onComplete 完成回调
     */
    private showScoreAnimationOnNode(playerController: any, score: number, onComplete: (() => void) | null) {
      

        // TODO: 实现在player_game_pfb上显示分数动画的逻辑
        // 这里需要根据PlayerGameController的具体实现来显示分数

        if (onComplete) {
            this.scheduleOnce(onComplete, 1.0);
        }
    }

    /**
     * 让指定位置的所有头像消失（参考回合结束时的清理逻辑）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param onComplete 完成回调
     */
    public hideAvatarsAtPosition(x: number, y: number, onComplete: () => void) {
        

        if (!this.boardNode) {
            console.warn("棋盘节点不存在，无法清理头像");
            onComplete();
            return;
        }

        // 收集所有头像节点（参考clearAllPlayerNodes的逻辑）
        const avatarNodes: cc.Node[] = [];

        // 方法1: 收集存储在gridData中的玩家节点（自己的头像）
        for (let gx = 0; gx < this.BOARD_SIZE; gx++) {
            for (let gy = 0; gy < this.BOARD_SIZE; gy++) {
                if (this.gridData[gx][gy].hasPlayer && this.gridData[gx][gy].playerNode) {
                    avatarNodes.push(this.gridData[gx][gy].playerNode);
                   
                }
            }
        }

        // 方法2: 收集棋盘上所有的玩家预制体节点（包括其他玩家的头像）
        for (let i = 0; i < this.boardNode.children.length; i++) {
            const child = this.boardNode.children[i];

            // 检查是否是玩家预制体（通过组件判断，参考clearAllPlayerNodes）
            const playerController = child.getComponent(PlayerGameController);
            if (playerController) {
                // 避免重复添加（可能已经在方法1中添加过）
                if (!avatarNodes.includes(child)) {
                    avatarNodes.push(child);
                  
                }
            }
        }

      

        if (avatarNodes.length === 0) {
            // 没有头像需要消失
          
            onComplete();
            return;
        }

        let completedCount = 0;
        const totalCount = avatarNodes.length;

        // 为每个头像播放消失动画
        avatarNodes.forEach((avatarNode, index) => {
            
            // 使用cc.Tween播放消失动画
            cc.tween(avatarNode)
                .to(0.3, { opacity: 0, scaleX: 0.5, scaleY: 0.5 }, { easing: 'sineIn' })
                .call(() => {
                    // 动画完成后移除节点
                    avatarNode.removeFromParent();
                    completedCount++;
                   

                    // 所有头像都消失完成后，执行回调
                    if (completedCount >= totalCount) {
                       
                        // 清除所有自己头像的引用（参考clearAllPlayerNodes）
                        this.clearAllMyAvatarReferences();

                        onComplete();
                    }
                })
                .start();
        });
    }

    /**
     * 清除所有自己头像的引用（参考clearAllPlayerNodes的逻辑）
     */
    private clearAllMyAvatarReferences() {
       
        for (let x = 0; x < this.BOARD_SIZE; x++) {
            for (let y = 0; y < this.BOARD_SIZE; y++) {
                if (this.gridData[x][y].hasPlayer) {
                    this.gridData[x][y].hasPlayer = false;
                    this.gridData[x][y].playerNode = null;
                  
                }
            }
        }
    }

    /**
     * 隐藏指定位置的格子（不销毁，以便重置时可以重新显示）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param immediate 是否立即隐藏（不播放动画）
     */
    public removeGridAt(x: number, y: number, immediate: boolean = false) {
        if (!this.isValidCoordinate(x, y)) {
            console.warn(`隐藏格子失败：坐标(${x}, ${y})无效`);
            return;
        }

        // 获取格子节点
        const gridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (gridNode) {
            if (immediate) {
                // 立即隐藏，不播放动画
                gridNode.active = false;
            } else {
                // 使用cc.Tween播放消失动画后隐藏（不销毁）
                cc.tween(gridNode)
                    .to(0.3, { opacity: 0, scaleX: 0, scaleY: 0 }, { easing: 'sineIn' })
                    .call(() => {
                        gridNode.active = false; // 隐藏而不是销毁
                    })
                    .start();
            }
        }
    }

    /**
     * 在指定位置创建boom预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param isCurrentUser 是否是当前用户点到的雷（可选，默认为true以保持向后兼容）
     */
    public createBoomPrefab(x: number, y: number, isCurrentUser: boolean = true) {


        if (!this.boomPrefab) {
            console.error("boomPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }

        // 实例化您的boom预制体
        const boomNode = cc.instantiate(this.boomPrefab);
        boomNode.name = "Boom";

        // 设置位置（使用新的精确位置计算）
        const position = this.calculatePrefabPosition(x, y);
        boomNode.setPosition(position);

        // 添加到棋盘
        this.addPlayerNodeSafely(boomNode);

        // 播放出现动画
        boomNode.setScale(0);
        cc.tween(boomNode)
            .to(0.3, { scaleX: 1.2, scaleY: 1.2 }, { easing: 'backOut' })
            .to(0.1, { scaleX: 1.0, scaleY: 1.0 })
            .start();

        // 只有当前用户点到雷时才播放棋盘震动效果
        if (isCurrentUser) {
            this.playBoardShakeAnimation();
        }

    }

    /**
     * 在指定位置创建biaoji预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     */
    public createBiaojiPrefab(x: number, y: number) {
       
        if (!this.biaojiPrefab) {
            console.error("biaojiPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }

        // 实例化您的biaoji预制体
        const biaojiNode = cc.instantiate(this.biaojiPrefab);
        biaojiNode.name = "Biaoji";

        // 设置位置（使用新的精确位置计算）
        const position = this.calculatePrefabPosition(x, y);
        biaojiNode.setPosition(position);

        // 添加到棋盘
        this.addPlayerNodeSafely(biaojiNode);

        // 播放出现动画
        biaojiNode.setScale(0);
        cc.tween(biaojiNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();

       
    }

    /**
     * 更新指定位置的neighborMines显示（使用boom数字预制体）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param neighborMines 周围地雷数量
     */
    public updateNeighborMinesDisplay(x: number, y: number, neighborMines: number) {
      

        // 0不需要显示数字
        if (neighborMines === 0) {
          
            return;
        }

        // 直接使用boom数字预制体
        this.createNumberPrefab(x, y, neighborMines);
    }

    /**
     * 创建数字预制体（boom1, boom2, ...）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param number 数字
     */
    private createNumberPrefab(x: number, y: number, number: number) {
       

        // 根据数字选择对应的预制体
        let prefab: cc.Prefab = null;
        switch (number) {
            case 1: prefab = this.boom1Prefab; break;
            case 2: prefab = this.boom2Prefab; break;
            case 3: prefab = this.boom3Prefab; break;
            case 4: prefab = this.boom4Prefab; break;
            case 5: prefab = this.boom5Prefab; break;
            case 6: prefab = this.boom6Prefab; break;
            case 7: prefab = this.boom7Prefab; break;
            case 8: prefab = this.boom8Prefab; break;
            default:
                console.error(`不支持的数字: ${number}`);
                return;
        }

        if (!prefab) {
            console.error(`boom${number}Prefab 预制体未设置，请在编辑器中挂载`);
            return;
        }

        // 实例化数字预制体
        const numberNode = cc.instantiate(prefab);
        numberNode.name = `Boom${number}`;

        // 设置位置（使用新的精确位置计算）
        const position = this.calculatePrefabPosition(x, y);
        numberNode.setPosition(position);

        // 添加到棋盘
        this.addPlayerNodeSafely(numberNode);

        // 播放出现动画
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();

       
    }

    /**
     * 加载数字预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param number 数字
     */
    private loadNumberPrefab(x: number, y: number, number: number) {
        const prefabName = `${number}boom`;
       
     
        this.createTemporaryNumberNode(x, y, number);
    }

    /**
     * 创建临时的数字节点（在预制体加载失败时使用）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param number 数字
     */
    private createTemporaryNumberNode(x: number, y: number, number: number) {
        
        // 创建数字显示节点
        const numberNode = new cc.Node(`NeighborMines_${number}`);
        const label = numberNode.addComponent(cc.Label);

        // 设置数字显示 - 更大的字体和居中对齐
        label.string = number.toString();
        label.fontSize = 48; // 增大字体
        label.node.color = this.getNumberColor(number);
        label.horizontalAlign = cc.Label.HorizontalAlign.CENTER;
        label.verticalAlign = cc.Label.VerticalAlign.CENTER;

        // 设置节点大小，确保居中
        numberNode.setContentSize(this.GRID_SIZE, this.GRID_SIZE);

        // 设置位置 - 使用格子中心位置
        const position = this.calculateCorrectPosition(x, y);
        numberNode.setPosition(position);

        // 添加到棋盘
        this.addPlayerNodeSafely(numberNode);

        // 播放出现动画
        this.playNumberAppearAnimation(numberNode, number);

       
    }

    /**
     * 设置数字节点（用于预制体）
     * @param numberNode 数字节点
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param number 数字
     */
    private setupNumberNode(numberNode: cc.Node, x: number, y: number, number: number) {
       

        // 设置位置 - 使用格子中心位置
        const position = this.calculateCorrectPosition(x, y);
        numberNode.setPosition(position);

        // 添加到棋盘
        this.addPlayerNodeSafely(numberNode);

        // 播放出现动画
        this.playNumberAppearAnimation(numberNode, number);

       
    }

    /**
     * 播放数字出现动画
     * @param numberNode 数字节点
     * @param number 数字
     */
    private playNumberAppearAnimation(numberNode: cc.Node, number: number) {
        // 使用cc.Tween播放数字出现动画
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();

      
    }

    /**
     * 播放格子消失动画（连锁效果）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param neighborMines 周围地雷数量
     */
    public playGridDisappearAnimation(x: number, y: number, neighborMines: number) {
     

        // 先删除格子
        this.removeGridAt(x, y);

        // 延迟0.3秒后显示数字（等格子消失动画完成）
        this.scheduleOnce(() => {
            this.updateNeighborMinesDisplay(x, y, neighborMines);
        }, 0.3);
    }

    /**
     * 根据数字获取颜色
     * @param number 数字
     * @returns 颜色
     */
    private getNumberColor(number: number): cc.Color {
        switch (number) {
            case 1: return cc.Color.BLUE;
            case 2: return cc.Color.GREEN;
            case 3: return cc.Color.RED;
            case 4: return cc.Color.MAGENTA;
            case 5: return cc.Color.YELLOW;
            case 6: return cc.Color.CYAN;
            case 7: return cc.Color.BLACK;
            case 8: return cc.Color.GRAY;
            default: return cc.Color.BLACK;
        }
    }

    /**
     * 播放棋盘震动动画
     */
    private playBoardShakeAnimation() {
        if (!this.boardNode) {
            console.warn("boardNode 未设置，无法播放震动效果");
            return;
        }

        // 保存原始位置
        const originalPosition = this.boardNode.position.clone();

        // 震动参数 - 增强震动效果
        const shakeIntensity = 25; // 震动强度（从10增加到25）
        const shakeDuration = 0.8; // 震动持续时间（从0.5增加到0.8）
        const shakeFrequency = 30; // 震动频率（从20增加到30，更快的震动）

        // 创建更强烈的震动动画，使用递减强度
        let currentIntensity = shakeIntensity;
        const intensityDecay = 0.9; // 强度衰减系数

        const createShakeStep = (intensity: number) => {
            return cc.tween()
                .to(0.02, {
                    x: originalPosition.x + (Math.random() - 0.5) * intensity * 2,
                    y: originalPosition.y + (Math.random() - 0.5) * intensity * 2
                });
        };

        // 创建震动序列，强度逐渐衰减
        let shakeTween = cc.tween(this.boardNode);
        const totalSteps = Math.floor(shakeDuration * shakeFrequency);

        for (let i = 0; i < totalSteps; i++) {
            shakeTween = shakeTween.then(createShakeStep(currentIntensity));
            currentIntensity *= intensityDecay; // 逐渐减弱震动强度
        }

        // 最后恢复到原位置
        shakeTween.to(0.15, {
            x: originalPosition.x,
            y: originalPosition.y
        }, { easing: 'backOut' })
        .start();
    }

    // update (dt) {}
}
