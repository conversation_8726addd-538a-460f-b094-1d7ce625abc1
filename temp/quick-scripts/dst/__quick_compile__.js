
(function () {
var scripts = [{"deps":{"./joui18n-script/LocalizedSprite":1,"./joui18n-script/LocalizedLabel":2,"./assets/meshTools/tools/MeshSdk":3,"./assets/scripts/bean/GameBean":4,"./assets/scripts/game/Chess/HexChessBoardController":5,"./assets/scripts/hall/Level/LevelSelectController":6,"./assets/resources/i18n/zh_HK":7,"./assets/meshTools/Singleton":8,"./assets/scripts/common/GameData":9,"./assets/scripts/level/LevelPageController":10,"./assets/scripts/pfb/InfoItemController":11,"./assets/scripts/start_up/StartUpPageController":12,"./assets/scripts/net/GameServerUrl":13,"./assets/scripts/test/NoticeRoundStartTest":14,"./assets/scripts/util/AudioMgr":15,"./assets/meshTools/BaseSDK":16,"./assets/scripts/ToastController":17,"./assets/scripts/TipsDialogController":18,"./assets/meshTools/tools/MeshSdkApi":19,"./assets/scripts/GlobalManagerController":20,"./assets/scripts/bean/GlobalBean":21,"./assets/scripts/bean/LanguageType":22,"./assets/scripts/common/EventCenter":23,"./assets/scripts/common/GameMgr":24,"./assets/scripts/game/GameScoreController":25,"./assets/meshTools/tools/Publish":26,"./assets/scripts/game/BtnController":27,"./assets/scripts/game/GamePageController":28,"./assets/scripts/common/MineConsole":29,"./assets/scripts/game/Chess/GridController":30,"./assets/scripts/hall/HallCreateRoomController":31,"./assets/scripts/hall/HallJoinRoomController":32,"./assets/scripts/game/Chess/ChessBoardController":33,"./assets/scripts/hall/HallPageController":34,"./assets/scripts/hall/HallParentController":35,"./assets/scripts/hall/InfoDialogController":36,"./assets/scripts/hall/LevelSelectDemo":37,"./assets/scripts/bean/EnumBean":38,"./assets/scripts/game/Chess/SingleChessBoardController":39,"./assets/scripts/game/CongratsDialogController":40,"./assets/scripts/hall/MatchParentController":41,"./assets/scripts/hall/HallCenterLayController":42,"./assets/scripts/hall/TopUpDialogController":43,"./assets/scripts/hall/SettingDialogController":44,"./assets/scripts/hall/Level/LevelSelectPageController":45,"./assets/scripts/hall/HallAutoController":46,"./assets/scripts/hall/Level/LevelItemController":47,"./assets/scripts/hall/Level/LevelSelectExample":48,"./assets/scripts/hall/PlayerLayoutController":49,"./assets/scripts/hall/KickOutDialogController":50,"./assets/scripts/net/HttpManager":51,"./assets/scripts/net/IHttpMsgBody":52,"./assets/scripts/net/MessageId":53,"./assets/scripts/net/HttpUtils":54,"./assets/scripts/net/MessageBaseBean":55,"./assets/scripts/net/WebSocketTool":56,"./assets/scripts/common/GameTools":57,"./assets/scripts/pfb/MatchItemController":58,"./assets/scripts/pfb/PlayerScoreController":59,"./assets/scripts/net/ErrorCode":60,"./assets/scripts/pfb/InfoItemOneController":61,"./assets/scripts/pfb/PlayerGameController ":62,"./assets/scripts/pfb/SeatItemController":63,"./assets/scripts/pfb/CongratsItemController":64,"./assets/scripts/hall/LeaveDialogController":65,"./assets/scripts/start_up/StartUpCenterController":66,"./assets/scripts/util/Dictionary":67,"./assets/scripts/util/LocalStorageManager":68,"./assets/scripts/util/NickNameLabel":69,"./assets/scripts/util/Tools":70,"./assets/scripts/util/AudioManager":71,"./assets/scripts/util/Config":72,"./assets/scripts/hall/Level/ScrollViewHelper":73,"./assets/meshTools/MeshTools":74,"./assets/resources/i18n/en":75,"./assets/resources/i18n/zh_CN":76,"./assets/scripts/util/BlockingQueue":77,"./assets/scripts/net/WebSocketManager":78},"path":"preview-scripts/__qc_index__.js"},{"deps":{},"path":"preview-scripts/joui18n-script/LocalizedSprite.js"},{"deps":{},"path":"preview-scripts/joui18n-script/LocalizedLabel.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/tools/MeshSdk.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/GameBean.js"},{"deps":{"../../bean/GlobalBean":21,"../../pfb/PlayerGameController ":62},"path":"preview-scripts/assets/scripts/game/Chess/HexChessBoardController.js"},{"deps":{"./ScrollViewHelper":73},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectController.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/zh_HK.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/Singleton.js"},{"deps":{"../../meshTools/MeshTools":74,"../../meshTools/Singleton":8,"../net/GameServerUrl":13},"path":"preview-scripts/assets/scripts/common/GameData.js"},{"deps":{"../hall/LeaveDialogController":65,"../util/Tools":70,"../util/Config":72,"../GlobalManagerController":20,"../game/Chess/SingleChessBoardController":39,"../net/WebSocketManager":78,"../net/MessageId":53,"../common/GameMgr":24,"../common/EventCenter":23},"path":"preview-scripts/assets/scripts/level/LevelPageController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/pfb/InfoItemController.js"},{"deps":{"../common/GameMgr":24,"./StartUpCenterController":66},"path":"preview-scripts/assets/scripts/start_up/StartUpPageController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/GameServerUrl.js"},{"deps":{"../common/GameMgr":24,"../common/EventCenter":23,"../net/MessageId":53},"path":"preview-scripts/assets/scripts/test/NoticeRoundStartTest.js"},{"deps":{"./Config":72,"./Dictionary":67},"path":"preview-scripts/assets/scripts/util/AudioMgr.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/BaseSDK.js"},{"deps":{},"path":"preview-scripts/assets/scripts/ToastController.js"},{"deps":{"./util/Config":72,"./util/Tools":70},"path":"preview-scripts/assets/scripts/TipsDialogController.js"},{"deps":{"../MeshTools":74,"../BaseSDK":16,"../../scripts/net/MessageBaseBean":55,"../../scripts/common/GameMgr":24,"../../scripts/common/EventCenter":23,"MeshSdk":3},"path":"preview-scripts/assets/meshTools/tools/MeshSdkApi.js"},{"deps":{"../meshTools/MeshTools":74,"../meshTools/tools/Publish":26,"./bean/EnumBean":38,"./bean/GlobalBean":21,"./bean/LanguageType":22,"./common/EventCenter":23,"./common/GameMgr":24,"./game/GamePageController":28,"./hall/HallPageController":34,"./level/LevelPageController":10,"./hall/TopUpDialogController":43,"./net/ErrorCode":60,"./net/GameServerUrl":13,"./net/MessageBaseBean":55,"./net/MessageId":53,"./net/WebSocketManager":78,"./net/WebSocketTool":56,"./start_up/StartUpPageController":12,"./TipsDialogController":18,"./ToastController":17,"./util/AudioMgr":15,"./util/Config":72},"path":"preview-scripts/assets/scripts/GlobalManagerController.js"},{"deps":{"../../meshTools/Singleton":8,"../hall/HallAutoController":46},"path":"preview-scripts/assets/scripts/bean/GlobalBean.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/LanguageType.js"},{"deps":{"../../meshTools/Singleton":8,"./GameMgr":24},"path":"preview-scripts/assets/scripts/common/EventCenter.js"},{"deps":{"../../meshTools/tools/MeshSdkApi":19,"./EventCenter":23,"./GameData":9,"./GameTools":57,"./MineConsole":29},"path":"preview-scripts/assets/scripts/common/GameMgr.js"},{"deps":{"../bean/GlobalBean":21,"../pfb/PlayerScoreController":59},"path":"preview-scripts/assets/scripts/game/GameScoreController.js"},{"deps":{"../Singleton":8},"path":"preview-scripts/assets/meshTools/tools/Publish.js"},{"deps":{"../util/AudioManager":71,"../util/Config":72,"../util/LocalStorageManager":68},"path":"preview-scripts/assets/scripts/game/BtnController.js"},{"deps":{"../bean/GlobalBean":21,"../hall/LeaveDialogController":65,"../util/AudioManager":71,"../util/Config":72,"../util/Tools":70,"./CongratsDialogController":40,"./GameScoreController":25,"./Chess/ChessBoardController":33,"./Chess/HexChessBoardController":5,"../pfb/PlayerGameController ":62,"../net/WebSocketManager":78,"../net/MessageId":53},"path":"preview-scripts/assets/scripts/game/GamePageController.js"},{"deps":{"../../meshTools/Singleton":8},"path":"preview-scripts/assets/scripts/common/MineConsole.js"},{"deps":{},"path":"preview-scripts/assets/scripts/game/Chess/GridController.js"},{"deps":{"../bean/GlobalBean":21,"../pfb/SeatItemController":63,"../util/Config":72,"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/HallCreateRoomController.js"},{"deps":{"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/HallJoinRoomController.js"},{"deps":{"../../bean/GlobalBean":21,"../../pfb/PlayerGameController ":62},"path":"preview-scripts/assets/scripts/game/Chess/ChessBoardController.js"},{"deps":{"../bean/GlobalBean":21,"../common/GameMgr":24,"../net/MessageId":53,"../net/WebSocketManager":78,"../net/WebSocketTool":56,"../ToastController":17,"../util/AudioManager":71,"./HallParentController":35,"./InfoDialogController":36,"./KickOutDialogController":50,"./LeaveDialogController":65,"./Level/LevelSelectPageController":45,"./MatchParentController":41,"./SettingDialogController":44},"path":"preview-scripts/assets/scripts/hall/HallPageController.js"},{"deps":{"../../meshTools/tools/Publish":26,"../bean/GlobalBean":21,"../common/GameMgr":24,"../net/MessageId":53,"../net/WebSocketManager":78,"../ToastController":17,"../util/Config":72,"../util/Tools":70,"./HallCenterLayController":42},"path":"preview-scripts/assets/scripts/hall/HallParentController.js"},{"deps":{"../util/Config":72,"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/InfoDialogController.js"},{"deps":{"./Level/LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/LevelSelectDemo.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/EnumBean.js"},{"deps":{"../../net/WebSocketManager":78,"../../net/MessageId":53},"path":"preview-scripts/assets/scripts/game/Chess/SingleChessBoardController.js"},{"deps":{"../bean/GlobalBean":21,"../common/EventCenter":23,"../common/GameMgr":24,"../net/MessageBaseBean":55,"../pfb/CongratsItemController":64,"../util/Config":72,"../util/Tools":70},"path":"preview-scripts/assets/scripts/game/CongratsDialogController.js"},{"deps":{"../../meshTools/tools/Publish":26,"../bean/GlobalBean":21,"../common/EventCenter":23,"../common/GameMgr":24,"../net/MessageBaseBean":55,"../pfb/MatchItemController":58,"../util/Config":72,"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/MatchParentController.js"},{"deps":{"../bean/GlobalBean":21,"../net/MessageId":53,"../net/WebSocketManager":78,"../ToastController":17,"./HallAutoController":46,"./HallCreateRoomController":31,"./HallJoinRoomController":32},"path":"preview-scripts/assets/scripts/hall/HallCenterLayController.js"},{"deps":{"../common/GameMgr":24,"../util/Config":72,"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/TopUpDialogController.js"},{"deps":{"../../meshTools/tools/Publish":26,"../util/AudioManager":71,"../util/Config":72,"../util/LocalStorageManager":68,"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/SettingDialogController.js"},{"deps":{"../../GlobalManagerController":20,"./LevelSelectController":6,"../../net/MessageId":53,"../../net/WebSocketManager":78},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectPageController.js"},{"deps":{"../bean/GlobalBean":21,"../util/Config":72,"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/HallAutoController.js"},{"deps":{"./LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/Level/LevelItemController.js"},{"deps":{"./LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectExample.js"},{"deps":{"../bean/GlobalBean":21,"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/PlayerLayoutController.js"},{"deps":{"../net/MessageId":53,"../net/WebSocketManager":78,"../util/Config":72,"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/KickOutDialogController.js"},{"deps":{"./HttpUtils":54,"./MessageBaseBean":55,"./GameServerUrl":13,"../../meshTools/MeshTools":74,"../common/GameMgr":24,"../common/EventCenter":23},"path":"preview-scripts/assets/scripts/net/HttpManager.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/IHttpMsgBody.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/MessageId.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/HttpUtils.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/MessageBaseBean.js"},{"deps":{"./MessageBaseBean":55,"./MessageId":53,"../util/Tools":70,"../../meshTools/Singleton":8,"../common/EventCenter":23,"../common/GameMgr":24},"path":"preview-scripts/assets/scripts/net/WebSocketTool.js"},{"deps":{"../../meshTools/Singleton":8},"path":"preview-scripts/assets/scripts/common/GameTools.js"},{"deps":{"../util/NickNameLabel":69,"../util/Tools":70},"path":"preview-scripts/assets/scripts/pfb/MatchItemController.js"},{"deps":{"../bean/GlobalBean":21,"../util/NickNameLabel":69,"../util/Tools":70},"path":"preview-scripts/assets/scripts/pfb/PlayerScoreController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/ErrorCode.js"},{"deps":{},"path":"preview-scripts/assets/scripts/pfb/InfoItemOneController.js"},{"deps":{"../util/Tools":70},"path":"preview-scripts/assets/scripts/pfb/PlayerGameController .js"},{"deps":{"../util/NickNameLabel":69,"../util/Tools":70},"path":"preview-scripts/assets/scripts/pfb/SeatItemController.js"},{"deps":{"../../meshTools/tools/Publish":26,"../util/Config":72,"../util/NickNameLabel":69,"../util/Tools":70},"path":"preview-scripts/assets/scripts/pfb/CongratsItemController.js"},{"deps":{"../common/GameMgr":24,"../net/MessageId":53,"../net/WebSocketManager":78,"../util/Config":72,"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/LeaveDialogController.js"},{"deps":{"../common/EventCenter":23,"../common/GameMgr":24,"../net/MessageBaseBean":55,"../util/Config":72},"path":"preview-scripts/assets/scripts/start_up/StartUpCenterController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/Dictionary.js"},{"deps":{"../../meshTools/Singleton":8},"path":"preview-scripts/assets/scripts/util/LocalStorageManager.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/NickNameLabel.js"},{"deps":{"./AudioManager":71,"./Config":72},"path":"preview-scripts/assets/scripts/util/Tools.js"},{"deps":{"./AudioMgr":15,"./LocalStorageManager":68},"path":"preview-scripts/assets/scripts/util/AudioManager.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/Config.js"},{"deps":{},"path":"preview-scripts/assets/scripts/hall/Level/ScrollViewHelper.js"},{"deps":{"./tools/Publish":26},"path":"preview-scripts/assets/meshTools/MeshTools.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/en.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/zh_CN.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/BlockingQueue.js"},{"deps":{"../../meshTools/Singleton":8,"../common/EventCenter":23,"../common/GameMgr":24,"./WebSocketTool":56},"path":"preview-scripts/assets/scripts/net/WebSocketManager.js"}];
var entries = ["preview-scripts/__qc_index__.js"];
var bundleScript = 'preview-scripts/__qc_bundle__.js';

/**
 * Notice: This file can not use ES6 (for IE 11)
 */
var modules = {};
var name2path = {};

// Will generated by module.js plugin
// var scripts = ${scripts};
// var entries = ${entries};
// var bundleScript = ${bundleScript};

if (typeof global === 'undefined') {
    window.global = window;
}

var isJSB = typeof jsb !== 'undefined';

function getXMLHttpRequest () {
    return window.XMLHttpRequest ? new window.XMLHttpRequest() : new ActiveXObject('MSXML2.XMLHTTP');
}

function downloadText(url, callback) {
    if (isJSB) {
        var result = jsb.fileUtils.getStringFromFile(url);
        callback(null, result);
        return;
    }

    var xhr = getXMLHttpRequest(),
        errInfo = 'Load text file failed: ' + url;
    xhr.open('GET', url, true);
    if (xhr.overrideMimeType) xhr.overrideMimeType('text\/plain; charset=utf-8');
    xhr.onload = function () {
        if (xhr.readyState === 4) {
            if (xhr.status === 200 || xhr.status === 0) {
                callback(null, xhr.responseText);
            }
            else {
                callback({status:xhr.status, errorMessage:errInfo + ', status: ' + xhr.status});
            }
        }
        else {
            callback({status:xhr.status, errorMessage:errInfo + '(wrong readyState)'});
        }
    };
    xhr.onerror = function(){
        callback({status:xhr.status, errorMessage:errInfo + '(error)'});
    };
    xhr.ontimeout = function(){
        callback({status:xhr.status, errorMessage:errInfo + '(time out)'});
    };
    xhr.send(null);
};

function loadScript (src, cb) {
    if (typeof require !== 'undefined') {
        require(src);
        return cb();
    }

    // var timer = 'load ' + src;
    // console.time(timer);

    var scriptElement = document.createElement('script');

    function done() {
        // console.timeEnd(timer);
        // deallocation immediate whatever
        scriptElement.remove();
    }

    scriptElement.onload = function () {
        done();
        cb();
    };
    scriptElement.onerror = function () {
        done();
        var error = 'Failed to load ' + src;
        console.error(error);
        cb(new Error(error));
    };
    scriptElement.setAttribute('type','text/javascript');
    scriptElement.setAttribute('charset', 'utf-8');
    scriptElement.setAttribute('src', src);

    document.head.appendChild(scriptElement);
}

function loadScripts (srcs, cb) {
    var n = srcs.length;

    srcs.forEach(function (src) {
        loadScript(src, function () {
            n--;
            if (n === 0) {
                cb();
            }
        });
    })
}

function formatPath (path) {
    let destPath = window.__quick_compile_project__.destPath;
    if (destPath) {
        let prefix = 'preview-scripts';
        if (destPath[destPath.length - 1] === '/') {
            prefix += '/';
        }
        path = path.replace(prefix, destPath);
    }
    return path;
}

window.__quick_compile_project__ = {
    destPath: '',

    registerModule: function (path, module) {
        path = formatPath(path);
        modules[path].module = module;
    },

    registerModuleFunc: function (path, func) {
        path = formatPath(path);
        modules[path].func = func;

        var sections = path.split('/');
        var name = sections[sections.length - 1];
        name = name.replace(/\.(?:js|ts|json)$/i, '');
        name2path[name] = path;
    },

    require: function (request, path) {
        var m, requestScript;

        path = formatPath(path);
        if (path) {
            m = modules[path];
            if (!m) {
                console.warn('Can not find module for path : ' + path);
                return null;
            }
        }

        if (m) {
            let depIndex = m.deps[request];
            // dependence script was excluded
            if (depIndex === -1) {
                return null;
            }
            else {
                requestScript = scripts[ m.deps[request] ];
            }
        }
        
        let requestPath = '';
        if (!requestScript) {
            // search from name2path when request is a dynamic module name
            if (/^[\w- .]*$/.test(request)) {
                requestPath = name2path[request];
            }

            if (!requestPath) {
                if (CC_JSB) {
                    return require(request);
                }
                else {
                    console.warn('Can not find deps [' + request + '] for path : ' + path);
                    return null;
                }
            }
        }
        else {
            requestPath = formatPath(requestScript.path);
        }

        let requestModule = modules[requestPath];
        if (!requestModule) {
            console.warn('Can not find request module for path : ' + requestPath);
            return null;
        }

        if (!requestModule.module && requestModule.func) {
            requestModule.func();
        }

        if (!requestModule.module) {
            console.warn('Can not find requestModule.module for path : ' + path);
            return null;
        }

        return requestModule.module.exports;
    },

    run: function () {
        entries.forEach(function (entry) {
            entry = formatPath(entry);
            var module = modules[entry];
            if (!module.module) {
                module.func();
            }
        });
    },

    load: function (cb) {
        var self = this;

        var srcs = scripts.map(function (script) {
            var path = formatPath(script.path);
            modules[path] = script;

            if (script.mtime) {
                path += ("?mtime=" + script.mtime);
            }
            return path;
        });

        console.time && console.time('load __quick_compile_project__');
        // jsb can not analysis sourcemap, so keep separate files.
        if (bundleScript && !isJSB) {
            downloadText(formatPath(bundleScript), function (err, bundleSource) {
                console.timeEnd && console.timeEnd('load __quick_compile_project__');
                if (err) {
                    console.error(err);
                    return;
                }

                let evalTime = 'eval __quick_compile_project__ : ' + srcs.length + ' files';
                console.time && console.time(evalTime);
                var sources = bundleSource.split('\n//------QC-SOURCE-SPLIT------\n');
                for (var i = 0; i < sources.length; i++) {
                    if (sources[i]) {
                        window.eval(sources[i]);
                        // not sure why new Function cannot set breakpoints precisely
                        // new Function(sources[i])()
                    }
                }
                self.run();
                console.timeEnd && console.timeEnd(evalTime);
                cb();
            })
        }
        else {
            loadScripts(srcs, function () {
                self.run();
                console.timeEnd && console.timeEnd('load __quick_compile_project__');
                cb();
            });
        }
    }
};

// Polyfill for IE 11
if (!('remove' in Element.prototype)) {
    Element.prototype.remove = function () {
        if (this.parentNode) {
            this.parentNode.removeChild(this);
        }
    };
}
})();
    