
(function () {
var scripts = [{"deps":{"./joui18n-script/LocalizedSprite":1,"./joui18n-script/LocalizedLabel":2,"./assets/meshTools/Singleton":8,"./assets/meshTools/BaseSDK":25,"./assets/meshTools/tools/MeshSdkApi":77,"./assets/meshTools/tools/Publish":78,"./assets/meshTools/tools/MeshSdk":4,"./assets/scripts/TipsDialogController":15,"./assets/scripts/ToastController":53,"./assets/scripts/GlobalManagerController":26,"./assets/scripts/bean/GameBean":3,"./assets/scripts/bean/GlobalBean":18,"./assets/scripts/bean/LanguageType":16,"./assets/scripts/bean/EnumBean":21,"./assets/scripts/common/GameData":9,"./assets/scripts/common/GameMgr":19,"./assets/scripts/common/GameTools":20,"./assets/scripts/common/MineConsole":66,"./assets/scripts/common/EventCenter":22,"./assets/scripts/game/CongratsDialogController":23,"./assets/scripts/game/GamePageController":24,"./assets/scripts/game/GameScoreController":58,"./assets/scripts/game/BtnController":28,"./assets/scripts/game/Chess/GridController":5,"./assets/scripts/game/Chess/HexChessBoardController":27,"./assets/scripts/game/Chess/SingleChessBoardController":30,"./assets/scripts/game/Chess/ChessBoardController":29,"./assets/scripts/hall/HallCenterLayController":32,"./assets/scripts/hall/HallCreateRoomController":31,"./assets/scripts/hall/HallJoinRoomController":33,"./assets/scripts/hall/HallPageController":45,"./assets/scripts/hall/HallParentController":35,"./assets/scripts/hall/InfoDialogController":34,"./assets/scripts/hall/KickOutDialogController":36,"./assets/scripts/hall/LeaveDialogController":40,"./assets/scripts/hall/LevelSelectDemo":37,"./assets/scripts/hall/MatchParentController":39,"./assets/scripts/hall/PlayerLayoutController":38,"./assets/scripts/hall/SettingDialogController":42,"./assets/scripts/hall/TopUpDialogController":41,"./assets/scripts/hall/HallAutoController":43,"./assets/scripts/hall/Level/LevelSelectController":6,"./assets/scripts/hall/Level/LevelSelectExample":46,"./assets/scripts/hall/Level/LevelSelectPageController":47,"./assets/scripts/hall/Level/ScrollViewHelper":44,"./assets/scripts/hall/Level/LevelItemController":48,"./assets/scripts/level/LevelPageController":11,"./assets/scripts/net/GameServerUrl":10,"./assets/scripts/net/HttpManager":49,"./assets/scripts/net/HttpUtils":51,"./assets/scripts/net/IHttpMsgBody":56,"./assets/scripts/net/MessageBaseBean":50,"./assets/scripts/net/MessageId":52,"./assets/scripts/net/WebSocketManager":55,"./assets/scripts/net/WebSocketTool":62,"./assets/scripts/net/ErrorCode":54,"./assets/scripts/pfb/InfoItemController":12,"./assets/scripts/pfb/InfoItemOneController":67,"./assets/scripts/pfb/MatchItemController":57,"./assets/scripts/pfb/PlayerGameController ":60,"./assets/scripts/pfb/PlayerScoreController":74,"./assets/scripts/pfb/SeatItemController":59,"./assets/scripts/pfb/CongratsItemController":61,"./assets/scripts/start_up/StartUpPageController":13,"./assets/scripts/start_up/StartUpCenterController":76,"./assets/scripts/test/NoticeRoundStartTest":14,"./assets/scripts/util/AudioMgr":17,"./assets/scripts/util/BlockingQueue":63,"./assets/scripts/util/Config":65,"./assets/scripts/util/Dictionary":64,"./assets/scripts/util/LocalStorageManager":70,"./assets/scripts/util/NickNameLabel":72,"./assets/scripts/util/Tools":68,"./assets/scripts/util/AudioManager":75,"./assets/meshTools/MeshTools":73,"./assets/resources/i18n/zh_HK":7,"./assets/resources/i18n/en":71,"./assets/resources/i18n/zh_CN":69},"path":"preview-scripts/__qc_index__.js"},{"deps":{},"path":"preview-scripts/joui18n-script/LocalizedSprite.js"},{"deps":{},"path":"preview-scripts/joui18n-script/LocalizedLabel.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/GameBean.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/tools/MeshSdk.js"},{"deps":{},"path":"preview-scripts/assets/scripts/game/Chess/GridController.js"},{"deps":{"./ScrollViewHelper":44},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectController.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/zh_HK.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/Singleton.js"},{"deps":{"../../meshTools/MeshTools":73,"../../meshTools/Singleton":8,"../net/GameServerUrl":10},"path":"preview-scripts/assets/scripts/common/GameData.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/GameServerUrl.js"},{"deps":{"../GlobalManagerController":26,"../hall/LeaveDialogController":40,"../util/Config":65,"../util/Tools":68,"../net/MessageId":52,"../net/WebSocketManager":55,"../common/EventCenter":22,"../common/GameMgr":19,"../game/Chess/SingleChessBoardController":30},"path":"preview-scripts/assets/scripts/level/LevelPageController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/pfb/InfoItemController.js"},{"deps":{"../common/GameMgr":19,"./StartUpCenterController":76},"path":"preview-scripts/assets/scripts/start_up/StartUpPageController.js"},{"deps":{"../common/GameMgr":19,"../common/EventCenter":22,"../net/MessageId":52},"path":"preview-scripts/assets/scripts/test/NoticeRoundStartTest.js"},{"deps":{"./util/Config":65,"./util/Tools":68},"path":"preview-scripts/assets/scripts/TipsDialogController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/LanguageType.js"},{"deps":{"./Config":65,"./Dictionary":64},"path":"preview-scripts/assets/scripts/util/AudioMgr.js"},{"deps":{"../../meshTools/Singleton":8,"../hall/HallAutoController":43},"path":"preview-scripts/assets/scripts/bean/GlobalBean.js"},{"deps":{"../../meshTools/tools/MeshSdkApi":77,"./EventCenter":22,"./GameData":9,"./GameTools":20,"./MineConsole":66},"path":"preview-scripts/assets/scripts/common/GameMgr.js"},{"deps":{"../../meshTools/Singleton":8},"path":"preview-scripts/assets/scripts/common/GameTools.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/EnumBean.js"},{"deps":{"../../meshTools/Singleton":8,"./GameMgr":19},"path":"preview-scripts/assets/scripts/common/EventCenter.js"},{"deps":{"../bean/GlobalBean":18,"../common/EventCenter":22,"../common/GameMgr":19,"../net/MessageBaseBean":50,"../pfb/CongratsItemController":61,"../util/Config":65,"../util/Tools":68},"path":"preview-scripts/assets/scripts/game/CongratsDialogController.js"},{"deps":{"./CongratsDialogController":23,"./GameScoreController":58,"../bean/GlobalBean":18,"../hall/LeaveDialogController":40,"../util/Config":65,"../util/Tools":68,"../util/AudioManager":75,"./Chess/HexChessBoardController":27,"./Chess/ChessBoardController":29,"../pfb/PlayerGameController ":60,"../net/MessageId":52,"../net/WebSocketManager":55},"path":"preview-scripts/assets/scripts/game/GamePageController.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/BaseSDK.js"},{"deps":{"../meshTools/MeshTools":73,"../meshTools/tools/Publish":78,"./bean/EnumBean":21,"./bean/GlobalBean":18,"./bean/LanguageType":16,"./common/EventCenter":22,"./common/GameMgr":19,"./game/GamePageController":24,"./hall/HallPageController":45,"./level/LevelPageController":11,"./hall/TopUpDialogController":41,"./net/ErrorCode":54,"./net/GameServerUrl":10,"./net/MessageBaseBean":50,"./net/MessageId":52,"./net/WebSocketManager":55,"./net/WebSocketTool":62,"./start_up/StartUpPageController":13,"./TipsDialogController":15,"./ToastController":53,"./util/AudioMgr":17,"./util/Config":65},"path":"preview-scripts/assets/scripts/GlobalManagerController.js"},{"deps":{"../../bean/GlobalBean":18,"../../pfb/PlayerGameController ":60},"path":"preview-scripts/assets/scripts/game/Chess/HexChessBoardController.js"},{"deps":{"../util/AudioManager":75,"../util/Config":65,"../util/LocalStorageManager":70},"path":"preview-scripts/assets/scripts/game/BtnController.js"},{"deps":{"../../bean/GlobalBean":18,"../../pfb/PlayerGameController ":60},"path":"preview-scripts/assets/scripts/game/Chess/ChessBoardController.js"},{"deps":{"../../net/WebSocketManager":55,"../../net/MessageId":52},"path":"preview-scripts/assets/scripts/game/Chess/SingleChessBoardController.js"},{"deps":{"../bean/GlobalBean":18,"../pfb/SeatItemController":59,"../util/Config":65,"../util/Tools":68},"path":"preview-scripts/assets/scripts/hall/HallCreateRoomController.js"},{"deps":{"../bean/GlobalBean":18,"../net/MessageId":52,"../net/WebSocketManager":55,"../ToastController":53,"./HallAutoController":43,"./HallCreateRoomController":31,"./HallJoinRoomController":33},"path":"preview-scripts/assets/scripts/hall/HallCenterLayController.js"},{"deps":{"../util/Tools":68},"path":"preview-scripts/assets/scripts/hall/HallJoinRoomController.js"},{"deps":{"../util/Config":65,"../util/Tools":68},"path":"preview-scripts/assets/scripts/hall/InfoDialogController.js"},{"deps":{"../../meshTools/tools/Publish":78,"../bean/GlobalBean":18,"../common/GameMgr":19,"../net/MessageId":52,"../net/WebSocketManager":55,"../ToastController":53,"../util/Config":65,"../util/Tools":68,"./HallCenterLayController":32},"path":"preview-scripts/assets/scripts/hall/HallParentController.js"},{"deps":{"../net/MessageId":52,"../net/WebSocketManager":55,"../util/Config":65,"../util/Tools":68},"path":"preview-scripts/assets/scripts/hall/KickOutDialogController.js"},{"deps":{"./Level/LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/LevelSelectDemo.js"},{"deps":{"../bean/GlobalBean":18,"../util/Tools":68},"path":"preview-scripts/assets/scripts/hall/PlayerLayoutController.js"},{"deps":{"../../meshTools/tools/Publish":78,"../bean/GlobalBean":18,"../common/EventCenter":22,"../common/GameMgr":19,"../net/MessageBaseBean":50,"../pfb/MatchItemController":57,"../util/Config":65,"../util/Tools":68},"path":"preview-scripts/assets/scripts/hall/MatchParentController.js"},{"deps":{"../common/GameMgr":19,"../net/MessageId":52,"../net/WebSocketManager":55,"../util/Config":65,"../util/Tools":68},"path":"preview-scripts/assets/scripts/hall/LeaveDialogController.js"},{"deps":{"../common/GameMgr":19,"../util/Config":65,"../util/Tools":68},"path":"preview-scripts/assets/scripts/hall/TopUpDialogController.js"},{"deps":{"../../meshTools/tools/Publish":78,"../util/AudioManager":75,"../util/Config":65,"../util/LocalStorageManager":70,"../util/Tools":68},"path":"preview-scripts/assets/scripts/hall/SettingDialogController.js"},{"deps":{"../bean/GlobalBean":18,"../util/Config":65,"../util/Tools":68},"path":"preview-scripts/assets/scripts/hall/HallAutoController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/hall/Level/ScrollViewHelper.js"},{"deps":{"../bean/GlobalBean":18,"../common/GameMgr":19,"../net/MessageId":52,"../net/WebSocketManager":55,"../net/WebSocketTool":62,"../ToastController":53,"../util/AudioManager":75,"./HallParentController":35,"./InfoDialogController":34,"./KickOutDialogController":36,"./LeaveDialogController":40,"./Level/LevelSelectPageController":47,"./MatchParentController":39,"./SettingDialogController":42},"path":"preview-scripts/assets/scripts/hall/HallPageController.js"},{"deps":{"./LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectExample.js"},{"deps":{"../../GlobalManagerController":26,"./LevelSelectController":6,"../../net/MessageId":52,"../../net/WebSocketManager":55},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectPageController.js"},{"deps":{"./LevelSelectController":6},"path":"preview-scripts/assets/scripts/hall/Level/LevelItemController.js"},{"deps":{"./HttpUtils":51,"./MessageBaseBean":50,"./GameServerUrl":10,"../../meshTools/MeshTools":73,"../common/GameMgr":19,"../common/EventCenter":22},"path":"preview-scripts/assets/scripts/net/HttpManager.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/MessageBaseBean.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/HttpUtils.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/MessageId.js"},{"deps":{},"path":"preview-scripts/assets/scripts/ToastController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/ErrorCode.js"},{"deps":{"../../meshTools/Singleton":8,"../common/EventCenter":22,"../common/GameMgr":19,"./WebSocketTool":62},"path":"preview-scripts/assets/scripts/net/WebSocketManager.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/IHttpMsgBody.js"},{"deps":{"../util/NickNameLabel":72,"../util/Tools":68},"path":"preview-scripts/assets/scripts/pfb/MatchItemController.js"},{"deps":{"../bean/GlobalBean":18,"../pfb/PlayerScoreController":74},"path":"preview-scripts/assets/scripts/game/GameScoreController.js"},{"deps":{"../util/NickNameLabel":72,"../util/Tools":68},"path":"preview-scripts/assets/scripts/pfb/SeatItemController.js"},{"deps":{"../util/Tools":68},"path":"preview-scripts/assets/scripts/pfb/PlayerGameController .js"},{"deps":{"../../meshTools/tools/Publish":78,"../util/Config":65,"../util/NickNameLabel":72,"../util/Tools":68},"path":"preview-scripts/assets/scripts/pfb/CongratsItemController.js"},{"deps":{"./MessageBaseBean":50,"./MessageId":52,"../util/Tools":68,"../../meshTools/Singleton":8,"../common/EventCenter":22,"../common/GameMgr":19},"path":"preview-scripts/assets/scripts/net/WebSocketTool.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/BlockingQueue.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/Dictionary.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/Config.js"},{"deps":{"../../meshTools/Singleton":8},"path":"preview-scripts/assets/scripts/common/MineConsole.js"},{"deps":{},"path":"preview-scripts/assets/scripts/pfb/InfoItemOneController.js"},{"deps":{"./AudioManager":75,"./Config":65},"path":"preview-scripts/assets/scripts/util/Tools.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/zh_CN.js"},{"deps":{"../../meshTools/Singleton":8},"path":"preview-scripts/assets/scripts/util/LocalStorageManager.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/en.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/NickNameLabel.js"},{"deps":{"./tools/Publish":78},"path":"preview-scripts/assets/meshTools/MeshTools.js"},{"deps":{"../bean/GlobalBean":18,"../util/NickNameLabel":72,"../util/Tools":68},"path":"preview-scripts/assets/scripts/pfb/PlayerScoreController.js"},{"deps":{"./AudioMgr":17,"./LocalStorageManager":70},"path":"preview-scripts/assets/scripts/util/AudioManager.js"},{"deps":{"../common/EventCenter":22,"../common/GameMgr":19,"../net/MessageBaseBean":50,"../util/Config":65},"path":"preview-scripts/assets/scripts/start_up/StartUpCenterController.js"},{"deps":{"../MeshTools":73,"../BaseSDK":25,"../../scripts/net/MessageBaseBean":50,"../../scripts/common/GameMgr":19,"../../scripts/common/EventCenter":22,"MeshSdk":4},"path":"preview-scripts/assets/meshTools/tools/MeshSdkApi.js"},{"deps":{"../Singleton":8},"path":"preview-scripts/assets/meshTools/tools/Publish.js"}];
var entries = ["preview-scripts/__qc_index__.js"];
var bundleScript = 'preview-scripts/__qc_bundle__.js';

/**
 * Notice: This file can not use ES6 (for IE 11)
 */
var modules = {};
var name2path = {};

// Will generated by module.js plugin
// var scripts = ${scripts};
// var entries = ${entries};
// var bundleScript = ${bundleScript};

if (typeof global === 'undefined') {
    window.global = window;
}

var isJSB = typeof jsb !== 'undefined';

function getXMLHttpRequest () {
    return window.XMLHttpRequest ? new window.XMLHttpRequest() : new ActiveXObject('MSXML2.XMLHTTP');
}

function downloadText(url, callback) {
    if (isJSB) {
        var result = jsb.fileUtils.getStringFromFile(url);
        callback(null, result);
        return;
    }

    var xhr = getXMLHttpRequest(),
        errInfo = 'Load text file failed: ' + url;
    xhr.open('GET', url, true);
    if (xhr.overrideMimeType) xhr.overrideMimeType('text\/plain; charset=utf-8');
    xhr.onload = function () {
        if (xhr.readyState === 4) {
            if (xhr.status === 200 || xhr.status === 0) {
                callback(null, xhr.responseText);
            }
            else {
                callback({status:xhr.status, errorMessage:errInfo + ', status: ' + xhr.status});
            }
        }
        else {
            callback({status:xhr.status, errorMessage:errInfo + '(wrong readyState)'});
        }
    };
    xhr.onerror = function(){
        callback({status:xhr.status, errorMessage:errInfo + '(error)'});
    };
    xhr.ontimeout = function(){
        callback({status:xhr.status, errorMessage:errInfo + '(time out)'});
    };
    xhr.send(null);
};

function loadScript (src, cb) {
    if (typeof require !== 'undefined') {
        require(src);
        return cb();
    }

    // var timer = 'load ' + src;
    // console.time(timer);

    var scriptElement = document.createElement('script');

    function done() {
        // console.timeEnd(timer);
        // deallocation immediate whatever
        scriptElement.remove();
    }

    scriptElement.onload = function () {
        done();
        cb();
    };
    scriptElement.onerror = function () {
        done();
        var error = 'Failed to load ' + src;
        console.error(error);
        cb(new Error(error));
    };
    scriptElement.setAttribute('type','text/javascript');
    scriptElement.setAttribute('charset', 'utf-8');
    scriptElement.setAttribute('src', src);

    document.head.appendChild(scriptElement);
}

function loadScripts (srcs, cb) {
    var n = srcs.length;

    srcs.forEach(function (src) {
        loadScript(src, function () {
            n--;
            if (n === 0) {
                cb();
            }
        });
    })
}

function formatPath (path) {
    let destPath = window.__quick_compile_project__.destPath;
    if (destPath) {
        let prefix = 'preview-scripts';
        if (destPath[destPath.length - 1] === '/') {
            prefix += '/';
        }
        path = path.replace(prefix, destPath);
    }
    return path;
}

window.__quick_compile_project__ = {
    destPath: '',

    registerModule: function (path, module) {
        path = formatPath(path);
        modules[path].module = module;
    },

    registerModuleFunc: function (path, func) {
        path = formatPath(path);
        modules[path].func = func;

        var sections = path.split('/');
        var name = sections[sections.length - 1];
        name = name.replace(/\.(?:js|ts|json)$/i, '');
        name2path[name] = path;
    },

    require: function (request, path) {
        var m, requestScript;

        path = formatPath(path);
        if (path) {
            m = modules[path];
            if (!m) {
                console.warn('Can not find module for path : ' + path);
                return null;
            }
        }

        if (m) {
            let depIndex = m.deps[request];
            // dependence script was excluded
            if (depIndex === -1) {
                return null;
            }
            else {
                requestScript = scripts[ m.deps[request] ];
            }
        }
        
        let requestPath = '';
        if (!requestScript) {
            // search from name2path when request is a dynamic module name
            if (/^[\w- .]*$/.test(request)) {
                requestPath = name2path[request];
            }

            if (!requestPath) {
                if (CC_JSB) {
                    return require(request);
                }
                else {
                    console.warn('Can not find deps [' + request + '] for path : ' + path);
                    return null;
                }
            }
        }
        else {
            requestPath = formatPath(requestScript.path);
        }

        let requestModule = modules[requestPath];
        if (!requestModule) {
            console.warn('Can not find request module for path : ' + requestPath);
            return null;
        }

        if (!requestModule.module && requestModule.func) {
            requestModule.func();
        }

        if (!requestModule.module) {
            console.warn('Can not find requestModule.module for path : ' + path);
            return null;
        }

        return requestModule.module.exports;
    },

    run: function () {
        entries.forEach(function (entry) {
            entry = formatPath(entry);
            var module = modules[entry];
            if (!module.module) {
                module.func();
            }
        });
    },

    load: function (cb) {
        var self = this;

        var srcs = scripts.map(function (script) {
            var path = formatPath(script.path);
            modules[path] = script;

            if (script.mtime) {
                path += ("?mtime=" + script.mtime);
            }
            return path;
        });

        console.time && console.time('load __quick_compile_project__');
        // jsb can not analysis sourcemap, so keep separate files.
        if (bundleScript && !isJSB) {
            downloadText(formatPath(bundleScript), function (err, bundleSource) {
                console.timeEnd && console.timeEnd('load __quick_compile_project__');
                if (err) {
                    console.error(err);
                    return;
                }

                let evalTime = 'eval __quick_compile_project__ : ' + srcs.length + ' files';
                console.time && console.time(evalTime);
                var sources = bundleSource.split('\n//------QC-SOURCE-SPLIT------\n');
                for (var i = 0; i < sources.length; i++) {
                    if (sources[i]) {
                        window.eval(sources[i]);
                        // not sure why new Function cannot set breakpoints precisely
                        // new Function(sources[i])()
                    }
                }
                self.run();
                console.timeEnd && console.timeEnd(evalTime);
                cb();
            })
        }
        else {
            loadScripts(srcs, function () {
                self.run();
                console.timeEnd && console.timeEnd('load __quick_compile_project__');
                cb();
            });
        }
    }
};

// Polyfill for IE 11
if (!('remove' in Element.prototype)) {
    Element.prototype.remove = function () {
        if (this.parentNode) {
            this.parentNode.removeChild(this);
        }
    };
}
})();
    