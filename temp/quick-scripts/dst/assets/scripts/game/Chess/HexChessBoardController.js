
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/Chess/HexChessBoardController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'd9f2eirVKNNorhz2LDmkG2T', 'HexChessBoardController');
// scripts/game/Chess/HexChessBoardController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../../bean/GlobalBean");
var PlayerGameController_1 = require("../../pfb/PlayerGameController ");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var HexChessBoardController = /** @class */ (function (_super) {
    __extends(HexChessBoardController, _super);
    function HexChessBoardController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.playerGamePrefab = null;
        _this.boomPrefab = null;
        _this.biaojiPrefab = null;
        _this.boom1Prefab = null;
        _this.boom2Prefab = null;
        _this.boom3Prefab = null;
        _this.boom4Prefab = null;
        _this.boom5Prefab = null;
        _this.boom6Prefab = null;
        _this.boom7Prefab = null;
        _this.boom8Prefab = null;
        _this.boardNode = null; // 棋盘节点
        // 六边形棋盘配置
        _this.HEX_SIZE = 44; // 六边形半径
        _this.HEX_WIDTH = _this.HEX_SIZE * 2; // 六边形宽度
        _this.HEX_HEIGHT = _this.HEX_SIZE * Math.sqrt(3); // 六边形高度
        // 格子数据存储 - 使用Map存储六边形坐标
        _this.hexGridData = new Map(); // 存储六边形格子数据
        _this.hexGridNodes = new Map(); // 存储六边形格子节点
        _this.validHexCoords = []; // 有效的六边形坐标列表
        return _this;
    }
    HexChessBoardController.prototype.onLoad = function () {
    };
    HexChessBoardController.prototype.start = function () {
        var _this = this;
        // 延迟一帧后再次尝试启用触摸事件，确保所有节点都已创建完成
        this.scheduleOnce(function () {
            _this.setValidHexCoords([]); // 传入空数组，但会被忽略
            // 测试预制体位置计算
            _this.testHexPositionCalculation();
            _this.enableTouchForExistingGrids();
        }, 0.1);
    };
    /**
     * 设置有效的六边形坐标列表（忽略服务器数据，使用前端节点坐标）
     * @param _coords 服务器发送的坐标列表（将被忽略）
     */
    HexChessBoardController.prototype.setValidHexCoords = function (_coords) {
        // 忽略服务器传入的坐标，始终从节点名称自动生成
        this.generateCoordsFromNodeNames();
        this.initHexBoard();
    };
    /**
     * 从节点名称自动生成有效坐标列表
     */
    HexChessBoardController.prototype.generateCoordsFromNodeNames = function () {
        if (!this.boardNode) {
            console.error("❌ 棋盘节点不存在，无法生成坐标列表");
            return;
        }
        var foundCoords = [];
        var children = this.boardNode.children;
        var _loop_1 = function (i) {
            var child = children[i];
            var coords = this_1.parseHexCoordinateFromName(child.name);
            if (coords) {
                // 检查是否已经存在相同的坐标
                var exists = foundCoords.some(function (c) { return c.q === coords.q && c.r === coords.r; });
                if (!exists) {
                    foundCoords.push({ q: coords.q, r: coords.r });
                }
            }
        };
        var this_1 = this;
        for (var i = 0; i < children.length; i++) {
            _loop_1(i);
        }
        this.validHexCoords = foundCoords;
    };
    // 初始化六边形棋盘
    HexChessBoardController.prototype.initHexBoard = function () {
        // 清空现有数据
        this.hexGridData.clear();
        this.hexGridNodes.clear();
        // 初始化有效坐标的数据
        for (var _i = 0, _a = this.validHexCoords; _i < _a.length; _i++) {
            var coord = _a[_i];
            var key = this.getHexKey(coord.q, coord.r);
            this.hexGridData.set(key, {
                q: coord.q,
                r: coord.r,
                worldPos: this.getHexWorldPosition(coord.q, coord.r),
                hasPlayer: false
            });
        }
        this.createHexGridNodes();
    };
    // 生成六边形坐标的唯一键
    HexChessBoardController.prototype.getHexKey = function (q, r) {
        return q + "," + r;
    };
    // 启用现有格子的触摸事件
    HexChessBoardController.prototype.createHexGridNodes = function () {
        if (!this.boardNode) {
            console.error("棋盘节点未设置！");
            return;
        }
        // 如果格子已经存在，直接启用触摸事件
        this.enableTouchForExistingGrids();
    };
    // 为现有格子启用触摸事件
    HexChessBoardController.prototype.enableTouchForExistingGrids = function () {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法启用触摸事件！");
            return;
        }
        // 遍历棋盘节点的所有子节点
        var children = this.boardNode.children;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            // 尝试从节点名称解析六边形坐标
            var coords = this.parseHexCoordinateFromName(child.name);
            if (coords) {
                this.setupHexGridTouchEvents(child, coords.q, coords.r);
                var key = this.getHexKey(coords.q, coords.r);
                this.hexGridNodes.set(key, child);
            }
            else {
                // 如果无法从名称解析，尝试从位置计算
                var pos = child.getPosition();
                var coords_1 = this.getHexCoordinateFromPosition(pos);
                if (coords_1) {
                    this.setupHexGridTouchEvents(child, coords_1.q, coords_1.r);
                    var key = this.getHexKey(coords_1.q, coords_1.r);
                    this.hexGridNodes.set(key, child);
                }
            }
        }
    };
    // 从节点名称解析六边形坐标
    HexChessBoardController.prototype.parseHexCoordinateFromName = function (nodeName) {
        var patterns = [
            /^sixblock_(-?\d+)_(-?\d+)$/,
        ];
        for (var _i = 0, patterns_1 = patterns; _i < patterns_1.length; _i++) {
            var pattern = patterns_1[_i];
            var match = nodeName.match(pattern);
            if (match) {
                var coords = { q: parseInt(match[1]), r: parseInt(match[2]) };
                return coords;
            }
        }
        console.warn("\u274C \u65E0\u6CD5\u89E3\u6790\u8282\u70B9\u540D\u79F0: " + nodeName);
        return null;
    };
    // 从位置计算六边形坐标（近似）
    HexChessBoardController.prototype.getHexCoordinateFromPosition = function (pos) {
        // 六边形坐标转换（从像素坐标到六边形坐标）
        var x = pos.x;
        var y = pos.y;
        // 使用六边形坐标转换公式
        var q = Math.round((Math.sqrt(3) / 3 * x - 1 / 3 * y) / this.HEX_SIZE);
        var r = Math.round((2 / 3 * y) / this.HEX_SIZE);
        // 检查是否为有效坐标
        if (this.isValidHexCoordinate(q, r)) {
            return { q: q, r: r };
        }
        return null;
    };
    // 计算六边形预制体的生成位置（直接使用您提供的格子中心坐标）
    HexChessBoardController.prototype.getHexWorldPosition = function (q, r, isPlayerAvatar) {
        if (isPlayerAvatar === void 0) { isPlayerAvatar = false; }
        // 您提供的精确格子中心坐标
        var exactCoords = new Map();
        // 更新后的基准点坐标（与rowData保持一致）
        exactCoords.set("0,0", cc.v2(-300, -258)); // r=0行基准点
        exactCoords.set("1,-1", cc.v2(-258, -184)); // r=-1行基准点
        exactCoords.set("1,-2", cc.v2(-300, -108)); // r=-2行基准点
        exactCoords.set("2,-3", cc.v2(-258, -36)); // r=-3行基准点
        exactCoords.set("2,-4", cc.v2(-300, 37)); // r=-4行基准点
        exactCoords.set("3,-5", cc.v2(-258, 110)); // r=-5行基准点
        exactCoords.set("3,-6", cc.v2(-300, 185)); // r=-6行基准点
        exactCoords.set("4,-7", cc.v2(-258, 260)); // r=-7行基准点
        // 首先检查是否有精确坐标
        var key = q + "," + r;
        if (exactCoords.has(key)) {
            var pos = exactCoords.get(key);
            // 如果是单人头像预制体，往左上偏移一点点
            if (isPlayerAvatar) {
                return cc.v2(pos.x, pos.y - 12); // 改为往左偏移10像素
            }
            return pos;
        }
        // 对于其他坐标，使用基于您提供的精确坐标数据进行计算
        // 定义每一行的数据：使用统一步长86，保证美观整齐
        var UNIFORM_STEP_X = 86; // 统一的x方向步长
        var rowData = new Map();
        // 基于您提供的更新数据，使用统一步长86
        rowData.set(0, { baseQ: 0, baseX: -300, y: -258 }); // r=0行：基准点(0,0) → (-300, -258)
        rowData.set(-1, { baseQ: 1, baseX: -258, y: -184 }); // r=-1行：基准点(1,-1) → (-258, -184)
        rowData.set(-2, { baseQ: 1, baseX: -300, y: -108 }); // r=-2行：基准点(1,-2) → (-300, -108)
        rowData.set(-3, { baseQ: 2, baseX: -258, y: -36 }); // r=-3行：基准点(2,-3) → (-258, -36)
        rowData.set(-4, { baseQ: 2, baseX: -300, y: 37 }); // r=-4行：基准点(2,-4) → (-300, 37)
        rowData.set(-5, { baseQ: 3, baseX: -258, y: 110 }); // r=-5行：基准点(3,-5) → (-258, 110)
        rowData.set(-6, { baseQ: 3, baseX: -300, y: 185 }); // r=-6行：基准点(3,-6) → (-300, 185)
        rowData.set(-7, { baseQ: 4, baseX: -258, y: 260 }); // r=-7行：基准点(4,-7) → (-258, 260)
        // 计算基础位置
        var x, y;
        // 如果有该行的数据，使用统一步长计算
        if (rowData.has(r)) {
            var data = rowData.get(r);
            x = data.baseX + (q - data.baseQ) * UNIFORM_STEP_X;
            y = data.y;
        }
        else {
            // 对于其他行，使用通用的六边形轴线坐标系公式（也使用统一步长）
            var baseX = -300; // 更新为新的基准点
            var baseY = -258;
            var stepXR = -43;
            var stepYR = 74;
            x = baseX + q * UNIFORM_STEP_X + r * stepXR;
            y = baseY - r * stepYR;
        }
        // 如果是单人头像预制体，往左上偏移一点点
        if (isPlayerAvatar) {
            y -= 12; // 往下偏移12像素（相比之前的-20，现在是-12，相当于往上调了8像素）
        }
        return cc.v2(x, y);
    };
    // 为六边形格子节点设置触摸事件
    HexChessBoardController.prototype.setupHexGridTouchEvents = function (gridNode, q, r) {
        var _this = this;
        // 安全检查：确保坐标有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.error("\u274C setupHexGridTouchEvents: \u5C1D\u8BD5\u4E3A\u65E0\u6548\u5750\u6807(" + q + "," + r + ")\u8BBE\u7F6E\u89E6\u6478\u4E8B\u4EF6");
            return;
        }
        // 长按相关变量
        var isLongPressing = false;
        var longPressTimer = 0;
        var longPressCallback = null;
        var LONG_PRESS_TIME = 1.0; // 1秒长按时间
        // 触摸开始事件
        gridNode.on(cc.Node.EventType.TOUCH_START, function (_event) {
            isLongPressing = true;
            longPressTimer = 0;
            // 开始长按检测
            longPressCallback = function () {
                if (isLongPressing) {
                    longPressTimer += 0.1;
                    if (longPressTimer >= LONG_PRESS_TIME) {
                        _this.onHexGridLongPress(q, r);
                        isLongPressing = false;
                        if (longPressCallback) {
                            _this.unschedule(longPressCallback);
                        }
                    }
                }
            };
            _this.schedule(longPressCallback, 0.1);
        }, this);
        // 触摸结束事件
        gridNode.on(cc.Node.EventType.TOUCH_END, function (event) {
            // 如果不是长按，则执行点击事件
            if (isLongPressing && longPressTimer < LONG_PRESS_TIME) {
                _this.onHexGridClick(q, r, event);
            }
            isLongPressing = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
            }
        }, this);
        // 触摸取消事件
        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, function (_event) {
            isLongPressing = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
            }
        }, this);
        // 添加Button组件以确保触摸响应
        var button = gridNode.getComponent(cc.Button);
        if (!button) {
            button = gridNode.addComponent(cc.Button);
            button.transition = cc.Button.Transition.SCALE;
            button.zoomScale = 0.95;
        }
    };
    // 六边形格子点击事件 - 发送挖掘操作
    HexChessBoardController.prototype.onHexGridClick = function (q, r, _event) {
        // 检查坐标是否有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u274C \u65E0\u6548\u7684\u516D\u8FB9\u5F62\u5750\u6807: (" + q + ", " + r + ")");
            return;
        }
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        // 检查该位置是否已经有玩家预制体
        if (gridData && gridData.hasPlayer) {
            console.warn("\u26A0\uFE0F \u683C\u5B50(" + q + ", " + r + ")\u5DF2\u6709\u73A9\u5BB6");
            return;
        }
        // 发送挖掘操作事件 (action = 1)
        this.node.emit('hex-chess-board-click', {
            q: q,
            r: r,
            action: 1 // 1 = 挖掘
        });
    };
    // 六边形格子长按事件 - 发送标记操作
    HexChessBoardController.prototype.onHexGridLongPress = function (q, r) {
        // 检查坐标是否有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u274C \u65E0\u6548\u7684\u516D\u8FB9\u5F62\u5750\u6807: (" + q + ", " + r + ")");
            return;
        }
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        // 检查该位置是否已经有玩家预制体
        if (gridData && gridData.hasPlayer) {
            console.warn("\u26A0\uFE0F \u683C\u5B50(" + q + ", " + r + ")\u5DF2\u6709\u73A9\u5BB6");
            return;
        }
        // 发送标记操作事件 (action = 2)
        this.node.emit('hex-chess-board-click', {
            q: q,
            r: r,
            action: 2 // 2 = 标记
        });
    };
    // 检查六边形坐标是否有效
    HexChessBoardController.prototype.isValidHexCoordinate = function (q, r) {
        var key = this.getHexKey(q, r);
        return this.hexGridData.has(key);
    };
    // 在六边形格子上放置玩家预制体
    HexChessBoardController.prototype.placePlayerOnHexGrid = function (q, r, withFlag) {
        var _this = this;
        if (withFlag === void 0) { withFlag = false; }
        // 双重检查：确保坐标有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.error("\u274C placePlayerOnHexGrid: \u65E0\u6548\u5750\u6807(" + q + "," + r + ")");
            return;
        }
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        // 双重检查：确保格子为空
        if (!gridData || gridData.hasPlayer) {
            console.error("\u274C placePlayerOnHexGrid: \u683C\u5B50(" + q + "," + r + ")\u5DF2\u6709\u73A9\u5BB6\uFF0C\u4E0D\u80FD\u91CD\u590D\u653E\u7F6E");
            return;
        }
        if (!this.playerGamePrefab) {
            console.error("❌ 玩家预制体未设置！");
            return;
        }
        if (!this.boardNode) {
            console.error("❌ 棋盘节点未设置！");
            return;
        }
        // 实例化玩家预制体
        var playerNode = cc.instantiate(this.playerGamePrefab);
        // 计算正确的位置（单人头像预制体，y轴+20）
        var correctPosition = this.getHexWorldPosition(q, r, true);
        playerNode.setPosition(correctPosition);
        // 设置单人放置的缩放为0.8
        playerNode.setScale(0.8);
        // 先隐藏节点，等头像加载完成后再显示
        playerNode.active = false;
        // 处理Layout限制问题
        this.addPlayerNodeSafely(playerNode);
        // 设置头像和用户数据（异步加载）
        this.setupPlayerAvatarAsync(playerNode, q, r, withFlag, function () {
            // 头像加载完成的回调，播放生成动画
            _this.playAvatarSpawnAnimation(playerNode);
        });
        // 更新格子数据
        gridData.hasPlayer = true;
        gridData.playerNode = playerNode;
    };
    // 安全地添加玩家节点（处理Layout限制）
    HexChessBoardController.prototype.addPlayerNodeSafely = function (playerNode) {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法添加玩家节点！");
            return;
        }
        // 检查棋盘节点是否有Layout组件
        var layout = this.boardNode.getComponent(cc.Layout);
        if (layout) {
            // 临时禁用Layout
            layout.enabled = false;
            // 添加节点
            this.boardNode.addChild(playerNode);
        }
        else {
            this.boardNode.addChild(playerNode);
        }
    };
    // 异步设置玩家头像（带回调）
    HexChessBoardController.prototype.setupPlayerAvatarAsync = function (playerNode, q, r, withFlag, onComplete) {
        var _a, _b;
        // 查找PlayerGameController组件（使用类引用）
        var playerController = playerNode.getComponent(PlayerGameController_1.default);
        if (playerController) {
            // 检查avatar节点是否存在
            if (playerController.avatar) {
                // 检查avatar节点是否有Sprite组件
                var avatarSprite = playerController.avatar.getComponent(cc.Sprite);
                if (!avatarSprite) {
                    avatarSprite = playerController.avatar.addComponent(cc.Sprite);
                }
                // 确保avatar节点可见
                playerController.avatar.active = true;
                playerController.avatar.opacity = 255;
            }
            else {
                console.error("❌ PlayerGameController中的avatar节点为null");
                onComplete();
                return;
            }
            // 设置旗子节点的显示状态
            if (playerController.flagNode) {
                playerController.flagNode.active = withFlag;
                // 额外检查旗子节点的可见性
                if (withFlag) {
                    playerController.flagNode.opacity = 255;
                    // 确保旗子节点的父节点也是可见的
                    var parent = playerController.flagNode.parent;
                    while (parent && parent !== playerNode) {
                        parent.active = true;
                        parent = parent.parent;
                    }
                }
            }
            else {
                console.warn("\u26A0\uFE0F \u627E\u4E0D\u5230\u65D7\u5B50\u8282\u70B9 (" + q + "," + r + ")");
            }
            // 获取当前用户ID
            var currentUserId = ((_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId) || "hex_player_" + q + "_" + r;
            // 在节点上存储userId，用于后续查找
            playerNode['userId'] = currentUserId;
            // 创建用户数据并设置头像
            var userData = {
                userId: currentUserId,
                nickName: "\u73A9\u5BB6(" + q + "," + r + ")",
                avatar: this.getDefaultAvatarUrl(),
                score: 0,
                pos: 0,
                coin: 0,
                status: 0,
                rank: 0
            };
            // 使用PlayerGameController的setData方法来设置头像
            try {
                playerController.setData(userData);
                // 延迟设置旗子状态，确保在PlayerGameController初始化之后
                this.scheduleOnce(function () {
                    if (playerController.flagNode) {
                        playerController.flagNode.active = withFlag;
                    }
                    onComplete();
                }, 0.1);
            }
            catch (error) {
                console.error("设置头像数据失败:", error);
                onComplete();
            }
        }
        else {
            console.warn("⚠️ 找不到PlayerGameController组件");
            onComplete();
        }
    };
    // 获取默认头像URL
    HexChessBoardController.prototype.getDefaultAvatarUrl = function () {
        // 使用真实的头像URL
        return "https://static.gooplay.com/online/user-avatar/1732786296530322669.jpg";
    };
    /**
     * 播放头像生成动画（由大变小，完全复制四边形棋盘控制器的逻辑）
     * @param playerNode 玩家节点
     */
    HexChessBoardController.prototype.playAvatarSpawnAnimation = function (playerNode) {
        if (!playerNode) {
            console.warn("播放生成动画失败：节点为空");
            return;
        }
        // 显示节点
        playerNode.active = true;
        // 设置初始缩放为1.5倍（比正常大）
        var originalScale = playerNode.scaleX;
        var startScale = originalScale * 1.5;
        playerNode.setScale(startScale);
        // 使用cc.Tween创建由大变小的缩放动画
        cc.tween(playerNode)
            .to(0.3, { scaleX: originalScale, scaleY: originalScale }, { easing: 'backOut' })
            .start();
    };
    // 清除指定六边形格子的玩家
    HexChessBoardController.prototype.clearHexGridPlayer = function (q, r) {
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (!gridData || !gridData.hasPlayer) {
            return false;
        }
        // 移除玩家节点
        if (gridData.playerNode) {
            gridData.playerNode.removeFromParent();
            gridData.playerNode = null;
        }
        // 更新数据
        gridData.hasPlayer = false;
        return true;
    };
    // 清除所有玩家
    HexChessBoardController.prototype.clearAllPlayers = function () {
        var clearedCount = 0;
        // 1. 清理存储在hexGridData中的玩家节点（自己的头像）
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer && gridData.playerNode) {
                gridData.playerNode.removeFromParent();
                gridData.playerNode = null;
                gridData.hasPlayer = false;
                clearedCount++;
            }
        });
        // 2. 清理棋盘上的其他玩家头像节点
        if (this.boardNode) {
            var children = this.boardNode.children.slice(); // 创建副本避免遍历时修改数组
            for (var i = 0; i < children.length; i++) {
                var child = children[i];
                if (child.name === "player_game_pfb") {
                    // 检查是否有PlayerGameController组件
                    var playerController = child.getComponent(PlayerGameController_1.default);
                    if (playerController) {
                        child.removeFromParent();
                        clearedCount++;
                    }
                }
            }
        }
    };
    /**
     * 清理所有玩家预制体（新回合开始时调用）
     * 包括自己的头像和其他玩家的头像
     * 为了与四边形棋盘控制器保持一致的接口
     */
    HexChessBoardController.prototype.clearAllPlayerNodes = function () {
        this.clearAllPlayers();
    };
    // 获取所有已放置玩家的六边形坐标
    HexChessBoardController.prototype.getAllPlayerHexCoordinates = function () {
        var coordinates = [];
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer) {
                coordinates.push({ q: gridData.q, r: gridData.r });
            }
        });
        return coordinates;
    };
    // 检查六边形格子是否为空
    HexChessBoardController.prototype.isHexGridEmpty = function (q, r) {
        if (!this.isValidHexCoordinate(q, r)) {
            return false;
        }
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        return gridData ? !gridData.hasPlayer : false;
    };
    /**
     * 重置游戏场景（游戏开始时调用）
     * 清除数字、炸弹、标记预制体，重新显示所有小格子
     */
    HexChessBoardController.prototype.resetGameScene = function () {
        if (!this.boardNode) {
            console.error("❌ 棋盘节点不存在，无法重置");
            return;
        }
        // 清除所有游戏元素（数字、炸弹、标记等）
        this.clearAllGameElements();
        // 显示所有小格子
        this.showAllHexGrids();
        // 重新初始化棋盘数据
        this.reinitializeHexBoardData();
    };
    /**
     * 清除所有游戏元素（数字、炸弹、标记、玩家头像等），但保留小格子
     */
    HexChessBoardController.prototype.clearAllGameElements = function () {
        if (!this.boardNode) {
            return;
        }
        var childrenToRemove = [];
        // 遍历棋盘的所有子节点
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            var nodeName = child.name;
            // 检查是否是需要清除的游戏元素（不包括小格子）
            if (this.isGameElement(child, nodeName)) {
                childrenToRemove.push(child);
            }
        }
        // 移除找到的游戏元素
        childrenToRemove.forEach(function (child) {
            child.removeFromParent();
        });
    };
    /**
     * 判断节点是否是游戏元素（需要清除的），小格子不会被清除
     */
    HexChessBoardController.prototype.isGameElement = function (node, nodeName) {
        // 绝对不清除的节点（六边形小格子）
        if (nodeName.startsWith("HexGrid_") || nodeName === "hexblock") {
            return false;
        }
        // 分数控制器不清除
        if (nodeName.includes("Score") || nodeName.includes("score")) {
            return false;
        }
        // UI相关节点不清除
        if (nodeName.includes("UI") || nodeName.includes("ui")) {
            return false;
        }
        // 明确需要清除的游戏预制体
        // 炸弹预制体
        if (nodeName === "Boom") {
            return true;
        }
        // 数字预制体（Boom1, Boom2, Boom3 等）
        if (nodeName.match(/^Boom\d+$/)) {
            return true;
        }
        // 临时数字节点（NeighborMines_1, NeighborMines_2 等）
        if (nodeName.match(/^NeighborMines_\d+$/)) {
            return true;
        }
        // 测试节点（Test_q_r 格式）
        if (nodeName.match(/^Test_-?\d+_-?\d+$/)) {
            return true;
        }
        // 玩家预制体（通过组件判断）
        if (node.getComponent(PlayerGameController_1.default)) {
            return true;
        }
        // 标记预制体
        if (nodeName.includes("Flag") || nodeName.includes("Mark") || nodeName.includes("flag") ||
            nodeName === "Biaoji" || nodeName.includes("Biaoji")) {
            return true;
        }
        // 玩家头像预制体
        if (nodeName.includes("Player") || nodeName.includes("Avatar")) {
            return true;
        }
        // 默认保留未知节点（保守策略）
        return false;
    };
    /**
     * 显示所有六边形小格子（第二把游戏开始时恢复被隐藏的小格子）
     */
    HexChessBoardController.prototype.showAllHexGrids = function () {
        if (!this.boardNode) {
            return;
        }
        // 遍历棋盘的所有子节点，找到小格子并显示
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 如果是六边形小格子节点
            if (child.name.startsWith("HexGrid_") || child.name === "hexblock") {
                // 停止所有可能正在进行的动画
                child.stopAllActions();
                // 恢复显示状态
                child.active = true;
                child.opacity = 255;
                child.scaleX = 1;
                child.scaleY = 1;
                // 确保格子可以交互
                var button = child.getComponent(cc.Button);
                if (button) {
                    button.enabled = true;
                }
            }
        }
    };
    /**
     * 隐藏指定位置的六边形小格子（点击时调用）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param immediate 是否立即隐藏（不播放动画）
     */
    HexChessBoardController.prototype.hideHexGridAt = function (q, r, immediate) {
        if (immediate === void 0) { immediate = false; }
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + q + ", " + r + ")\u65E0\u6548");
            return;
        }
        // 获取格子节点
        var key = this.getHexKey(q, r);
        var gridNode = this.hexGridNodes.get(key);
        if (gridNode) {
            if (immediate) {
                // 立即隐藏，不播放动画
                gridNode.active = false;
            }
            else {
                // 使用动画隐藏格子
                cc.tween(gridNode)
                    .to(0.3, { opacity: 0, scaleX: 0, scaleY: 0 }, { easing: 'sineIn' })
                    .call(function () {
                    gridNode.active = false;
                })
                    .start();
            }
        }
    };
    /**
     * 重新初始化六边形棋盘数据
     */
    HexChessBoardController.prototype.reinitializeHexBoardData = function () {
        // 重置hexGridData中的玩家状态
        this.hexGridData.forEach(function (gridData) {
            gridData.hasPlayer = false;
            gridData.playerNode = null;
        });
    };
    /**
     * 获取六边形格子数据
     */
    HexChessBoardController.prototype.getHexGridData = function (q, r) {
        var key = this.getHexKey(q, r);
        return this.hexGridData.get(key) || null;
    };
    /**
     * 批量放置玩家（用于从服务器同步数据）
     */
    HexChessBoardController.prototype.batchPlaceHexPlayers = function (coordinates) {
        var _this = this;
        coordinates.forEach(function (coord) {
            if (_this.isValidHexCoordinate(coord.q, coord.r) && _this.isHexGridEmpty(coord.q, coord.r)) {
                _this.placePlayerOnHexGrid(coord.q, coord.r);
            }
        });
    };
    /**
     * 测试点击功能（调试用）
     */
    HexChessBoardController.prototype.testHexClick = function (q, r) {
        this.onHexGridClick(q, r);
    };
    /**
     * 获取棋盘状态信息（调试用）
     */
    HexChessBoardController.prototype.getHexBoardInfo = function () {
        var info = {
            validHexCoordsCount: this.validHexCoords.length,
            boardNodeChildren: this.boardNode ? this.boardNode.children.length : 0,
            playerCount: this.getAllPlayerHexCoordinates().length,
            hasPlayerGamePrefab: !!this.playerGamePrefab,
            hasBoardNode: !!this.boardNode,
            hexGridDataSize: this.hexGridData.size,
            hexGridNodesSize: this.hexGridNodes.size
        };
        return info;
    };
    /**
     * 获取前端节点的总数量（用于计算炸弹数量）
     */
    HexChessBoardController.prototype.getHexGridCount = function () {
        return this.validHexCoords.length;
    };
    /**
     * 根据前端节点数量计算推荐的炸弹数量
     */
    HexChessBoardController.prototype.getRecommendedMineCount = function () {
        var gridCount = this.getHexGridCount();
        if (gridCount === 0) {
            return 13; // 默认值
        }
        // 约15%的格子是炸弹
        var mineCount = Math.floor(gridCount * 0.15);
        return Math.max(mineCount, 5); // 至少5个炸弹
    };
    /**
     * 测试六边形预制体位置计算是否正确
     */
    HexChessBoardController.prototype.testHexPositionCalculation = function () {
        var _this = this;
        // 测试更新后的基准点坐标
        var testPoints = [
            { q: 0, r: 0, expected: { x: -300, y: -258 }, desc: "r=0行基准点(0,0)" },
            { q: 1, r: -1, expected: { x: -258, y: -184 }, desc: "r=-1行基准点(1,-1)" },
            { q: 1, r: -2, expected: { x: -300, y: -108 }, desc: "r=-2行基准点(1,-2)" },
            { q: 2, r: -3, expected: { x: -258, y: -36 }, desc: "r=-3行基准点(2,-3)" },
            { q: 2, r: -4, expected: { x: -300, y: 37 }, desc: "r=-4行基准点(2,-4)" },
            { q: 3, r: -5, expected: { x: -258, y: 110 }, desc: "r=-5行基准点(3,-5)" },
            { q: 3, r: -6, expected: { x: -300, y: 185 }, desc: "r=-6行基准点(3,-6)" },
            { q: 4, r: -7, expected: { x: -258, y: 260 }, desc: "r=-7行基准点(4,-7)" }
        ];
        var correctCount = 0;
        testPoints.forEach(function (point) {
            var calculated = _this.getHexWorldPosition(point.q, point.r);
            var errorX = Math.abs(calculated.x - point.expected.x);
            var errorY = Math.abs(calculated.y - point.expected.y);
            var isCorrect = errorX < 2 && errorY < 2; // 允许2像素误差
            if (isCorrect)
                correctCount++;
        });
        // 测试一些中间坐标
        var intermediatePoints = [
            // r=0行测试
            { q: 2, r: 0 }, { q: 3, r: 0 }, { q: 4, r: 0 }, { q: 5, r: 0 }, { q: 6, r: 0 },
            // r=-1行测试
            { q: 3, r: -1 }, { q: 4, r: -1 }, { q: 5, r: -1 }, { q: 6, r: -1 },
            // r=-2行测试
            { q: 3, r: -2 }, { q: 4, r: -2 }, { q: 5, r: -2 }, { q: 6, r: -2 }, { q: 7, r: -2 },
            // r=-3行测试
            { q: 4, r: -3 }, { q: 5, r: -3 }, { q: 6, r: -3 }, { q: 7, r: -3 }
        ];
        intermediatePoints.forEach(function (point) {
            var calculated = _this.getHexWorldPosition(point.q, point.r);
        });
        // 暴露到全局以便调试
        window.testHexPositions = function () { return _this.testHexPositionCalculation(); };
    };
    // ==================== NoticeActionDisplay 相关方法 ====================
    // 以下方法与第一张地图（方形地图）的逻辑完全一样，用于处理加分和掀开地图
    /**
     * 在指定六边形位置创建boom预制体
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param isCurrentUser 是否是当前用户点到的雷
     */
    HexChessBoardController.prototype.createHexBoomPrefab = function (q, r, isCurrentUser) {
        if (isCurrentUser === void 0) { isCurrentUser = true; }
        if (!this.boomPrefab) {
            console.error("boomPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化boom预制体
        var boomNode = cc.instantiate(this.boomPrefab);
        boomNode.name = "HexBoom";
        // 设置位置（使用六边形坐标计算，不是单人头像所以不偏移）
        var position = this.getHexWorldPosition(q, r, false);
        boomNode.setPosition(position);
        // 添加到棋盘
        this.boardNode.addChild(boomNode);
        // 播放出现动画
        boomNode.setScale(0);
        cc.tween(boomNode)
            .to(0.3, { scaleX: 1.2, scaleY: 1.2 }, { easing: 'backOut' })
            .to(0.1, { scaleX: 1.0, scaleY: 1.0 })
            .start();
        // 只有当前用户点到雷时才播放棋盘震动效果
        if (isCurrentUser) {
            this.playBoardShakeAnimation();
        }
    };
    /**
     * 在指定六边形位置创建biaoji预制体
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     */
    HexChessBoardController.prototype.createHexBiaojiPrefab = function (q, r) {
        if (!this.biaojiPrefab) {
            console.error("biaojiPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化biaoji预制体
        var biaojiNode = cc.instantiate(this.biaojiPrefab);
        biaojiNode.name = "HexBiaoji";
        // 设置位置（使用六边形坐标计算，不是单人头像所以不偏移）
        var position = this.getHexWorldPosition(q, r, false);
        biaojiNode.setPosition(position);
        // 添加到棋盘
        this.boardNode.addChild(biaojiNode);
        // 播放出现动画
        biaojiNode.setScale(0);
        cc.tween(biaojiNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 更新指定六边形位置的neighborMines显示
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param neighborMines 周围地雷数量
     */
    HexChessBoardController.prototype.updateHexNeighborMinesDisplay = function (q, r, neighborMines) {
        // 0不需要显示数字
        if (neighborMines === 0) {
            return;
        }
        // 直接使用boom数字预制体
        this.createHexNumberPrefab(q, r, neighborMines);
    };
    /**
     * 创建六边形数字预制体（boom1, boom2, ...）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param number 数字
     */
    HexChessBoardController.prototype.createHexNumberPrefab = function (q, r, number) {
        // 根据数字选择对应的预制体
        var prefab = null;
        switch (number) {
            case 1:
                prefab = this.boom1Prefab;
                break;
            case 2:
                prefab = this.boom2Prefab;
                break;
            case 3:
                prefab = this.boom3Prefab;
                break;
            case 4:
                prefab = this.boom4Prefab;
                break;
            case 5:
                prefab = this.boom5Prefab;
                break;
            case 6:
                prefab = this.boom6Prefab;
                break;
            case 7:
                prefab = this.boom7Prefab;
                break;
            case 8:
                prefab = this.boom8Prefab;
                break;
            default:
                console.error("\u4E0D\u652F\u6301\u7684\u6570\u5B57: " + number);
                return;
        }
        if (!prefab) {
            console.error("boom" + number + "Prefab \u9884\u5236\u4F53\u672A\u8BBE\u7F6E\uFF0C\u8BF7\u5728\u7F16\u8F91\u5668\u4E2D\u6302\u8F7D");
            return;
        }
        // 实例化数字预制体
        var numberNode = cc.instantiate(prefab);
        numberNode.name = "HexBoom" + number;
        // 设置位置（使用六边形坐标计算，不是单人头像所以不偏移）
        var position = this.getHexWorldPosition(q, r, false);
        numberNode.setPosition(position);
        // 添加到棋盘
        this.boardNode.addChild(numberNode);
        // 播放出现动画
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 播放棋盘震动动画（当前用户点到雷时）
     */
    HexChessBoardController.prototype.playBoardShakeAnimation = function () {
        if (!this.boardNode) {
            return;
        }
        var originalPosition = this.boardNode.getPosition();
        var shakeDistance = 10;
        cc.tween(this.boardNode)
            .to(0.05, { x: originalPosition.x + shakeDistance })
            .to(0.05, { x: originalPosition.x - shakeDistance })
            .to(0.05, { x: originalPosition.x + shakeDistance })
            .to(0.05, { x: originalPosition.x - shakeDistance })
            .to(0.05, { x: originalPosition.x })
            .start();
    };
    // ==================== 头像生命周期管理 ====================
    // 以下方法与第一张地图的逻辑完全一样，用于管理头像预制体的生命周期
    /**
     * 让所有六边形头像消失（和第一张地图的hideAvatarsAtPosition逻辑一样）
     * @param onComplete 完成回调
     */
    HexChessBoardController.prototype.hideAllHexAvatars = function (onComplete) {
        var _this = this;
        if (!this.boardNode) {
            console.warn("棋盘节点不存在，无法清理六边形头像");
            onComplete();
            return;
        }
        // 收集所有头像节点（参考第一张地图的逻辑）
        var avatarNodes = [];
        // 方法1: 收集存储在hexGridData中的玩家节点（自己的头像）
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer && gridData.playerNode) {
                avatarNodes.push(gridData.playerNode);
            }
        });
        // 方法2: 收集棋盘上所有的玩家预制体节点（包括其他玩家的头像）
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 检查是否是玩家预制体（通过组件判断）
            var playerController = child.getComponent(PlayerGameController_1.default);
            if (playerController) {
                // 避免重复添加（可能已经在方法1中添加过）
                if (!avatarNodes.includes(child)) {
                    avatarNodes.push(child);
                }
            }
        }
        // 如果没有头像，直接执行回调
        if (avatarNodes.length === 0) {
            this.clearAllMyHexAvatarReferences();
            onComplete();
            return;
        }
        var completedCount = 0;
        var totalCount = avatarNodes.length;
        // 为每个头像播放消失动画（和第一张地图完全一样）
        avatarNodes.forEach(function (avatarNode) {
            // 使用cc.Tween播放消失动画
            cc.tween(avatarNode)
                .to(0.3, { opacity: 0, scaleX: 0.5, scaleY: 0.5 }, { easing: 'sineIn' })
                .call(function () {
                // 动画完成后移除节点
                avatarNode.removeFromParent();
                completedCount++;
                // 所有头像都消失完成后，执行回调
                if (completedCount >= totalCount) {
                    // 清除所有自己头像的引用
                    _this.clearAllMyHexAvatarReferences();
                    onComplete();
                }
            })
                .start();
        });
    };
    /**
     * 清除所有自己六边形头像的引用（和第一张地图的clearAllMyAvatarReferences逻辑一样）
     */
    HexChessBoardController.prototype.clearAllMyHexAvatarReferences = function () {
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer) {
                gridData.hasPlayer = false;
                gridData.playerNode = null;
            }
        });
    };
    // ==================== 加分逻辑相关方法 ====================
    // 以下方法与第一张地图的加分逻辑完全一样
    /**
     * 在指定六边形位置的玩家节点上显示分数
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param score 分数
     * @param showPlusOne 是否显示+1（先手奖励）
     */
    HexChessBoardController.prototype.showScoreOnHexPlayerNode = function (q, r, score, showPlusOne) {
        var _this = this;
        // 查找该位置的玩家节点
        var playerNode = this.findHexPlayerNodeAtPosition(q, r);
        if (!playerNode) {
            // 在NoticeActionDisplay流程中，头像会被清理，找不到节点是正常的
            return;
        }
        // 获取PlayerGameController组件
        var playerController = playerNode.getComponent(PlayerGameController_1.default);
        if (!playerController) {
            console.warn("找不到PlayerGameController组件");
            return;
        }
        // 显示分数动画
        if (showPlusOne) {
            // 先显示+1，再显示本回合得分
            this.showScoreAnimationOnHexNode(playerController, 1, function () {
                _this.scheduleOnce(function () {
                    _this.showScoreAnimationOnHexNode(playerController, score, null);
                }, 1.0);
            });
        }
        else {
            // 只显示本回合得分
            this.showScoreAnimationOnHexNode(playerController, score, null);
        }
    };
    /**
     * 查找指定六边形位置的玩家节点
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @returns 玩家节点或null
     */
    HexChessBoardController.prototype.findHexPlayerNodeAtPosition = function (q, r) {
        // 方法1: 从hexGridData中查找（自己的头像）
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData && gridData.hasPlayer && gridData.playerNode) {
            return gridData.playerNode;
        }
        // 方法2: 在棋盘上查找其他玩家的头像
        if (!this.boardNode) {
            return null;
        }
        // 遍历棋盘上的所有子节点，查找player_game_pfb
        var children = this.boardNode.children;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            if (child.name === "player_game_pfb") {
                // 检查位置是否匹配（允许一定的误差）
                var expectedPos = this.getHexWorldPosition(q, r, true);
                var actualPos = child.getPosition();
                var distance = expectedPos.sub(actualPos).mag();
                if (distance < 10) { // 10像素误差范围内
                    return child;
                }
            }
        }
        return null;
    };
    /**
     * 在六边形节点上显示分数动画
     * @param playerController 玩家控制器
     * @param score 分数
     * @param onComplete 完成回调
     */
    HexChessBoardController.prototype.showScoreAnimationOnHexNode = function (playerController, score, onComplete) {
        // 调用PlayerGameController的showAddScore方法
        if (playerController && typeof playerController.showAddScore === 'function') {
            playerController.showAddScore(score);
        }
        if (onComplete) {
            this.scheduleOnce(onComplete, 1.0);
        }
    };
    /**
     * 显示玩家游戏加减分效果（完全复制四边形棋盘控制器的逻辑）
     * @param userId 用户ID
     * @param score 分数变化（正数为加分，负数为减分）
     */
    HexChessBoardController.prototype.showHexPlayerGameScore = function (userId, score) {
        var currentUserId = this.getCurrentHexUserId();
        var foundPlayer = false;
        // 1. 如果是当前用户，查找自己的玩家节点（存储在hexGridData中）
        if (userId === currentUserId) {
            foundPlayer = this.showScoreForCurrentHexUser(score);
        }
        else {
            // 2. 如果是其他用户，查找对应的玩家头像节点
            foundPlayer = this.showScoreForOtherHexUser(userId, score);
        }
        if (!foundPlayer) {
            console.warn("\u672A\u627E\u5230\u7528\u6237 " + userId + " \u7684\u516D\u8FB9\u5F62\u5934\u50CF\u8282\u70B9\u6765\u663E\u793A\u5206\u6570\u6548\u679C");
        }
    };
    /**
     * 获取当前用户ID（复制四边形棋盘控制器的方法）
     */
    HexChessBoardController.prototype.getCurrentHexUserId = function () {
        var _a, _b;
        return ((_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId) || "";
    };
    /**
     * 为当前用户显示分数效果（复制四边形棋盘控制器的逻辑）
     */
    HexChessBoardController.prototype.showScoreForCurrentHexUser = function (score) {
        var _this = this;
        var foundPlayer = false;
        this.hexGridData.forEach(function (gridData) {
            // 如果已经找到了，就不再继续查找
            if (foundPlayer) {
                return;
            }
            if (gridData.hasPlayer && gridData.playerNode) {
                var playerController = gridData.playerNode.getComponent("PlayerGameController") ||
                    gridData.playerNode.getComponent("PlayerGameController ");
                if (playerController) {
                    console.log("\u2705 \u627E\u5230\u5F53\u524D\u7528\u6237\u7684\u516D\u8FB9\u5F62\u5934\u50CF\u8282\u70B9\uFF0C\u663E\u793A\u5206\u6570: " + score);
                    _this.showScoreOnHexPlayerController(playerController, score);
                    foundPlayer = true;
                }
            }
        });
        if (!foundPlayer) {
            console.warn("❌ 未找到当前用户的六边形头像节点");
        }
        return foundPlayer;
    };
    /**
     * 为其他用户显示分数效果（复制四边形棋盘控制器的逻辑）
     */
    HexChessBoardController.prototype.showScoreForOtherHexUser = function (userId, score) {
        if (!this.boardNode) {
            return false;
        }
        // 遍历棋盘上的所有玩家头像节点
        return this.findHexPlayerNodeByUserId(userId, score);
    };
    /**
     * 根据userId查找对应的玩家节点（复制四边形棋盘控制器的逻辑）
     */
    HexChessBoardController.prototype.findHexPlayerNodeByUserId = function (userId, score) {
        if (!this.boardNode) {
            console.warn("\u68CB\u76D8\u8282\u70B9\u4E0D\u5B58\u5728\uFF0C\u65E0\u6CD5\u67E5\u627E\u7528\u6237 " + userId + " \u7684\u5934\u50CF");
            return false;
        }
        // 遍历棋盘上的所有玩家头像节点，根据存储的userId精确匹配
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 尝试多种方式获取PlayerGameController组件
            var playerController = child.getComponent("PlayerGameController");
            if (!playerController) {
                playerController = child.getComponent("PlayerGameController "); // 注意末尾有空格
            }
            if (!playerController) {
                // 尝试通过类名获取
                var components = child.getComponents(cc.Component);
                playerController = components.find(function (comp) {
                    return comp.constructor.name === 'PlayerGameController' ||
                        comp.constructor.name === 'PlayerGameController ';
                });
            }
            var storedUserId = child['userId'];
            if (storedUserId === userId) {
                if (playerController) {
                    // 找到匹配的用户ID和组件，显示分数效果
                    this.showScoreOnHexPlayerController(playerController, score);
                    return true;
                }
                else {
                    // 找到匹配的用户ID但没有组件
                    console.warn("\u26A0\uFE0F \u627E\u5230\u7528\u6237 " + userId + " \u7684\u8282\u70B9\u4F46\u6CA1\u6709PlayerGameController\u7EC4\u4EF6");
                    return false; // 找到节点但没有组件，返回false
                }
            }
        }
        console.warn("\u274C \u672A\u627E\u5230\u7528\u6237 " + userId + " \u7684\u516D\u8FB9\u5F62\u5934\u50CF\u8282\u70B9");
        return false;
    };
    /**
     * 在PlayerController上显示分数效果（复制四边形棋盘控制器的逻辑）
     */
    HexChessBoardController.prototype.showScoreOnHexPlayerController = function (playerController, score) {
        // 临时提升节点层级，避免被其他头像遮挡
        var playerNode = playerController.node;
        var originalSiblingIndex = playerNode.getSiblingIndex();
        // 将节点移到最上层
        playerNode.setSiblingIndex(-1);
        if (score > 0) {
            playerController.showAddScore(score);
        }
        else if (score < 0) {
            playerController.showSubScore(Math.abs(score));
        }
        // 延迟恢复原始层级（等分数动画播放完成）
        this.scheduleOnce(function () {
            if (playerNode && playerNode.isValid) {
                playerNode.setSiblingIndex(originalSiblingIndex);
            }
        }, 2.0); // 2秒后恢复，根据分数动画时长调整
    };
    /**
     * 查找指定用户ID的所有六边形头像节点
     * @param userId 用户ID
     * @returns 头像节点数组
     */
    HexChessBoardController.prototype.findAllHexPlayerNodesByUserId = function (userId) {
        var playerNodes = [];
        if (!this.boardNode) {
            return playerNodes;
        }
        // 遍历棋盘上的所有子节点
        var children = this.boardNode.children;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            // 检查是否是玩家预制体（通过组件判断）
            var playerController = child.getComponent(PlayerGameController_1.default);
            if (playerController) {
                // 检查是否是指定用户的头像（使用存储在节点上的userId）
                var storedUserId = child['userId'];
                if (storedUserId === userId) {
                    playerNodes.push(child);
                }
            }
        }
        // 也检查存储在hexGridData中的玩家节点
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer && gridData.playerNode) {
                var playerController = gridData.playerNode.getComponent(PlayerGameController_1.default);
                var storedUserId = gridData.playerNode['userId'];
                if (playerController && storedUserId === userId) {
                    // 避免重复添加
                    if (!playerNodes.includes(gridData.playerNode)) {
                        playerNodes.push(gridData.playerNode);
                    }
                }
            }
        });
        return playerNodes;
    };
    // ==================== 其他玩家头像生成 ====================
    // 以下方法与第一张地图的逻辑完全一样，用于生成其他玩家的头像
    /**
     * 在指定六边形位置显示其他玩家的操作（完全复制四边形棋盘控制器的逻辑）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param actions 该位置的其他玩家操作列表
     */
    HexChessBoardController.prototype.displayOtherPlayersAtHexPosition = function (q, r, actions) {
        if (!this.isValidHexCoordinate(q, r) || !actions || actions.length === 0) {
            console.warn("\u65E0\u6548\u53C2\u6570: (" + q + ", " + r + "), actions: " + ((actions === null || actions === void 0 ? void 0 : actions.length) || 0));
            return;
        }
        // 检查该位置是否已经有自己的头像
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData && gridData.hasPlayer) {
            // 只有当真的有其他玩家时，才调整自己的头像位置
            if (actions.length > 0) {
                // 如果已有自己的头像且有其他玩家，需要使用多人布局策略
                this.addOtherPlayersToExistingHexGrid(q, r, actions);
            }
        }
        else {
            // 如果没有自己的头像，直接添加其他玩家头像
            this.addOtherPlayersToEmptyHexGrid(q, r, actions);
        }
    };
    /**
     * 在已有自己头像的六边形格子上添加其他玩家头像，并调整自己的头像位置和缩放
     * （完全复制四边形棋盘控制器的逻辑）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param actions 其他玩家操作列表
     */
    HexChessBoardController.prototype.addOtherPlayersToExistingHexGrid = function (q, r, actions) {
        // 总玩家数 = 自己(1) + 其他玩家数量
        var totalPlayers = 1 + actions.length;
        var positions = this.getHexPlayerPositions(totalPlayers);
        // 第一步：调整自己的头像位置和缩放
        var myPosition = positions[0]; // 第一个位置是自己的
        this.adjustMyHexAvatarPosition(q, r, myPosition, actions);
        // 第二步：从第二个位置开始放置其他玩家
        for (var i = 0; i < actions.length; i++) {
            var action = actions[i];
            var position = positions[i + 1]; // 跳过第一个位置（自己的位置）
            // 使用六边形坐标系创建其他玩家头像
            this.createOtherPlayerAtHexPosition(q, r, action, position, totalPlayers);
        }
    };
    /**
     * 在空六边形格子上添加其他玩家头像
     * （完全复制四边形棋盘控制器的逻辑）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param actions 其他玩家操作列表
     */
    HexChessBoardController.prototype.addOtherPlayersToEmptyHexGrid = function (q, r, actions) {
        var totalPlayers = actions.length; // 空格子上只有其他玩家
        var positions = this.getHexPlayerPositions(totalPlayers);
        for (var i = 0; i < actions.length; i++) {
            var action = actions[i];
            var position = positions[i];
            // 使用六边形坐标系创建其他玩家头像
            this.createOtherPlayerAtHexPosition(q, r, action, position, totalPlayers);
        }
    };
    /**
     * 根据玩家数量获取六边形布局位置（完全复制四边形棋盘控制器的逻辑）
     * @param playerCount 玩家数量
     * @returns 位置数组 {x: number, y: number, scale: number}[]
     */
    HexChessBoardController.prototype.getHexPlayerPositions = function (playerCount) {
        switch (playerCount) {
            case 1:
                // 单个玩家，居中显示，正常大小
                return [{ x: 0, y: 0, scale: 1.0 }];
            case 2:
                // 两个玩家，左右分布，缩放0.5
                return [
                    { x: -22, y: -8, scale: 0.5 },
                    { x: 22, y: -8, scale: 0.5 } // 右
                ];
            case 3:
                // 三个玩家，上中下分布，缩放0.5
                return [
                    { x: 0, y: 12, scale: 0.5 },
                    { x: -23, y: -27, scale: 0.5 },
                    { x: 23, y: -27, scale: 0.5 } // 右下
                ];
            case 4:
                // 四个玩家，四角分布，缩放0.5
                return [
                    { x: -22, y: 12, scale: 0.5 },
                    { x: 22, y: 12, scale: 0.5 },
                    { x: -22, y: -30, scale: 0.5 },
                    { x: 22, y: -30, scale: 0.5 } // 右下
                ];
            default:
                // 超过4个玩家，只显示前4个
                console.warn("\u73A9\u5BB6\u6570\u91CF\u8FC7\u591A: " + playerCount + "\uFF0C\u53EA\u663E\u793A\u524D4\u4E2A");
                return this.getHexPlayerPositions(4);
        }
    };
    /**
     * 调整自己的六边形头像位置和缩放（当多人在同一格子时）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param position 新的位置和缩放信息
     * @param actions 其他玩家操作列表
     */
    HexChessBoardController.prototype.adjustMyHexAvatarPosition = function (q, r, position, actions) {
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        // 查找自己的头像节点
        if (!gridData || !gridData.hasPlayer || !gridData.playerNode) {
            console.warn("\u5728\u516D\u8FB9\u5F62\u4F4D\u7F6E(" + q + ", " + r + ")\u627E\u4E0D\u5230\u81EA\u5DF1\u7684\u5934\u50CF\u8282\u70B9");
            return;
        }
        var myPlayerNode = gridData.playerNode;
        // 计算该格子的总人数（自己 + 其他玩家）
        var totalPlayers = 1 + (actions ? actions.length : 0);
        // 计算基础位置（根据总人数决定是否偏移）
        var basePosition = this.calculateHexBasePositionByPlayerCount(q, r, totalPlayers);
        // 计算新的最终位置
        var newPosition = cc.v2(basePosition.x + position.x, basePosition.y + position.y);
        // 播放平滑移动和缩放动画
        this.playHexAvatarAdjustAnimation(myPlayerNode, newPosition, position.scale);
    };
    /**
     * 根据六边形格子总人数计算基础位置（完全复制四边形棋盘控制器的逻辑）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param totalPlayers 该格子的总人数
     * @returns 基础位置
     */
    HexChessBoardController.prototype.calculateHexBasePositionByPlayerCount = function (q, r, totalPlayers) {
        if (totalPlayers === 1) {
            // 一个格子里只有一个人：使用正常的偏移（单人头像预制体，y轴+20）
            return this.getHexWorldPosition(q, r, true);
        }
        else {
            // 一个格子里有两个及以上：不偏移（多人头像预制体，不偏移）
            return this.getHexWorldPosition(q, r, false);
        }
    };
    /**
     * 播放六边形头像调整动画（完全复制四边形棋盘控制器的逻辑）
     * @param playerNode 玩家节点
     * @param newPosition 新位置
     * @param newScale 新缩放
     */
    HexChessBoardController.prototype.playHexAvatarAdjustAnimation = function (playerNode, newPosition, newScale) {
        if (!playerNode || !playerNode.isValid) {
            return;
        }
        // 停止之前的动画
        playerNode.stopAllActions();
        // 使用cc.tween播放位置和缩放动画
        cc.tween(playerNode)
            .to(0.3, {
            x: newPosition.x,
            y: newPosition.y,
            scaleX: newScale,
            scaleY: newScale
        }, { easing: 'sineOut' })
            .start();
    };
    /**
     * 创建其他玩家在六边形位置的头像
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param action 玩家操作数据
     * @param position 位置和缩放信息
     * @param totalPlayers 总玩家数
     */
    HexChessBoardController.prototype.createOtherPlayerAtHexPosition = function (q, r, action, position, totalPlayers) {
        var _this = this;
        if (!this.playerGamePrefab || !this.boardNode) {
            console.error("❌ 预制体或棋盘节点未设置！");
            return;
        }
        // 实例化玩家预制体
        var playerNode = cc.instantiate(this.playerGamePrefab);
        // 计算基础位置（根据总人数决定是否偏移）
        var basePosition = this.calculateHexBasePositionByPlayerCount(q, r, totalPlayers);
        // 计算最终位置
        var finalPosition = cc.v2(basePosition.x + position.x, basePosition.y + position.y);
        playerNode.setPosition(finalPosition);
        // 根据总人数设置缩放：单人0.8，多人使用position.scale
        if (totalPlayers === 1) {
            playerNode.setScale(0.8);
        }
        else {
            playerNode.setScale(position.scale);
        }
        // 先隐藏节点，等头像加载完成后再显示
        playerNode.active = false;
        // 添加到棋盘
        this.addPlayerNodeSafely(playerNode);
        // 设置其他玩家的头像和数据
        this.setupOtherPlayerHexAvatar(playerNode, action, function () {
            // 头像加载完成的回调，播放生成动画
            _this.playAvatarSpawnAnimation(playerNode);
        });
    };
    /**
     * 设置其他玩家的六边形头像和数据
     * @param playerNode 玩家节点
     * @param action 玩家操作数据
     * @param onComplete 完成回调
     */
    HexChessBoardController.prototype.setupOtherPlayerHexAvatar = function (playerNode, action, onComplete) {
        // 查找PlayerGameController组件
        var playerController = playerNode.getComponent(PlayerGameController_1.default);
        if (playerController) {
            // 在节点上存储userId，用于后续查找
            playerNode['userId'] = action.userId;
            // 设置旗子节点的显示状态
            var withFlag_1 = (action.action === 2); // action=2表示标记操作，显示旗子
            if (playerController.flagNode) {
                playerController.flagNode.active = withFlag_1;
            }
            // 获取真实的用户数据（和第一张地图逻辑一样）
            var realUserData = this.getRealUserData(action.userId);
            if (!realUserData) {
                console.warn("\u627E\u4E0D\u5230\u7528\u6237 " + action.userId + " \u7684\u771F\u5B9E\u6570\u636E\uFF0C\u4F7F\u7528\u9ED8\u8BA4\u6570\u636E");
                // 使用默认数据作为备选
                realUserData = {
                    userId: action.userId,
                    nickName: "\u73A9\u5BB6" + action.userId,
                    avatar: this.getDefaultAvatarUrl(),
                    score: 0,
                    pos: 0,
                    coin: 0,
                    status: 0,
                    rank: 0
                };
            }
            // 使用PlayerGameController的setData方法来设置头像
            try {
                playerController.setData(realUserData);
                // 延迟设置旗子状态，确保在PlayerGameController初始化之后
                this.scheduleOnce(function () {
                    if (playerController.flagNode) {
                        playerController.flagNode.active = withFlag_1;
                    }
                    onComplete();
                }, 0.1);
            }
            catch (error) {
                console.error("设置其他玩家头像数据失败:", error);
                onComplete();
            }
        }
        else {
            console.warn("⚠️ 找不到PlayerGameController组件");
            onComplete();
        }
    };
    /**
     * 获取其他玩家的头像URL
     * @param userId 用户ID
     * @returns 头像URL
     */
    HexChessBoardController.prototype.getOtherPlayerAvatarUrl = function (userId) {
        // 这里可以根据userId获取真实的头像URL
        // 暂时使用默认头像
        return this.getDefaultAvatarUrl();
    };
    /**
     * 从GlobalBean中获取真实的用户数据（和第一张地图逻辑完全一样）
     * @param userId 用户ID
     * @returns RoomUser 或 null
     */
    HexChessBoardController.prototype.getRealUserData = function (userId) {
        try {
            if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
                console.warn("没有游戏数据，无法获取用户信息");
                return null;
            }
            var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
            var user = users.find(function (u) { return u.userId === userId; });
            if (user) {
                return user;
            }
            else {
                console.warn("\u672A\u627E\u5230\u7528\u6237 " + userId + " \u7684\u6570\u636E");
                return null;
            }
        }
        catch (error) {
            console.error("\u83B7\u53D6\u7528\u6237\u6570\u636E\u65F6\u51FA\u9519: " + error);
            return null;
        }
    };
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "playerGamePrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boomPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "biaojiPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom1Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom2Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom3Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom4Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom5Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom6Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom7Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom8Prefab", void 0);
    __decorate([
        property(cc.Node)
    ], HexChessBoardController.prototype, "boardNode", void 0);
    HexChessBoardController = __decorate([
        ccclass
    ], HexChessBoardController);
    return HexChessBoardController;
}(cc.Component));
exports.default = HexChessBoardController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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