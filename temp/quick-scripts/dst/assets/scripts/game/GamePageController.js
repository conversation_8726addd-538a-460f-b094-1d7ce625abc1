
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/GamePageController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'ae7d7j8qCJHEr/tVZKmu8hm', 'GamePageController');
// scripts/game/GamePageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../bean/GlobalBean");
var LeaveDialogController_1 = require("../hall/LeaveDialogController");
var AudioManager_1 = require("../util/AudioManager");
var Config_1 = require("../util/Config");
var Tools_1 = require("../util/Tools");
var CongratsDialogController_1 = require("./CongratsDialogController");
var GameScoreController_1 = require("./GameScoreController");
var ChessBoardController_1 = require("./Chess/ChessBoardController");
var HexChessBoardController_1 = require("./Chess/HexChessBoardController");
var PlayerGameController_1 = require("../pfb/PlayerGameController ");
var WebSocketManager_1 = require("../net/WebSocketManager");
var MessageId_1 = require("../net/MessageId");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var GamePageController = /** @class */ (function (_super) {
    __extends(GamePageController, _super);
    function GamePageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBtnBack = null; //返回按钮
        _this.timeLabel = null; // 计时器显示标签
        _this.mineCountLabel = null; // 炸弹数量显示标签
        _this.squareMapNode = null; // 方形地图节点 (mapType = 0)
        _this.hexMapNode = null; // 六边形地图节点 (mapType = 1)
        _this.leaveDialogController = null; // 退出游戏弹窗
        _this.congratsDialogController = null; //结算弹窗
        _this.gameScoreController = null; //分数控制器
        _this.chessBoardController = null; //方形棋盘控制器
        _this.hexChessBoardController = null; //六边形棋盘控制器
        _this.isLeaveGameDialogShow = false; //是否显示退出游戏的弹窗
        _this.isCongratsDialog = false; //是否显示结算的弹窗
        // 计时器相关属性
        _this.countdownInterval = null; // 倒计时定时器ID
        _this.currentCountdown = 0; // 当前倒计时秒数
        _this.currentRoundNumber = 0; // 当前回合编号
        // 游戏状态管理
        _this.canOperate = false; // 是否可以操作（在NoticeRoundStart和NoticeActionDisplay之间）
        _this.gameStatus = 0; // 游戏状态
        _this.hasOperatedThisRound = false; // 本回合是否已经操作过
        // 游戏数据
        _this.currentMapType = 0; // 当前地图类型 0-方形地图，1-六边形地图
        _this.currentMineCount = 0; // 当前炸弹数量
        // 当前NoticeActionDisplay数据，用于倒计时显示逻辑
        _this.currentNoticeActionData = null;
        return _this;
        // update (dt) {}
    }
    GamePageController.prototype.onLoad = function () {
        var _this = this;
        // 如果timeLabel没有在编辑器中设置，尝试通过路径查找
        if (!this.timeLabel) {
            // 根据场景结构查找time_label节点
            var timeBgNode = cc.find('Canvas/time_bg');
            if (timeBgNode) {
                var timeLabelNode = timeBgNode.getChildByName('time_label');
                if (timeLabelNode) {
                    this.timeLabel = timeLabelNode.getComponent(cc.Label);
                }
            }
        }
        // 如果mineCountLabel没有在编辑器中设置，尝试通过路径查找
        if (!this.mineCountLabel) {
            // 根据场景结构查找mine_count_label节点
            var mineCountBgNode = cc.find('Canvas/mine_count_bg');
            if (mineCountBgNode) {
                var mineCountLabelNode = mineCountBgNode.getChildByName('mine_count_label');
                if (mineCountLabelNode) {
                    this.mineCountLabel = mineCountLabelNode.getComponent(cc.Label);
                }
            }
        }
        // 将测试方法暴露到全局，方便调试
        window.testGameReset = function () {
            _this.testReset();
        };
        // 暴露 GamePageController 实例到全局
        window.gamePageController = this;
    };
    GamePageController.prototype.start = function () {
        var _this = this;
        Tools_1.Tools.imageButtonClick(this.boardBtnBack, Config_1.Config.buttonRes + 'side_btn_back_normal', Config_1.Config.buttonRes + 'side_btn_back_pressed', function () {
            _this.isLeaveGameDialogShow = true;
            _this.leaveDialogController.show(1, function () {
                _this.isLeaveGameDialogShow = false;
            });
        });
        // 监听棋盘点击事件
        if (this.chessBoardController) {
            this.chessBoardController.node.on('chess-board-click', this.onChessBoardClick, this);
        }
        // 监听六边形棋盘点击事件
        if (this.hexChessBoardController) {
            this.hexChessBoardController.node.on('hex-chess-board-click', this.onHexChessBoardClick, this);
        }
    };
    /**
     * 处理棋盘点击事件
     * @param event 事件数据 {x: number, y: number, action: number}
     */
    GamePageController.prototype.onChessBoardClick = function (event) {
        var _a = event.detail || event, x = _a.x, y = _a.y, action = _a.action;
        // 检查是否可以操作（在操作时间内）
        if (!this.isCanOperate()) {
            return;
        }
        // 检查本回合是否已经操作过
        if (this.hasOperatedThisRound) {
            return;
        }
        // 发送点击操作
        this.sendClickBlock(x, y, action);
        // 操作有效，通知棋盘生成预制体
        if (this.chessBoardController) {
            if (action === 1) {
                // 挖掘操作，生成不带旗子的预制体
                this.chessBoardController.placePlayerOnGrid(x, y, false);
            }
            else if (action === 2) {
                // 标记操作，生成带旗子的预制体
                this.chessBoardController.placePlayerOnGrid(x, y, true);
            }
        }
        // 标记本回合已经操作过，禁止后续交互
        this.hasOperatedThisRound = true;
    };
    /**
     * 处理六边形棋盘点击事件
     * @param event 事件数据 {q: number, r: number, action: number}
     */
    GamePageController.prototype.onHexChessBoardClick = function (event) {
        var _a = event.detail || event, q = _a.q, r = _a.r, action = _a.action;
        // 检查是否可以操作（在操作时间内）
        if (!this.isCanOperate()) {
            return;
        }
        // 检查本回合是否已经操作过
        if (this.hasOperatedThisRound) {
            return;
        }
        // 发送六边形点击操作（需要将六边形坐标转换为服务器期望的格式）
        this.sendHexClickBlock(q, r, action);
        // 操作有效，通知六边形棋盘生成预制体
        if (this.hexChessBoardController) {
            if (action === 1) {
                // 挖掘操作，生成不带旗子的预制体
                this.hexChessBoardController.placePlayerOnHexGrid(q, r, false);
            }
            else if (action === 2) {
                // 标记操作，生成带旗子的预制体
                this.hexChessBoardController.placePlayerOnHexGrid(q, r, true);
            }
        }
        // 标记本回合已经操作过，禁止后续交互
        this.hasOperatedThisRound = true;
    };
    //结算
    GamePageController.prototype.setCongratsDialog = function (noticeSettlement) {
        var _this = this;
        this.setCongrats(noticeSettlement);
        //退出弹窗正在显示的话  就先关闭
        if (this.isLeaveGameDialogShow) {
            this.leaveDialogController.hide();
        }
        this.isCongratsDialog = true;
        //弹出结算弹窗
        this.congratsDialogController.show(noticeSettlement, function () {
            _this.isCongratsDialog = false;
        });
    };
    GamePageController.prototype.onDisable = function () {
        //退出弹窗正在显示的话  就先关闭
        if (this.isLeaveGameDialogShow) {
            this.leaveDialogController.hide();
        }
        //结算弹窗正在显示的话就先关闭掉
        if (this.isCongratsDialog) {
            this.congratsDialogController.hide();
        }
        // 清理计时器
        this.clearCountdownTimer();
    };
    //结算
    GamePageController.prototype.setCongrats = function (noticeSettlement) {
        // 获取用户列表，优先使用 finalRanking，其次使用 users
        var userList = noticeSettlement.finalRanking || noticeSettlement.users;
        // 检查用户列表是否存在
        if (!noticeSettlement || !userList || !Array.isArray(userList)) {
            console.warn('NoticeSettlement 用户数据无效:', noticeSettlement);
            AudioManager_1.AudioManager.winAudio(); // 默认播放胜利音效
            return;
        }
        var currentUserId = GlobalBean_1.GlobalBean.GetInstance().loginData.userInfo.userId;
        var index = userList.findIndex(function (item) { return item.userId === currentUserId; }); //搜索
        if (index >= 0) { //自己参与的话 才会显示正常的胜利和失败的音效，自己不参与的话 就全部显示胜利的音效
            if (userList[index].rank === 1) { //判断自己是不是第一名
                AudioManager_1.AudioManager.winAudio();
            }
            else {
                AudioManager_1.AudioManager.loseAudio();
            }
        }
        else {
            AudioManager_1.AudioManager.winAudio();
        }
    };
    // 处理游戏开始通知，获取炸弹数量和地图类型
    GamePageController.prototype.onGameStart = function (data) {
        // 保存地图类型
        this.currentMapType = data.mapType || 0;
        // 根据地图类型重置对应的棋盘控制器
        if (this.currentMapType === 0) {
            // 方形地图
            if (this.chessBoardController) {
                this.chessBoardController.resetGameScene();
            }
            else {
                console.error("❌ chessBoardController 不存在！");
            }
        }
        else if (this.currentMapType === 1) {
            // 六边形地图
            if (this.hexChessBoardController) {
                this.hexChessBoardController.resetGameScene();
                // 忽略服务器的 validHexCoords，使用前端节点坐标
                this.hexChessBoardController.setValidHexCoords([]); // 传入空数组，会被忽略
            }
            else {
                console.error("❌ hexChessBoardController 不存在！");
            }
        }
        // 重置游戏状态
        this.canOperate = false;
        this.hasOperatedThisRound = false;
        this.currentRoundNumber = 0;
        this.currentCountdown = 0;
        this.gameStatus = 0;
        // 根据地图类型获取炸弹数量
        if (data.mapType === 0 && data.mapConfig) {
            // 方形地图
            this.currentMineCount = data.mapConfig.mineCount || 13;
        }
        else if (data.mapType === 1) {
            // 六边形地图，根据前端节点数量计算炸弹数量
            if (this.hexChessBoardController) {
                this.currentMineCount = this.hexChessBoardController.getRecommendedMineCount();
            }
            else {
                this.currentMineCount = 15; // 备用固定值
            }
        }
        else {
            // 默认值
            this.currentMineCount = 13;
        }
        // 更新炸弹数UI
        this.updateMineCountDisplay(this.currentMineCount);
        // 根据地图类型控制地图节点的显示与隐藏
        this.switchMapDisplay(this.currentMapType);
        // 初始化分数界面（使用后端传回来的真实数据）
        if (this.gameScoreController) {
            this.gameScoreController.initializeScoreView();
        }
    };
    /**
     * 测试重置功能（可以在浏览器控制台手动调用）
     */
    GamePageController.prototype.testReset = function () {
        if (this.chessBoardController) {
            this.chessBoardController.resetGameScene();
        }
        else {
            console.error("❌ chessBoardController 不存在！");
        }
    };
    // 处理扫雷回合开始通知
    GamePageController.prototype.onNoticeRoundStart = function (data) {
        this.currentRoundNumber = data.roundNumber || 1;
        this.currentCountdown = data.countDown || 25;
        this.gameStatus = data.gameStatus || 0;
        // 新回合开始，重置操作状态
        this.canOperate = true;
        this.hasOperatedThisRound = false;
        // 清理棋盘上的所有玩家预制体
        if (this.currentMapType === 0 && this.chessBoardController) {
            // 方形地图
            this.chessBoardController.clearAllPlayerNodes();
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController) {
            // 六边形地图
            this.hexChessBoardController.clearAllPlayerNodes();
        }
        // 开始倒计时
        this.startCountdown(this.currentCountdown);
    };
    // 处理扫雷操作展示通知
    GamePageController.prototype.onNoticeActionDisplay = function (data) {
        // 保存当前NoticeActionDisplay数据，用于倒计时显示逻辑
        this.currentNoticeActionData = data;
        // 进入展示阶段，不能再操作
        this.canOperate = false;
        this.gameStatus = data.gameStatus || 0;
        // 根据countDown重置倒计时为5秒
        this.currentCountdown = data.countDown || 5;
        this.updateCountdownDisplay(this.currentCountdown);
        this.startCountdown(this.currentCountdown);
        // 更新剩余炸弹数量显示
        if (data.remainingMines !== undefined) {
            this.updateMineCountDisplay(data.remainingMines);
        }
        // 在棋盘上显示所有玩家的操作（头像）
        this.displayPlayerActions(data.playerActions, data.playerTotalScores);
        // 立即显示先手+1（如果先手不是我）
        this.showFirstChoiceBonusImmediately(data.playerActions);
    };
    /**
     * 立即显示先手+1奖励
     * @param playerActions 玩家操作列表
     */
    GamePageController.prototype.showFirstChoiceBonusImmediately = function (playerActions) {
        var _a, _b;
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 查找先手玩家
        var firstChoicePlayer = playerActions.find(function (action) { return action.isFirstChoice; });
        if (firstChoicePlayer) {
            var firstChoiceUserIndex = this.findUserIndex(firstChoicePlayer.userId);
            if (firstChoiceUserIndex !== -1) {
                // 立即显示先手+1
                this.showScoreInScorePanel(firstChoiceUserIndex, 1);
                // 同时在player_game_pfb显示先手+1
                this.showScoreOnPlayerAvatar(firstChoicePlayer.userId, 1);
            }
        }
    };
    /**
     * 延迟更新棋盘的回调方法
     * @param data NoticeActionDisplay数据
     */
    GamePageController.prototype.delayedUpdateBoard = function (data) {
        this.updateBoardAfterActions(data);
    };
    /**
     * 更新棋盘（删除格子、生成预制体、连锁动画）
     * @param data NoticeActionDisplay数据
     */
    GamePageController.prototype.updateBoardAfterActions = function (data) {
        // 注意：分数动画和头像删除现在由倒计时逻辑控制，这里直接处理格子隐藏和数字生成
        var _this = this;
        // 立即处理每个玩家的操作结果
        // 先按位置分组，处理同一位置有多个操作的情况
        var processedPositions = new Set();
        data.playerActions.forEach(function (action) {
            var positionKey = action.x + "," + action.y;
            // 如果这个位置已经处理过，跳过
            if (processedPositions.has(positionKey)) {
                return;
            }
            // 查找同一位置的所有操作
            var samePositionActions = data.playerActions.filter(function (a) {
                return a.x === action.x && a.y === action.y;
            });
            // 处理同一位置的操作结果（格子隐藏和数字生成）
            _this.processPositionResult(action.x, action.y, samePositionActions);
            // 标记这个位置已处理
            processedPositions.add(positionKey);
        });
        // 处理连锁展开结果
        if (data.floodFillResults && data.floodFillResults.length > 0) {
            data.floodFillResults.forEach(function (floodFill) {
                _this.processFloodFillResult(floodFill);
            });
        }
    };
    /**
     * 让所有头像消失（支持方形地图和六边形地图）
     * @param playerActions 玩家操作列表
     * @param onComplete 完成回调
     */
    GamePageController.prototype.hideAllAvatars = function (playerActions, onComplete) {
        // 根据地图类型调用对应的控制器
        if (this.currentMapType === 0 && this.chessBoardController) {
            // 方形地图：直接调用一次头像删除，不区分位置
            this.chessBoardController.hideAvatarsAtPosition(0, 0, function () {
                onComplete();
            });
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController) {
            // 六边形地图：直接调用方法（已经编译成功）
            this.hexChessBoardController.hideAllHexAvatars(function () {
                onComplete();
            });
        }
        else {
            // 没有可用的控制器，直接执行回调
            console.warn("没有可用的棋盘控制器，跳过头像消失动画");
            onComplete();
        }
    };
    /**
     * 处理同一位置的多个操作结果
     * @param x 格子x坐标（方形地图）或q坐标（六边形地图）
     * @param y 格子y坐标（方形地图）或r坐标（六边形地图）
     * @param actions 该位置的所有操作
     */
    GamePageController.prototype.processPositionResult = function (x, y, actions) {
        var _a, _b;
        // 根据地图类型删除该位置的格子（立即隐藏，不播放动画）
        if (this.currentMapType === 0) {
            // 方形地图
            this.chessBoardController.removeGridAt(x, y, true);
        }
        else if (this.currentMapType === 1) {
            // 六边形地图，x实际是q坐标，y实际是r坐标
            if (this.hexChessBoardController) {
                this.hexChessBoardController.hideHexGridAt(x, y, true);
            }
        }
        // 检查是否有地雷被点击（action=1且result="mine"）
        var mineClickAction = actions.find(function (action) {
            return action.action === 1 && action.result === "mine";
        });
        if (mineClickAction) {
            // 如果有地雷被点击，直接显示炸弹，不管是否有标记
            // 判断是否是当前用户点到的雷
            var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
            var isCurrentUser = mineClickAction.userId === currentUserId;
            // 根据地图类型调用对应的方法
            if (this.currentMapType === 0) {
                // 方形地图
                this.chessBoardController.createBoomPrefab(x, y, isCurrentUser);
            }
            else if (this.currentMapType === 1) {
                // 六边形地图，x实际是q坐标，y实际是r坐标
                if (this.hexChessBoardController) {
                    this.hexChessBoardController.createHexBoomPrefab(x, y, isCurrentUser);
                }
            }
            return;
        }
        // 如果没有地雷被点击，按原逻辑处理第一个操作的结果
        var firstAction = actions[0];
        var result = firstAction.result;
        if (result === "correct_mark") {
            // 正确标记：生成biaoji预制体
            if (this.currentMapType === 0) {
                // 方形地图
                this.chessBoardController.createBiaojiPrefab(x, y);
            }
            else if (this.currentMapType === 1) {
                // 六边形地图
                if (this.hexChessBoardController) {
                    this.hexChessBoardController.createHexBiaojiPrefab(x, y);
                }
            }
        }
        else if (typeof result === "number") {
            // 数字：更新neighborMines显示
            if (this.currentMapType === 0) {
                // 方形地图
                this.chessBoardController.updateNeighborMinesDisplay(x, y, result);
            }
            else if (this.currentMapType === 1) {
                // 六边形地图
                if (this.hexChessBoardController) {
                    this.hexChessBoardController.updateHexNeighborMinesDisplay(x, y, result);
                }
            }
        }
    };
    /**
     * 处理单个玩家操作结果（保留原方法以防其他地方调用）
     * @param action 玩家操作数据
     */
    GamePageController.prototype.processPlayerActionResult = function (action) {
        var _a, _b;
        var x = action.x;
        var y = action.y;
        var result = action.result;
        // 删除该位置的格子
        this.chessBoardController.removeGridAt(x, y);
        // 根据结果生成相应的预制体
        if (result === "mine") {
            // 地雷：生成boom预制体
            // 判断是否是当前用户点到的雷
            var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
            var isCurrentUser = action.userId === currentUserId;
            this.chessBoardController.createBoomPrefab(x, y, isCurrentUser);
        }
        else if (result === "correct_mark") {
            // 正确标记：生成biaoji预制体
            this.chessBoardController.createBiaojiPrefab(x, y);
        }
        else if (typeof result === "number") {
            // 数字：更新neighborMines显示
            this.chessBoardController.updateNeighborMinesDisplay(x, y, result);
        }
    };
    /**
     * 处理连锁展开结果
     * @param floodFill 连锁展开数据
     */
    GamePageController.prototype.processFloodFillResult = function (floodFill) {
        var _this = this;
        // 立即处理所有连锁格子，不再有延迟
        floodFill.revealedBlocks.forEach(function (block) {
            if (_this.currentMapType === 0) {
                // 方形地图：立即隐藏格子并显示数字
                _this.chessBoardController.removeGridAt(block.x, block.y, true);
                // 立即显示数字
                if (block.neighborMines > 0) {
                    _this.chessBoardController.updateNeighborMinesDisplay(block.x, block.y, block.neighborMines);
                }
            }
            else if (_this.currentMapType === 1) {
                // 六边形地图，x实际是q坐标，y实际是r坐标
                if (_this.hexChessBoardController) {
                    // 立即隐藏格子
                    _this.hexChessBoardController.hideHexGridAt(block.x, block.y, true);
                    // 立即显示数字（如果有的话）
                    if (block.neighborMines > 0) {
                        _this.hexChessBoardController.updateHexNeighborMinesDisplay(block.x, block.y, block.neighborMines);
                    }
                }
            }
        });
    };
    // 处理扫雷回合结束通知
    GamePageController.prototype.onNoticeRoundEnd = function (data) {
        // 进入回合结束阶段，不能再操作
        this.canOperate = false;
        this.gameStatus = data.gameStatus || 1;
        // 不再处理倒计时，让客户端自然倒计时到0，方便展示54321
        // 处理玩家分数动画和头像显示
        if (data.playerResults && data.playerResults.length > 0) {
            this.displayPlayerScoreAnimations(data.playerResults);
            // 如果本回合我没有操作，根据后端消息生成我的头像
            this.handleMyAvatarIfNotOperated(data.playerResults);
        }
        // 清理棋盘上的所有玩家预制体
        if (this.currentMapType === 0 && this.chessBoardController) {
            // 方形地图
            this.chessBoardController.clearAllPlayerNodes();
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController) {
            // 六边形地图
            this.hexChessBoardController.clearAllPlayers();
        }
    };
    /**
     * 在棋盘上显示所有玩家的操作
     * @param playerActions 玩家操作列表
     * @param playerTotalScores 玩家总分数据
     */
    GamePageController.prototype.displayPlayerActions = function (playerActions, playerTotalScores) {
        var _this = this;
        var _a, _b;
        // 检查是否有可用的棋盘控制器
        var hasSquareBoard = this.chessBoardController && this.currentMapType === 0;
        var hasHexBoard = this.hexChessBoardController && this.currentMapType === 1;
        if ((!hasSquareBoard && !hasHexBoard) || !playerActions || playerActions.length === 0) {
            return;
        }
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 注意：分数动画已经在updateBoardAfterActions的第一步显示了，这里不再重复显示
        // 检查本回合是否进行了操作，如果没有，需要显示自己的头像
        var myAction = playerActions.find(function (action) { return action.userId === currentUserId; });
        var shouldDisplayMyAvatar = false;
        if (!this.hasOperatedThisRound && myAction) {
            shouldDisplayMyAvatar = true;
            // 生成我的头像
            var withFlag = (myAction.action === 2); // action=2表示标记操作，显示旗子
            if (this.currentMapType === 0) {
                // 方形地图
                this.chessBoardController.placePlayerOnGrid(myAction.x, myAction.y, withFlag);
            }
            else if (this.currentMapType === 1) {
                // 六边形地图，x实际是q坐标，y实际是r坐标
                if (this.hexChessBoardController) {
                    this.hexChessBoardController.placePlayerOnHexGrid(myAction.x, myAction.y, withFlag);
                }
            }
        }
        // 过滤掉自己的操作，只显示其他玩家的操作
        var otherPlayersActions = playerActions.filter(function (action) { return action.userId !== currentUserId; });
        if (otherPlayersActions.length === 0) {
            return;
        }
        // 按位置分组其他玩家的操作
        var positionGroups = this.groupActionsByPosition(otherPlayersActions);
        // 为每个位置生成预制体
        positionGroups.forEach(function (actions, positionKey) {
            var _a = positionKey.split(',').map(Number), x = _a[0], y = _a[1];
            if (_this.currentMapType === 0) {
                // 方形地图
                _this.chessBoardController.displayOtherPlayersAtPosition(x, y, actions);
            }
            else if (_this.currentMapType === 1) {
                // 六边形地图，x实际是q坐标，y实际是r坐标
                if (_this.hexChessBoardController) {
                    // 直接调用方法（已经编译成功）
                    _this.hexChessBoardController.displayOtherPlayersAtHexPosition(x, y, actions);
                }
            }
        });
    };
    /**
     * 按位置分组玩家操作
     * @param playerActions 玩家操作列表
     * @returns Map<string, PlayerActionDisplay[]> 位置为key，操作列表为value
     */
    GamePageController.prototype.groupActionsByPosition = function (playerActions) {
        var groups = new Map();
        for (var _i = 0, playerActions_1 = playerActions; _i < playerActions_1.length; _i++) {
            var action = playerActions_1[_i];
            var positionKey = action.x + "," + action.y;
            if (!groups.has(positionKey)) {
                groups.set(positionKey, []);
            }
            groups.get(positionKey).push(action);
        }
        return groups;
    };
    /**
     * 显示玩家分数动画
     * @param playerResults 玩家回合结果列表
     */
    GamePageController.prototype.displayPlayerScoreAnimations = function (playerResults) {
        var _this = this;
        var _a, _b;
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 为每个玩家显示分数动画
        playerResults.forEach(function (result, index) {
            // 延迟显示，让动画错开
            _this.scheduleOnce(function () {
                _this.showPlayerScoreAnimation(result, currentUserId);
            }, index * 0.2);
        });
    };
    /**
     * 显示单个玩家的分数动画
     * @param result 玩家回合结果
     * @param currentUserId 当前用户ID
     */
    GamePageController.prototype.showPlayerScoreAnimation = function (result, currentUserId) {
        var isMyself = result.userId === currentUserId;
        if (isMyself) {
            // 自己的分数动画：在player_game_pfb里只显示本回合得分
            this.showMyScoreAnimation(result);
        }
        else {
            // 其他人的分数动画：根据isFirstChoice决定显示逻辑
            this.showOtherPlayerScoreAnimation(result);
        }
    };
    /**
     * 显示自己的分数动画
     * @param result 玩家回合结果
     */
    GamePageController.prototype.showMyScoreAnimation = function (result) {
        // 在棋盘上的头像预制体中显示本回合得分
        if (this.currentMapType === 0 && this.chessBoardController) {
            // 方形地图
            this.chessBoardController.showScoreOnPlayerNode(result.x, result.y, result.score, false);
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController) {
            // 六边形地图，x实际是q坐标，y实际是r坐标
            this.hexChessBoardController.showScoreOnHexPlayerNode(result.x, result.y, result.score, false);
        }
        // 在player_score_pfb中显示分数动画
        this.showScoreAnimationInScorePanel(result.userId, result.score, result.isFirstChoice);
    };
    /**
     * 显示其他玩家的分数动画
     * @param result 玩家回合结果
     */
    GamePageController.prototype.showOtherPlayerScoreAnimation = function (result) {
        if (result.isFirstChoice) {
            // 其他人为先手：player_game_pfb里不显示+1，只显示本回合得分
            if (this.currentMapType === 0 && this.chessBoardController) {
                // 方形地图
                this.chessBoardController.showScoreOnPlayerNode(result.x, result.y, result.score, false);
            }
            else if (this.currentMapType === 1 && this.hexChessBoardController) {
                // 六边形地图
                this.hexChessBoardController.showScoreOnHexPlayerNode(result.x, result.y, result.score, false);
            }
            // 在player_score_pfb里先显示+1，再显示本回合得分，然后更新总分
            this.showFirstChoiceScoreAnimation(result.userId, result.score);
        }
        else {
            // 其他人非先手：正常显示本回合得分
            if (this.currentMapType === 0 && this.chessBoardController) {
                // 方形地图
                this.chessBoardController.showScoreOnPlayerNode(result.x, result.y, result.score, false);
            }
            else if (this.currentMapType === 1 && this.hexChessBoardController) {
                // 六边形地图
                this.hexChessBoardController.showScoreOnHexPlayerNode(result.x, result.y, result.score, false);
            }
            // 在player_score_pfb中显示分数动画
            this.showScoreAnimationInScorePanel(result.userId, result.score, false);
        }
    };
    /**
     * 在分数面板中显示分数动画
     * @param userId 用户ID
     * @param score 本回合得分
     * @param isFirstChoice 是否为先手
     */
    GamePageController.prototype.showScoreAnimationInScorePanel = function (userId, score, isFirstChoice) {
        // 这里需要找到对应的PlayerScoreController并调用分数动画
        // 由于没有直接的引用，这里先用日志记录
        // TODO: 实现在player_score_pfb中显示分数动画的逻辑
        // 需要找到对应用户的PlayerScoreController实例并调用showAddScore方法
    };
    /**
     * 显示先手玩家的分数动画（先显示+1，再显示本回合得分）
     * @param userId 用户ID
     * @param score 本回合得分
     */
    GamePageController.prototype.showFirstChoiceScoreAnimation = function (userId, score) {
        var _this = this;
        // 先显示+1的先手奖励
        this.scheduleOnce(function () {
            _this.showScoreAnimationInScorePanel(userId, 1, true);
        }, 0.1);
        // 再显示本回合得分
        this.scheduleOnce(function () {
            _this.showScoreAnimationInScorePanel(userId, score, false);
        }, 1.2);
        // 最后更新总分
        this.scheduleOnce(function () {
            _this.updatePlayerTotalScore(userId, score + 1);
        }, 2.4);
    };
    /**
     * 更新玩家总分
     * @param userId 用户ID
     * @param totalScore 新的总分
     */
    GamePageController.prototype.updatePlayerTotalScore = function (userId, totalScore) {
        // TODO: 实现更新玩家总分的逻辑
        // 需要更新GlobalBean中的用户数据，并刷新UI显示
    };
    /**
     * 如果本回合我没有操作，根据后端消息生成我的头像
     * @param playerResults 玩家回合结果列表
     */
    GamePageController.prototype.handleMyAvatarIfNotOperated = function (playerResults) {
        var _a, _b;
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 检查本回合是否进行了操作
        if (this.hasOperatedThisRound) {
            return;
        }
        // 查找我的操作结果
        var myResult = playerResults.find(function (result) { return result.userId === currentUserId; });
        if (!myResult) {
            return;
        }
        // 根据后端消息生成我的头像
        if (this.chessBoardController) {
            // 根据操作类型决定是否显示旗子
            var withFlag = (myResult.action === 2); // action=2表示标记操作，显示旗子
            // 生成我的头像预制体
            this.chessBoardController.placePlayerOnGrid(myResult.x, myResult.y, withFlag);
        }
    };
    // 发送点击方块消息
    GamePageController.prototype.sendClickBlock = function (x, y, action) {
        if (!this.canOperate) {
            return;
        }
        // 检查本回合是否已经操作过
        if (this.hasOperatedThisRound) {
            return;
        }
        var clickData = {
            x: x,
            y: y,
            action: action // 1=挖掘方块，2=标记/取消标记地雷
        };
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeClickBlock, clickData);
        // 标记本回合已经操作过，防止重复操作
        this.hasOperatedThisRound = true;
    };
    // 发送六边形点击方块消息
    GamePageController.prototype.sendHexClickBlock = function (q, r, action) {
        if (!this.canOperate) {
            return;
        }
        // 检查本回合是否已经操作过
        if (this.hasOperatedThisRound) {
            return;
        }
        // 根据当前地图类型决定发送格式
        if (this.currentMapType === 1) {
            // 六边形地图：使用六边形坐标格式
            var hexClickData = {
                q: q,
                r: r,
                action: action // 1=挖掘方块，2=标记/取消标记地雷
            };
            // 注意：这里仍然使用 MsgTypeClickBlock，但数据格式不同
            // 后端应该根据当前房间的 mapType 来解析不同的坐标格式
            WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeClickBlock, hexClickData);
        }
        else {
            // 方形地图：转换为x,y坐标（备用方案）
            var clickData = {
                x: q,
                y: r,
                action: action
            };
            WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeClickBlock, clickData);
        }
        // 标记本回合已经操作过，防止重复操作
        this.hasOperatedThisRound = true;
    };
    // 检查是否可以操作
    GamePageController.prototype.isCanOperate = function () {
        return this.canOperate && !this.hasOperatedThisRound;
    };
    /**
     * 处理首选玩家奖励通知
     * @param data NoticeFirstChoiceBonus 消息数据
     */
    GamePageController.prototype.onNoticeFirstChoiceBonus = function (data) {
        var _a, _b;
        // 转发给GameScoreController处理所有玩家的分数更新和加分动画
        if (this.gameScoreController) {
            this.gameScoreController.onNoticeFirstChoiceBonus(data);
        }
        // 判断是否为当前用户，如果是则同时更新player_game_pfb中的change_score
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        var isMyself = (data.userId === currentUserId);
        if (isMyself) {
            // 更新player_game_pfb中的change_score显示
            this.updatePlayerGameScore(data.userId, data.bonusScore);
        }
    };
    /**
     * 更新player_game_pfb中的change_score显示
     * @param userId 用户ID
     * @param bonusScore 奖励分数
     */
    GamePageController.prototype.updatePlayerGameScore = function (userId, bonusScore) {
        // 调用ChessBoardController显示加分效果
        if (this.chessBoardController) {
            this.chessBoardController.showPlayerGameScore(userId, bonusScore);
        }
        else {
            console.warn("ChessBoardController未设置，无法显示player_game_pfb加分效果");
        }
    };
    // 获取当前地图类型
    GamePageController.prototype.getCurrentMapType = function () {
        return this.currentMapType;
    };
    // 获取当前炸弹数量
    GamePageController.prototype.getCurrentMineCount = function () {
        return this.currentMineCount;
    };
    // 获取当前回合操作状态（用于调试）
    GamePageController.prototype.getCurrentRoundStatus = function () {
        return {
            roundNumber: this.currentRoundNumber,
            canOperate: this.canOperate,
            hasOperated: this.hasOperatedThisRound
        };
    };
    // 开始倒计时
    GamePageController.prototype.startCountdown = function (seconds) {
        var _this = this;
        // 清除之前的计时器
        this.clearCountdownTimer();
        var remainingSeconds = seconds;
        this.updateCountdownDisplay(remainingSeconds);
        this.countdownInterval = setInterval(function () {
            remainingSeconds--;
            _this.updateCountdownDisplay(remainingSeconds);
            // 在NoticeActionDisplay阶段，根据倒计时执行不同的显示逻辑
            if (_this.gameStatus === 0 && _this.currentNoticeActionData) {
                _this.handleCountdownBasedDisplay(remainingSeconds);
            }
            if (remainingSeconds <= 0) {
                _this.clearCountdownTimer();
            }
        }, 1000);
    };
    // 更新倒计时显示
    GamePageController.prototype.updateCountdownDisplay = function (seconds) {
        if (this.timeLabel) {
            this.timeLabel.string = seconds + "s"; // 显示数字加s：5s, 4s, 3s, 2s, 1s, 0s
        }
        this.currentCountdown = seconds;
    };
    /**
     * 根据倒计时处理不同时机的显示逻辑
     * @param remainingSeconds 剩余秒数
     */
    GamePageController.prototype.handleCountdownBasedDisplay = function (remainingSeconds) {
        if (!this.currentNoticeActionData) {
            return;
        }
        var data = this.currentNoticeActionData;
        if (remainingSeconds === 4) {
            // 4s时：同时展示本回合加减分
            this.showCurrentRoundScores(data.playerActions, data.playerTotalScores);
        }
        else if (remainingSeconds === 3) {
            // 3s时：隐藏加减分并删除头像预制体
            this.hideScoreEffectsAndAvatars(data.playerActions);
            // 3s时：立即执行格子隐藏和生成数字预制体等操作
            this.updateBoardAfterActions(data);
            // 清空数据，避免重复执行
            this.currentNoticeActionData = null;
        }
    };
    /**
     * 显示本回合所有玩家的加减分
     * @param playerActions 玩家操作列表
     * @param playerTotalScores 玩家总分数据
     */
    GamePageController.prototype.showCurrentRoundScores = function (playerActions, playerTotalScores) {
        var _this = this;
        var _a, _b;
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 为每个玩家显示本回合的加减分
        playerActions.forEach(function (action) {
            // 在player_game_pfb中显示本回合的加减分
            _this.showScoreOnPlayerAvatar(action.userId, action.score);
            // 在分数面板显示本回合的加减分
            var userIndex = _this.findUserIndex(action.userId);
            if (userIndex !== -1) {
                _this.showScoreInScorePanel(userIndex, action.score);
            }
        });
        // 延迟更新总分，让加减分动画先显示
        this.scheduleOnce(function () {
            // 更新所有玩家的总分
            playerActions.forEach(function (action) {
                var totalScore = playerTotalScores[action.userId] || 0;
                if (_this.gameScoreController) {
                    _this.gameScoreController.updatePlayerScore(action.userId, totalScore);
                    // 更新全局数据中的总分
                    _this.updatePlayerTotalScoreInGlobalData(action.userId, totalScore);
                }
            });
        }, 1.2);
    };
    /**
     * 隐藏加减分效果并删除头像预制体
     * @param playerActions 玩家操作列表
     */
    GamePageController.prototype.hideScoreEffectsAndAvatars = function (playerActions) {
        // 隐藏所有加减分效果
        this.hideAllScoreEffects();
        // 删除头像预制体（不等待完成回调）
        this.hideAllAvatars(playerActions, function () {
            console.log("所有头像已隐藏完成");
        });
    };
    /**
     * 隐藏所有加减分效果
     */
    GamePageController.prototype.hideAllScoreEffects = function () {
        // 隐藏分数面板的加减分效果
        // 注意：这里暂时不处理分数面板的隐藏，因为PlayerScoreController的hideScoreEffects会在1秒后自动隐藏
        // 隐藏棋盘上所有头像的加减分效果
        this.hideAllPlayerGameScoreEffects();
    };
    /**
     * 隐藏棋盘上所有头像的加减分效果
     */
    GamePageController.prototype.hideAllPlayerGameScoreEffects = function () {
        // 遍历棋盘上的所有PlayerGameController，调用hideScoreEffects方法
        if (this.currentMapType === 0 && this.chessBoardController && this.chessBoardController.boardNode) {
            // 方形地图
            var children = this.chessBoardController.boardNode.children;
            for (var i = 0; i < children.length; i++) {
                var child = children[i];
                var playerController = child.getComponent(PlayerGameController_1.default);
                if (playerController) {
                    playerController.hideScoreEffects();
                }
            }
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController && this.hexChessBoardController.boardNode) {
            // 六边形地图
            var children = this.hexChessBoardController.boardNode.children;
            for (var i = 0; i < children.length; i++) {
                var child = children[i];
                var playerController = child.getComponent(PlayerGameController_1.default);
                if (playerController) {
                    playerController.hideScoreEffects();
                }
            }
        }
    };
    // 更新炸弹数显示
    GamePageController.prototype.updateMineCountDisplay = function (mineCount) {
        if (this.mineCountLabel) {
            this.mineCountLabel.string = "" + mineCount;
        }
    };
    // 根据地图类型切换地图显示
    GamePageController.prototype.switchMapDisplay = function (mapType) {
        // 先隐藏所有地图
        this.hideAllMaps();
        // 根据地图类型显示对应的地图
        if (mapType === 0) {
            this.showSquareMap();
        }
        else if (mapType === 1) {
            this.showHexMap();
        }
        else {
            console.warn("\u672A\u77E5\u7684\u5730\u56FE\u7C7B\u578B: " + mapType + "\uFF0C\u9ED8\u8BA4\u663E\u793A\u65B9\u5F62\u5730\u56FE");
            this.showSquareMap();
        }
    };
    // 显示方形地图
    GamePageController.prototype.showSquareMap = function () {
        if (this.squareMapNode) {
            this.squareMapNode.active = true;
        }
        else {
            console.warn('方形地图节点未挂载');
        }
    };
    // 显示六边形地图
    GamePageController.prototype.showHexMap = function () {
        if (this.hexMapNode) {
            this.hexMapNode.active = true;
        }
        else {
            console.warn('六边形地图节点未挂载');
        }
    };
    // 隐藏所有地图
    GamePageController.prototype.hideAllMaps = function () {
        if (this.squareMapNode) {
            this.squareMapNode.active = false;
        }
        if (this.hexMapNode) {
            this.hexMapNode.active = false;
        }
    };
    // 清除倒计时定时器
    GamePageController.prototype.clearCountdownTimer = function () {
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
            this.countdownInterval = null;
        }
    };
    /**
     * 显示所有玩家的分数动画和更新总分（参考先手加分逻辑）
     * @param playerActions 玩家操作列表
     * @param playerTotalScores 玩家总分数据
     */
    GamePageController.prototype.displayPlayerScoreAnimationsAndUpdateTotalScores = function (playerActions, playerTotalScores) {
        var _this = this;
        var _a, _b;
        // 获取当前用户ID
        var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
        if (!currentUserId) {
            console.warn("无法获取当前用户ID");
            return;
        }
        // 查找先手玩家
        var firstChoicePlayer = playerActions.find(function (action) { return action.isFirstChoice; });
        var isCurrentUserFirstChoice = firstChoicePlayer && firstChoicePlayer.userId === currentUserId;
        // 如果我不是先手，先为先手玩家在分数面板显示+1
        if (!isCurrentUserFirstChoice && firstChoicePlayer) {
            var firstChoiceUserIndex_1 = this.findUserIndex(firstChoicePlayer.userId);
            if (firstChoiceUserIndex_1 !== -1) {
                // 0.1秒后显示先手+1
                this.scheduleOnce(function () {
                    _this.showScoreInScorePanel(firstChoiceUserIndex_1, 1);
                }, 0.1);
            }
        }
        // 为每个玩家显示分数动画和更新总分
        playerActions.forEach(function (action, index) {
            var totalScore = playerTotalScores[action.userId] || 0;
            var isFirstChoice = action.isFirstChoice;
            // 延迟显示，让动画错开
            _this.scheduleOnce(function () {
                if (isFirstChoice) {
                    // 先手玩家：特殊处理（先显示+1，再显示本回合分数）
                    _this.showFirstChoicePlayerScoreAnimation(action, currentUserId, totalScore);
                }
                else {
                    // 非先手玩家：直接显示本回合分数
                    _this.showPlayerScoreAnimationAndUpdateTotal(action, currentUserId, totalScore);
                }
            }, index * 0.2);
        });
    };
    /**
     * 显示单个玩家的分数动画和更新总分（参考先手加分逻辑）
     * @param action 玩家操作数据
     * @param currentUserId 当前用户ID
     * @param totalScore 玩家总分
     */
    GamePageController.prototype.showPlayerScoreAnimationAndUpdateTotal = function (action, currentUserId, totalScore) {
        var _this = this;
        var isMyself = action.userId === currentUserId;
        // 1. 在分数面板显示加减分动画（参考先手加分的逻辑）
        if (this.gameScoreController) {
            // 找到用户索引
            var userIndex = this.findUserIndex(action.userId);
            if (userIndex !== -1) {
                // 在分数面板显示加减分效果
                this.showScoreInScorePanel(userIndex, action.score);
            }
        }
        // 2. 更新总分（参考先手加分的updatePlayerScore）
        this.scheduleOnce(function () {
            if (_this.gameScoreController) {
                _this.gameScoreController.updatePlayerScore(action.userId, totalScore);
                // 更新全局数据中的总分
                _this.updatePlayerTotalScoreInGlobalData(action.userId, totalScore);
            }
        }, 1.2);
        // 3. 在所有玩家头像上显示加减分（不仅仅是自己）
        this.scheduleOnce(function () {
            _this.showScoreOnPlayerAvatar(action.userId, action.score);
        }, 0.1);
    };
    /**
     * 更新全局数据中的玩家总分
     * @param userId 用户ID
     * @param totalScore 新的总分
     */
    GamePageController.prototype.updatePlayerTotalScoreInGlobalData = function (userId, totalScore) {
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法更新玩家总分");
            return;
        }
        var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
        var userIndex = users.findIndex(function (user) { return user.userId === userId; });
        if (userIndex !== -1) {
            users[userIndex].score = totalScore;
        }
        else {
            console.warn("\u627E\u4E0D\u5230\u73A9\u5BB6: userId=" + userId);
        }
    };
    /**
     * 查找用户索引
     * @param userId 用户ID
     * @returns 用户索引，找不到返回-1
     */
    GamePageController.prototype.findUserIndex = function (userId) {
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法查找用户索引");
            return -1;
        }
        var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
        return users.findIndex(function (user) { return user.userId === userId; });
    };
    /**
     * 在玩家头像上显示加减分
     * @param userId 用户ID
     * @param score 分数变化
     */
    GamePageController.prototype.showScoreOnPlayerAvatar = function (userId, score) {
        // 根据地图类型调用对应的控制器
        if (this.currentMapType === 0 && this.chessBoardController) {
            // 方形地图
            this.chessBoardController.showPlayerGameScore(userId, score);
        }
        else if (this.currentMapType === 1 && this.hexChessBoardController) {
            // 六边形地图
            this.hexChessBoardController.showHexPlayerGameScore(userId, score);
        }
        else {
            console.warn("没有可用的棋盘控制器，无法显示头像分数");
        }
    };
    /**
     * 在分数面板显示加减分效果
     * @param userIndex 用户索引
     * @param score 分数变化
     */
    GamePageController.prototype.showScoreInScorePanel = function (userIndex, score) {
        if (!this.gameScoreController) {
            console.warn("gameScoreController 不存在，无法在分数面板显示分数");
            return;
        }
        // 获取对应的PlayerScoreController
        var playerScoreController = this.gameScoreController.getPlayerScoreController(userIndex);
        if (playerScoreController) {
            // 显示加减分效果
            if (score > 0) {
                playerScoreController.showAddScore(score);
            }
            else if (score < 0) {
                playerScoreController.showSubScore(Math.abs(score));
            }
        }
        else {
            console.warn("\u627E\u4E0D\u5230\u7528\u6237\u7D22\u5F15 " + userIndex + " \u5BF9\u5E94\u7684PlayerScoreController");
        }
    };
    /**
     * 显示先手玩家的分数动画（在分数面板先显示+1，再显示本回合分数）
     * @param action 玩家操作数据
     * @param currentUserId 当前用户ID
     * @param totalScore 玩家总分
     */
    GamePageController.prototype.showFirstChoicePlayerScoreAnimation = function (action, currentUserId, totalScore) {
        var _this = this;
        var userIndex = this.findUserIndex(action.userId);
        // 第一步：在分数面板显示+1先手奖励（1.2秒，与非先手玩家同步）
        this.scheduleOnce(function () {
            // 分数面板显示本回合分数（+1已经在前面显示过了）
            if (userIndex !== -1) {
                _this.showScoreInScorePanel(userIndex, action.score);
            }
        }, 1.2);
        // 第二步：更新总分（2.4秒）
        this.scheduleOnce(function () {
            if (_this.gameScoreController) {
                _this.gameScoreController.updatePlayerScore(action.userId, totalScore);
                _this.updatePlayerTotalScoreInGlobalData(action.userId, totalScore);
            }
        }, 2.4);
        // 第三步：在player_game_pfb中显示本回合的加减分（与非先手玩家同步）
        this.scheduleOnce(function () {
            _this.showScoreOnPlayerAvatar(action.userId, action.score);
        }, 0.1);
    };
    GamePageController.prototype.onDestroy = function () {
        // 移除棋盘点击事件监听
        if (this.chessBoardController) {
            this.chessBoardController.node.off('chess-board-click', this.onChessBoardClick, this);
        }
    };
    __decorate([
        property(cc.Node)
    ], GamePageController.prototype, "boardBtnBack", void 0);
    __decorate([
        property(cc.Label)
    ], GamePageController.prototype, "timeLabel", void 0);
    __decorate([
        property(cc.Label)
    ], GamePageController.prototype, "mineCountLabel", void 0);
    __decorate([
        property(cc.Node)
    ], GamePageController.prototype, "squareMapNode", void 0);
    __decorate([
        property(cc.Node)
    ], GamePageController.prototype, "hexMapNode", void 0);
    __decorate([
        property(LeaveDialogController_1.default)
    ], GamePageController.prototype, "leaveDialogController", void 0);
    __decorate([
        property(CongratsDialogController_1.default)
    ], GamePageController.prototype, "congratsDialogController", void 0);
    __decorate([
        property(GameScoreController_1.default)
    ], GamePageController.prototype, "gameScoreController", void 0);
    __decorate([
        property(ChessBoardController_1.default)
    ], GamePageController.prototype, "chessBoardController", void 0);
    __decorate([
        property(HexChessBoardController_1.default)
    ], GamePageController.prototype, "hexChessBoardController", void 0);
    GamePageController = __decorate([
        ccclass
    ], GamePageController);
    return GamePageController;
}(cc.Component));
exports.default = GamePageController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0cy9zY3JpcHRzL2dhbWUvR2FtZVBhZ2VDb250cm9sbGVyLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxvQkFBb0I7QUFDcEIsNEVBQTRFO0FBQzVFLG1CQUFtQjtBQUNuQixzRkFBc0Y7QUFDdEYsOEJBQThCO0FBQzlCLHNGQUFzRjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBR3RGLGlEQUFnRDtBQUNoRCx1RUFBa0U7QUFDbEUscURBQW9EO0FBQ3BELHlDQUF3QztBQUN4Qyx1Q0FBc0M7QUFDdEMsdUVBQWtFO0FBQ2xFLDZEQUF3RDtBQUN4RCxxRUFBZ0U7QUFDaEUsMkVBQXNFO0FBQ3RFLHFFQUFnRTtBQUNoRSw0REFBMkQ7QUFDM0QsOENBQTZDO0FBRXZDLElBQUEsS0FBd0IsRUFBRSxDQUFDLFVBQVUsRUFBbkMsT0FBTyxhQUFBLEVBQUUsUUFBUSxjQUFrQixDQUFDO0FBRzVDO0lBQWdELHNDQUFZO0lBQTVEO1FBQUEscUVBazdDQztRQS82Q0csa0JBQVksR0FBWSxJQUFJLENBQUEsQ0FBQyxNQUFNO1FBR25DLGVBQVMsR0FBYSxJQUFJLENBQUEsQ0FBQyxVQUFVO1FBR3JDLG9CQUFjLEdBQWEsSUFBSSxDQUFBLENBQUMsV0FBVztRQUczQyxtQkFBYSxHQUFZLElBQUksQ0FBQSxDQUFDLHVCQUF1QjtRQUdyRCxnQkFBVSxHQUFZLElBQUksQ0FBQSxDQUFDLHdCQUF3QjtRQUduRCwyQkFBcUIsR0FBMEIsSUFBSSxDQUFBLENBQUMsU0FBUztRQUc3RCw4QkFBd0IsR0FBNkIsSUFBSSxDQUFBLENBQUMsTUFBTTtRQUdoRSx5QkFBbUIsR0FBd0IsSUFBSSxDQUFBLENBQUMsT0FBTztRQUd2RCwwQkFBb0IsR0FBeUIsSUFBSSxDQUFBLENBQUMsU0FBUztRQUczRCw2QkFBdUIsR0FBNEIsSUFBSSxDQUFBLENBQUMsVUFBVTtRQUVsRSwyQkFBcUIsR0FBWSxLQUFLLENBQUMsQ0FBRSxhQUFhO1FBQ3RELHNCQUFnQixHQUFZLEtBQUssQ0FBQyxDQUFFLFdBQVc7UUFFL0MsVUFBVTtRQUNGLHVCQUFpQixHQUFXLElBQUksQ0FBQyxDQUFDLFdBQVc7UUFDN0Msc0JBQWdCLEdBQVcsQ0FBQyxDQUFDLENBQUMsVUFBVTtRQUN4Qyx3QkFBa0IsR0FBVyxDQUFDLENBQUMsQ0FBQyxTQUFTO1FBRWpELFNBQVM7UUFDRCxnQkFBVSxHQUFZLEtBQUssQ0FBQyxDQUFDLGtEQUFrRDtRQUMvRSxnQkFBVSxHQUFXLENBQUMsQ0FBQyxDQUFDLE9BQU87UUFDL0IsMEJBQW9CLEdBQVksS0FBSyxDQUFDLENBQUMsYUFBYTtRQUU1RCxPQUFPO1FBQ0Msb0JBQWMsR0FBVyxDQUFDLENBQUMsQ0FBQyx3QkFBd0I7UUFDcEQsc0JBQWdCLEdBQVcsQ0FBQyxDQUFDLENBQUMsU0FBUztRQUUvQyxvQ0FBb0M7UUFDNUIsNkJBQXVCLEdBQXdCLElBQUksQ0FBQzs7UUErM0M1RCxpQkFBaUI7SUFDckIsQ0FBQztJQTczQ0csbUNBQU0sR0FBTjtRQUFBLGlCQWlDQztRQWhDRyxnQ0FBZ0M7UUFDaEMsSUFBSSxDQUFDLElBQUksQ0FBQyxTQUFTLEVBQUU7WUFDakIsdUJBQXVCO1lBQ3ZCLElBQU0sVUFBVSxHQUFHLEVBQUUsQ0FBQyxJQUFJLENBQUMsZ0JBQWdCLENBQUMsQ0FBQztZQUM3QyxJQUFJLFVBQVUsRUFBRTtnQkFDWixJQUFNLGFBQWEsR0FBRyxVQUFVLENBQUMsY0FBYyxDQUFDLFlBQVksQ0FBQyxDQUFDO2dCQUM5RCxJQUFJLGFBQWEsRUFBRTtvQkFDZixJQUFJLENBQUMsU0FBUyxHQUFHLGFBQWEsQ0FBQyxZQUFZLENBQUMsRUFBRSxDQUFDLEtBQUssQ0FBQyxDQUFDO2lCQUN6RDthQUNKO1NBQ0o7UUFFRCxxQ0FBcUM7UUFDckMsSUFBSSxDQUFDLElBQUksQ0FBQyxjQUFjLEVBQUU7WUFDdEIsNkJBQTZCO1lBQzdCLElBQU0sZUFBZSxHQUFHLEVBQUUsQ0FBQyxJQUFJLENBQUMsc0JBQXNCLENBQUMsQ0FBQztZQUN4RCxJQUFJLGVBQWUsRUFBRTtnQkFDakIsSUFBTSxrQkFBa0IsR0FBRyxlQUFlLENBQUMsY0FBYyxDQUFDLGtCQUFrQixDQUFDLENBQUM7Z0JBQzlFLElBQUksa0JBQWtCLEVBQUU7b0JBQ3BCLElBQUksQ0FBQyxjQUFjLEdBQUcsa0JBQWtCLENBQUMsWUFBWSxDQUFDLEVBQUUsQ0FBQyxLQUFLLENBQUMsQ0FBQztpQkFDbkU7YUFDSjtTQUNKO1FBRUQsa0JBQWtCO1FBQ2pCLE1BQWMsQ0FBQyxhQUFhLEdBQUc7WUFDNUIsS0FBSSxDQUFDLFNBQVMsRUFBRSxDQUFDO1FBQ3JCLENBQUMsQ0FBQztRQUVGLDhCQUE4QjtRQUM3QixNQUFjLENBQUMsa0JBQWtCLEdBQUcsSUFBSSxDQUFDO0lBRTlDLENBQUM7SUFHUyxrQ0FBSyxHQUFmO1FBQUEsaUJBaUJDO1FBaEJFLGFBQUssQ0FBQyxnQkFBZ0IsQ0FBQyxJQUFJLENBQUMsWUFBWSxFQUFFLGVBQU0sQ0FBQyxTQUFTLEdBQUcsc0JBQXNCLEVBQUUsZUFBTSxDQUFDLFNBQVMsR0FBRyx1QkFBdUIsRUFBRTtZQUN0SCxLQUFJLENBQUMscUJBQXFCLEdBQUcsSUFBSSxDQUFBO1lBQ2pDLEtBQUksQ0FBQyxxQkFBcUIsQ0FBQyxJQUFJLENBQUMsQ0FBQyxFQUFDO2dCQUNoQyxLQUFJLENBQUMscUJBQXFCLEdBQUcsS0FBSyxDQUFBO1lBQ3BDLENBQUMsQ0FBQyxDQUFBO1FBQ0wsQ0FBQyxDQUFDLENBQUM7UUFFVixXQUFXO1FBQ1gsSUFBSSxJQUFJLENBQUMsb0JBQW9CLEVBQUU7WUFDM0IsSUFBSSxDQUFDLG9CQUFvQixDQUFDLElBQUksQ0FBQyxFQUFFLENBQUMsbUJBQW1CLEVBQUUsSUFBSSxDQUFDLGlCQUFpQixFQUFFLElBQUksQ0FBQyxDQUFDO1NBQ3hGO1FBRUQsY0FBYztRQUNkLElBQUksSUFBSSxDQUFDLHVCQUF1QixFQUFFO1lBQzlCLElBQUksQ0FBQyx1QkFBdUIsQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLHVCQUF1QixFQUFFLElBQUksQ0FBQyxvQkFBb0IsRUFBRSxJQUFJLENBQUMsQ0FBQztTQUNsRztJQUNMLENBQUM7SUFFRDs7O09BR0c7SUFDSyw4Q0FBaUIsR0FBekIsVUFBMEIsS0FBVTtRQUMxQixJQUFBLEtBQW1CLEtBQUssQ0FBQyxNQUFNLElBQUksS0FBSyxFQUF0QyxDQUFDLE9BQUEsRUFBRSxDQUFDLE9BQUEsRUFBRSxNQUFNLFlBQTBCLENBQUM7UUFFL0MsbUJBQW1CO1FBQ25CLElBQUksQ0FBQyxJQUFJLENBQUMsWUFBWSxFQUFFLEVBQUU7WUFFdEIsT0FBTztTQUNWO1FBRUQsZUFBZTtRQUNmLElBQUksSUFBSSxDQUFDLG9CQUFvQixFQUFFO1lBRTNCLE9BQU87U0FDVjtRQUVELFNBQVM7UUFDVCxJQUFJLENBQUMsY0FBYyxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsTUFBTSxDQUFDLENBQUM7UUFFbEMsaUJBQWlCO1FBQ2pCLElBQUksSUFBSSxDQUFDLG9CQUFvQixFQUFFO1lBQzNCLElBQUksTUFBTSxLQUFLLENBQUMsRUFBRTtnQkFDZCxrQkFBa0I7Z0JBQ2xCLElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLEtBQUssQ0FBQyxDQUFDO2FBQzVEO2lCQUFNLElBQUksTUFBTSxLQUFLLENBQUMsRUFBRTtnQkFDckIsaUJBQWlCO2dCQUNqQixJQUFJLENBQUMsb0JBQW9CLENBQUMsaUJBQWlCLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxJQUFJLENBQUMsQ0FBQzthQUMzRDtTQUNKO1FBRUQsb0JBQW9CO1FBQ3BCLElBQUksQ0FBQyxvQkFBb0IsR0FBRyxJQUFJLENBQUM7SUFFckMsQ0FBQztJQUVEOzs7T0FHRztJQUNLLGlEQUFvQixHQUE1QixVQUE2QixLQUFVO1FBQzdCLElBQUEsS0FBbUIsS0FBSyxDQUFDLE1BQU0sSUFBSSxLQUFLLEVBQXRDLENBQUMsT0FBQSxFQUFFLENBQUMsT0FBQSxFQUFFLE1BQU0sWUFBMEIsQ0FBQztRQUUvQyxtQkFBbUI7UUFDbkIsSUFBSSxDQUFDLElBQUksQ0FBQyxZQUFZLEVBQUUsRUFBRTtZQUN0QixPQUFPO1NBQ1Y7UUFFRCxlQUFlO1FBQ2YsSUFBSSxJQUFJLENBQUMsb0JBQW9CLEVBQUU7WUFDM0IsT0FBTztTQUNWO1FBRUQsaUNBQWlDO1FBQ2pDLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLE1BQU0sQ0FBQyxDQUFDO1FBRXJDLG9CQUFvQjtRQUNwQixJQUFJLElBQUksQ0FBQyx1QkFBdUIsRUFBRTtZQUM5QixJQUFJLE1BQU0sS0FBSyxDQUFDLEVBQUU7Z0JBQ2Qsa0JBQWtCO2dCQUNsQixJQUFJLENBQUMsdUJBQXVCLENBQUMsb0JBQW9CLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxLQUFLLENBQUMsQ0FBQzthQUNsRTtpQkFBTSxJQUFJLE1BQU0sS0FBSyxDQUFDLEVBQUU7Z0JBQ3JCLGlCQUFpQjtnQkFDakIsSUFBSSxDQUFDLHVCQUF1QixDQUFDLG9CQUFvQixDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsSUFBSSxDQUFDLENBQUM7YUFDakU7U0FDSjtRQUVELG9CQUFvQjtRQUNwQixJQUFJLENBQUMsb0JBQW9CLEdBQUcsSUFBSSxDQUFDO0lBQ3JDLENBQUM7SUFHRCxJQUFJO0lBQ0osOENBQWlCLEdBQWpCLFVBQWtCLGdCQUFrQztRQUFwRCxpQkFlQztRQWJHLElBQUksQ0FBQyxXQUFXLENBQUMsZ0JBQWdCLENBQUMsQ0FBQTtRQUVsQyxrQkFBa0I7UUFDbEIsSUFBSSxJQUFJLENBQUMscUJBQXFCLEVBQUU7WUFDNUIsSUFBSSxDQUFDLHFCQUFxQixDQUFDLElBQUksRUFBRSxDQUFBO1NBQ3BDO1FBRUQsSUFBSSxDQUFDLGdCQUFnQixHQUFHLElBQUksQ0FBQTtRQUM1QixRQUFRO1FBQ1IsSUFBSSxDQUFDLHdCQUF3QixDQUFDLElBQUksQ0FBQyxnQkFBZ0IsRUFBRTtZQUNqRCxLQUFJLENBQUMsZ0JBQWdCLEdBQUcsS0FBSyxDQUFBO1FBQ2pDLENBQUMsQ0FBQyxDQUFBO0lBRU4sQ0FBQztJQUVTLHNDQUFTLEdBQW5CO1FBQ0ksa0JBQWtCO1FBQ2xCLElBQUksSUFBSSxDQUFDLHFCQUFxQixFQUFFO1lBQzVCLElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxJQUFJLEVBQUUsQ0FBQTtTQUNwQztRQUVELGlCQUFpQjtRQUNqQixJQUFJLElBQUksQ0FBQyxnQkFBZ0IsRUFBRTtZQUN2QixJQUFJLENBQUMsd0JBQXdCLENBQUMsSUFBSSxFQUFFLENBQUE7U0FDdkM7UUFFRCxRQUFRO1FBQ1IsSUFBSSxDQUFDLG1CQUFtQixFQUFFLENBQUM7SUFDL0IsQ0FBQztJQUdELElBQUk7SUFDSix3Q0FBVyxHQUFYLFVBQVksZ0JBQWtDO1FBQzFDLHNDQUFzQztRQUN0QyxJQUFJLFFBQVEsR0FBRyxnQkFBZ0IsQ0FBQyxZQUFZLElBQUksZ0JBQWdCLENBQUMsS0FBSyxDQUFDO1FBRXZFLGFBQWE7UUFDYixJQUFJLENBQUMsZ0JBQWdCLElBQUksQ0FBQyxRQUFRLElBQUksQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFDLFFBQVEsQ0FBQyxFQUFFO1lBQzVELE9BQU8sQ0FBQyxJQUFJLENBQUMsMEJBQTBCLEVBQUUsZ0JBQWdCLENBQUMsQ0FBQztZQUMzRCwyQkFBWSxDQUFDLFFBQVEsRUFBRSxDQUFDLENBQUMsV0FBVztZQUNwQyxPQUFPO1NBQ1Y7UUFFRCxJQUFNLGFBQWEsR0FBRyx1QkFBVSxDQUFDLFdBQVcsRUFBRSxDQUFDLFNBQVMsQ0FBQyxRQUFRLENBQUMsTUFBTSxDQUFDO1FBQ3pFLElBQU0sS0FBSyxHQUFHLFFBQVEsQ0FBQyxTQUFTLENBQUMsVUFBQyxJQUFJLElBQUssT0FBQSxJQUFJLENBQUMsTUFBTSxLQUFLLGFBQWEsRUFBN0IsQ0FBNkIsQ0FBQyxDQUFDLENBQUEsSUFBSTtRQUM5RSxJQUFJLEtBQUssSUFBSSxDQUFDLEVBQUUsRUFBRSwyQ0FBMkM7WUFDekQsSUFBSSxRQUFRLENBQUMsS0FBSyxDQUFDLENBQUMsSUFBSSxLQUFLLENBQUMsRUFBRSxFQUFFLFlBQVk7Z0JBQzFDLDJCQUFZLENBQUMsUUFBUSxFQUFFLENBQUM7YUFDM0I7aUJBQU07Z0JBQ0gsMkJBQVksQ0FBQyxTQUFTLEVBQUUsQ0FBQzthQUM1QjtTQUNKO2FBQU07WUFDSCwyQkFBWSxDQUFDLFFBQVEsRUFBRSxDQUFDO1NBQzNCO0lBRUwsQ0FBQztJQUVELHVCQUF1QjtJQUN2Qix3Q0FBVyxHQUFYLFVBQVksSUFBcUI7UUFHN0IsU0FBUztRQUNULElBQUksQ0FBQyxjQUFjLEdBQUcsSUFBSSxDQUFDLE9BQU8sSUFBSSxDQUFDLENBQUM7UUFFeEMsbUJBQW1CO1FBQ25CLElBQUksSUFBSSxDQUFDLGNBQWMsS0FBSyxDQUFDLEVBQUU7WUFDM0IsT0FBTztZQUNQLElBQUksSUFBSSxDQUFDLG9CQUFvQixFQUFFO2dCQUUzQixJQUFJLENBQUMsb0JBQW9CLENBQUMsY0FBYyxFQUFFLENBQUM7YUFDOUM7aUJBQU07Z0JBQ0gsT0FBTyxDQUFDLEtBQUssQ0FBQyw2QkFBNkIsQ0FBQyxDQUFDO2FBQ2hEO1NBQ0o7YUFBTSxJQUFJLElBQUksQ0FBQyxjQUFjLEtBQUssQ0FBQyxFQUFFO1lBQ2xDLFFBQVE7WUFDUixJQUFJLElBQUksQ0FBQyx1QkFBdUIsRUFBRTtnQkFFOUIsSUFBSSxDQUFDLHVCQUF1QixDQUFDLGNBQWMsRUFBRSxDQUFDO2dCQUU5QyxpQ0FBaUM7Z0JBRWpDLElBQUksQ0FBQyx1QkFBdUIsQ0FBQyxpQkFBaUIsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFFLGFBQWE7YUFDckU7aUJBQU07Z0JBQ0gsT0FBTyxDQUFDLEtBQUssQ0FBQyxnQ0FBZ0MsQ0FBQyxDQUFDO2FBQ25EO1NBQ0o7UUFFRCxTQUFTO1FBQ1QsSUFBSSxDQUFDLFVBQVUsR0FBRyxLQUFLLENBQUM7UUFDeEIsSUFBSSxDQUFDLG9CQUFvQixHQUFHLEtBQUssQ0FBQztRQUNsQyxJQUFJLENBQUMsa0JBQWtCLEdBQUcsQ0FBQyxDQUFDO1FBQzVCLElBQUksQ0FBQyxnQkFBZ0IsR0FBRyxDQUFDLENBQUM7UUFDMUIsSUFBSSxDQUFDLFVBQVUsR0FBRyxDQUFDLENBQUM7UUFFcEIsZUFBZTtRQUNmLElBQUksSUFBSSxDQUFDLE9BQU8sS0FBSyxDQUFDLElBQUksSUFBSSxDQUFDLFNBQVMsRUFBRTtZQUN0QyxPQUFPO1lBQ1AsSUFBSSxDQUFDLGdCQUFnQixHQUFHLElBQUksQ0FBQyxTQUFTLENBQUMsU0FBUyxJQUFJLEVBQUUsQ0FBQztTQUMxRDthQUFNLElBQUksSUFBSSxDQUFDLE9BQU8sS0FBSyxDQUFDLEVBQUU7WUFDM0IsdUJBQXVCO1lBQ3ZCLElBQUksSUFBSSxDQUFDLHVCQUF1QixFQUFFO2dCQUM5QixJQUFJLENBQUMsZ0JBQWdCLEdBQUcsSUFBSSxDQUFDLHVCQUF1QixDQUFDLHVCQUF1QixFQUFFLENBQUM7YUFDbEY7aUJBQU07Z0JBQ0gsSUFBSSxDQUFDLGdCQUFnQixHQUFHLEVBQUUsQ0FBQyxDQUFDLFFBQVE7YUFDdkM7U0FDSjthQUFNO1lBQ0gsTUFBTTtZQUNOLElBQUksQ0FBQyxnQkFBZ0IsR0FBRyxFQUFFLENBQUM7U0FDOUI7UUFFRCxVQUFVO1FBQ1YsSUFBSSxDQUFDLHNCQUFzQixDQUFDLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDO1FBRW5ELHFCQUFxQjtRQUNyQixJQUFJLENBQUMsZ0JBQWdCLENBQUMsSUFBSSxDQUFDLGNBQWMsQ0FBQyxDQUFDO1FBRTNDLHdCQUF3QjtRQUN4QixJQUFJLElBQUksQ0FBQyxtQkFBbUIsRUFBRTtZQUMxQixJQUFJLENBQUMsbUJBQW1CLENBQUMsbUJBQW1CLEVBQUUsQ0FBQztTQUVsRDtJQUVMLENBQUM7SUFFRDs7T0FFRztJQUNJLHNDQUFTLEdBQWhCO1FBQ0ksSUFBSSxJQUFJLENBQUMsb0JBQW9CLEVBQUU7WUFDM0IsSUFBSSxDQUFDLG9CQUFvQixDQUFDLGNBQWMsRUFBRSxDQUFDO1NBQzlDO2FBQU07WUFDSCxPQUFPLENBQUMsS0FBSyxDQUFDLDZCQUE2QixDQUFDLENBQUM7U0FDaEQ7SUFDTCxDQUFDO0lBSUQsYUFBYTtJQUNiLCtDQUFrQixHQUFsQixVQUFtQixJQUFzQjtRQUdyQyxJQUFJLENBQUMsa0JBQWtCLEdBQUcsSUFBSSxDQUFDLFdBQVcsSUFBSSxDQUFDLENBQUM7UUFDaEQsSUFBSSxDQUFDLGdCQUFnQixHQUFHLElBQUksQ0FBQyxTQUFTLElBQUksRUFBRSxDQUFDO1FBQzdDLElBQUksQ0FBQyxVQUFVLEdBQUcsSUFBSSxDQUFDLFVBQVUsSUFBSSxDQUFDLENBQUM7UUFFdkMsZUFBZTtRQUNmLElBQUksQ0FBQyxVQUFVLEdBQUcsSUFBSSxDQUFDO1FBQ3ZCLElBQUksQ0FBQyxvQkFBb0IsR0FBRyxLQUFLLENBQUM7UUFFbEMsZ0JBQWdCO1FBQ2hCLElBQUksSUFBSSxDQUFDLGNBQWMsS0FBSyxDQUFDLElBQUksSUFBSSxDQUFDLG9CQUFvQixFQUFFO1lBQ3hELE9BQU87WUFDUCxJQUFJLENBQUMsb0JBQW9CLENBQUMsbUJBQW1CLEVBQUUsQ0FBQztTQUNuRDthQUFNLElBQUksSUFBSSxDQUFDLGNBQWMsS0FBSyxDQUFDLElBQUksSUFBSSxDQUFDLHVCQUF1QixFQUFFO1lBQ2xFLFFBQVE7WUFDUixJQUFJLENBQUMsdUJBQXVCLENBQUMsbUJBQW1CLEVBQUUsQ0FBQztTQUN0RDtRQUlELFFBQVE7UUFDUixJQUFJLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDO0lBQy9DLENBQUM7SUFFRCxhQUFhO0lBQ2Isa0RBQXFCLEdBQXJCLFVBQXNCLElBQXlCO1FBRTNDLHNDQUFzQztRQUN0QyxJQUFJLENBQUMsdUJBQXVCLEdBQUcsSUFBSSxDQUFDO1FBRXBDLGVBQWU7UUFDZixJQUFJLENBQUMsVUFBVSxHQUFHLEtBQUssQ0FBQztRQUN4QixJQUFJLENBQUMsVUFBVSxHQUFHLElBQUksQ0FBQyxVQUFVLElBQUksQ0FBQyxDQUFDO1FBRXZDLHNCQUFzQjtRQUN0QixJQUFJLENBQUMsZ0JBQWdCLEdBQUcsSUFBSSxDQUFDLFNBQVMsSUFBSSxDQUFDLENBQUM7UUFDNUMsSUFBSSxDQUFDLHNCQUFzQixDQUFDLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDO1FBQ25ELElBQUksQ0FBQyxjQUFjLENBQUMsSUFBSSxDQUFDLGdCQUFnQixDQUFDLENBQUM7UUFFM0MsYUFBYTtRQUNiLElBQUksSUFBSSxDQUFDLGNBQWMsS0FBSyxTQUFTLEVBQUU7WUFDbkMsSUFBSSxDQUFDLHNCQUFzQixDQUFDLElBQUksQ0FBQyxjQUFjLENBQUMsQ0FBQztTQUNwRDtRQUVELG9CQUFvQjtRQUNwQixJQUFJLENBQUMsb0JBQW9CLENBQUMsSUFBSSxDQUFDLGFBQWEsRUFBRSxJQUFJLENBQUMsaUJBQWlCLENBQUMsQ0FBQztRQUV0RSxvQkFBb0I7UUFDcEIsSUFBSSxDQUFDLCtCQUErQixDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsQ0FBQztJQUM3RCxDQUFDO0lBRUQ7OztPQUdHO0lBQ0ssNERBQStCLEdBQXZDLFVBQXdDLGFBQW9DOztRQUN4RSxXQUFXO1FBQ1gsSUFBTSxhQUFhLGVBQUcsdUJBQVUsQ0FBQyxXQUFXLEVBQUUsQ0FBQyxTQUFTLDBDQUFFLFFBQVEsMENBQUUsTUFBTSxDQUFDO1FBQzNFLElBQUksQ0FBQyxhQUFhLEVBQUU7WUFDaEIsT0FBTyxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQztZQUMzQixPQUFPO1NBQ1Y7UUFFRCxTQUFTO1FBQ1QsSUFBTSxpQkFBaUIsR0FBRyxhQUFhLENBQUMsSUFBSSxDQUFDLFVBQUEsTUFBTSxJQUFJLE9BQUEsTUFBTSxDQUFDLGFBQWEsRUFBcEIsQ0FBb0IsQ0FBQyxDQUFDO1FBRTdFLElBQUksaUJBQWlCLEVBQUU7WUFDbkIsSUFBTSxvQkFBb0IsR0FBRyxJQUFJLENBQUMsYUFBYSxDQUFDLGlCQUFpQixDQUFDLE1BQU0sQ0FBQyxDQUFDO1lBQzFFLElBQUksb0JBQW9CLEtBQUssQ0FBQyxDQUFDLEVBQUU7Z0JBQzdCLFdBQVc7Z0JBQ1gsSUFBSSxDQUFDLHFCQUFxQixDQUFDLG9CQUFvQixFQUFFLENBQUMsQ0FBQyxDQUFDO2dCQUNwRCwyQkFBMkI7Z0JBQzNCLElBQUksQ0FBQyx1QkFBdUIsQ0FBQyxpQkFBaUIsQ0FBQyxNQUFNLEVBQUUsQ0FBQyxDQUFDLENBQUM7YUFDN0Q7U0FDSjtJQUNMLENBQUM7SUFFRDs7O09BR0c7SUFDSywrQ0FBa0IsR0FBMUIsVUFBMkIsSUFBeUI7UUFFaEQsSUFBSSxDQUFDLHVCQUF1QixDQUFDLElBQUksQ0FBQyxDQUFDO0lBQ3ZDLENBQUM7SUFFRDs7O09BR0c7SUFDSyxvREFBdUIsR0FBL0IsVUFBZ0MsSUFBeUI7UUFDckQseUNBQXlDO1FBRDdDLGlCQWlDQztRQTlCRyxnQkFBZ0I7UUFDaEIsd0JBQXdCO1FBQ3hCLElBQU0sa0JBQWtCLEdBQUcsSUFBSSxHQUFHLEVBQVUsQ0FBQztRQUU3QyxJQUFJLENBQUMsYUFBYSxDQUFDLE9BQU8sQ0FBQyxVQUFBLE1BQU07WUFDN0IsSUFBTSxXQUFXLEdBQU0sTUFBTSxDQUFDLENBQUMsU0FBSSxNQUFNLENBQUMsQ0FBRyxDQUFDO1lBRTlDLGlCQUFpQjtZQUNqQixJQUFJLGtCQUFrQixDQUFDLEdBQUcsQ0FBQyxXQUFXLENBQUMsRUFBRTtnQkFDckMsT0FBTzthQUNWO1lBRUQsY0FBYztZQUNkLElBQU0sbUJBQW1CLEdBQUcsSUFBSSxDQUFDLGFBQWEsQ0FBQyxNQUFNLENBQUMsVUFBQSxDQUFDO2dCQUNuRCxPQUFBLENBQUMsQ0FBQyxDQUFDLEtBQUssTUFBTSxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxLQUFLLE1BQU0sQ0FBQyxDQUFDO1lBQXBDLENBQW9DLENBQ3ZDLENBQUM7WUFFRix5QkFBeUI7WUFDekIsS0FBSSxDQUFDLHFCQUFxQixDQUFDLE1BQU0sQ0FBQyxDQUFDLEVBQUUsTUFBTSxDQUFDLENBQUMsRUFBRSxtQkFBbUIsQ0FBQyxDQUFDO1lBRXBFLFlBQVk7WUFDWixrQkFBa0IsQ0FBQyxHQUFHLENBQUMsV0FBVyxDQUFDLENBQUM7UUFDeEMsQ0FBQyxDQUFDLENBQUM7UUFFSCxXQUFXO1FBQ1gsSUFBSSxJQUFJLENBQUMsZ0JBQWdCLElBQUksSUFBSSxDQUFDLGdCQUFnQixDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUU7WUFDM0QsSUFBSSxDQUFDLGdCQUFnQixDQUFDLE9BQU8sQ0FBQyxVQUFBLFNBQVM7Z0JBQ25DLEtBQUksQ0FBQyxzQkFBc0IsQ0FBQyxTQUFTLENBQUMsQ0FBQztZQUMzQyxDQUFDLENBQUMsQ0FBQztTQUNOO0lBQ0wsQ0FBQztJQUVEOzs7O09BSUc7SUFDSywyQ0FBYyxHQUF0QixVQUF1QixhQUFvQyxFQUFFLFVBQXNCO1FBQy9FLGlCQUFpQjtRQUNqQixJQUFJLElBQUksQ0FBQyxjQUFjLEtBQUssQ0FBQyxJQUFJLElBQUksQ0FBQyxvQkFBb0IsRUFBRTtZQUN4RCx3QkFBd0I7WUFDeEIsSUFBSSxDQUFDLG9CQUFvQixDQUFDLHFCQUFxQixDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUU7Z0JBQ2xELFVBQVUsRUFBRSxDQUFDO1lBQ2pCLENBQUMsQ0FBQyxDQUFDO1NBQ047YUFBTSxJQUFJLElBQUksQ0FBQyxjQUFjLEtBQUssQ0FBQyxJQUFJLElBQUksQ0FBQyx1QkFBdUIsRUFBRTtZQUNsRSx1QkFBdUI7WUFDdkIsSUFBSSxDQUFDLHVCQUF1QixDQUFDLGlCQUFpQixDQUFDO2dCQUMzQyxVQUFVLEVBQUUsQ0FBQztZQUNqQixDQUFDLENBQUMsQ0FBQztTQUNOO2FBQU07WUFDSCxrQkFBa0I7WUFDbEIsT0FBTyxDQUFDLElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxDQUFDO1lBQ3BDLFVBQVUsRUFBRSxDQUFDO1NBQ2hCO0lBQ0wsQ0FBQztJQUVEOzs7OztPQUtHO0lBQ0ssa0RBQXFCLEdBQTdCLFVBQThCLENBQVMsRUFBRSxDQUFTLEVBQUUsT0FBOEI7O1FBQzlFLDZCQUE2QjtRQUM3QixJQUFJLElBQUksQ0FBQyxjQUFjLEtBQUssQ0FBQyxFQUFFO1lBQzNCLE9BQU87WUFDUCxJQUFJLENBQUMsb0JBQW9CLENBQUMsWUFBWSxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsSUFBSSxDQUFDLENBQUM7U0FDdEQ7YUFBTSxJQUFJLElBQUksQ0FBQyxjQUFjLEtBQUssQ0FBQyxFQUFFO1lBQ2xDLHdCQUF3QjtZQUN4QixJQUFJLElBQUksQ0FBQyx1QkFBdUIsRUFBRTtnQkFDOUIsSUFBSSxDQUFDLHVCQUF1QixDQUFDLGFBQWEsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLElBQUksQ0FBQyxDQUFDO2FBQzFEO1NBQ0o7UUFFRCxxQ0FBcUM7UUFDckMsSUFBTSxlQUFlLEdBQUcsT0FBTyxDQUFDLElBQUksQ0FBQyxVQUFBLE1BQU07WUFDdkMsT0FBQSxNQUFNLENBQUMsTUFBTSxLQUFLLENBQUMsSUFBSSxNQUFNLENBQUMsTUFBTSxLQUFLLE1BQU07UUFBL0MsQ0FBK0MsQ0FDbEQsQ0FBQztRQUVGLElBQUksZUFBZSxFQUFFO1lBQ2pCLDBCQUEwQjtZQUMxQixnQkFBZ0I7WUFDaEIsSUFBTSxhQUFhLGVBQUcsdUJBQVUsQ0FBQyxXQUFXLEVBQUUsQ0FBQyxTQUFTLDBDQUFFLFFBQVEsMENBQUUsTUFBTSxDQUFDO1lBQzNFLElBQU0sYUFBYSxHQUFHLGVBQWUsQ0FBQyxNQUFNLEtBQUssYUFBYSxDQUFDO1lBRS9ELGdCQUFnQjtZQUNoQixJQUFJLElBQUksQ0FBQyxjQUFjLEtBQUssQ0FBQyxFQUFFO2dCQUMzQixPQUFPO2dCQUNQLElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLGFBQWEsQ0FBQyxDQUFDO2FBQ25FO2lCQUFNLElBQUksSUFBSSxDQUFDLGNBQWMsS0FBSyxDQUFDLEVBQUU7Z0JBQ2xDLHdCQUF3QjtnQkFDeEIsSUFBSSxJQUFJLENBQUMsdUJBQXVCLEVBQUU7b0JBQzlCLElBQUksQ0FBQyx1QkFBdUIsQ0FBQyxtQkFBbUIsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLGFBQWEsQ0FBQyxDQUFDO2lCQUN6RTthQUNKO1lBQ0QsT0FBTztTQUNWO1FBRUQsMkJBQTJCO1FBQzNCLElBQU0sV0FBVyxHQUFHLE9BQU8sQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUMvQixJQUFNLE1BQU0sR0FBRyxXQUFXLENBQUMsTUFBTSxDQUFDO1FBRWxDLElBQUksTUFBTSxLQUFLLGNBQWMsRUFBRTtZQUMzQixtQkFBbUI7WUFDbkIsSUFBSSxJQUFJLENBQUMsY0FBYyxLQUFLLENBQUMsRUFBRTtnQkFDM0IsT0FBTztnQkFDUCxJQUFJLENBQUMsb0JBQW9CLENBQUMsa0JBQWtCLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO2FBQ3REO2lCQUFNLElBQUksSUFBSSxDQUFDLGNBQWMsS0FBSyxDQUFDLEVBQUU7Z0JBQ2xDLFFBQVE7Z0JBQ1IsSUFBSSxJQUFJLENBQUMsdUJBQXVCLEVBQUU7b0JBQzlCLElBQUksQ0FBQyx1QkFBdUIsQ0FBQyxxQkFBcUIsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7aUJBQzVEO2FBQ0o7U0FDSjthQUFNLElBQUksT0FBTyxNQUFNLEtBQUssUUFBUSxFQUFFO1lBQ25DLHVCQUF1QjtZQUN2QixJQUFJLElBQUksQ0FBQyxjQUFjLEtBQUssQ0FBQyxFQUFFO2dCQUMzQixPQUFPO2dCQUNQLElBQUksQ0FBQyxvQkFBb0IsQ0FBQywwQkFBMEIsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLE1BQU0sQ0FBQyxDQUFDO2FBQ3RFO2lCQUFNLElBQUksSUFBSSxDQUFDLGNBQWMsS0FBSyxDQUFDLEVBQUU7Z0JBQ2xDLFFBQVE7Z0JBQ1IsSUFBSSxJQUFJLENBQUMsdUJBQXVCLEVBQUU7b0JBQzlCLElBQUksQ0FBQyx1QkFBdUIsQ0FBQyw2QkFBNkIsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLE1BQU0sQ0FBQyxDQUFDO2lCQUM1RTthQUNKO1NBQ0o7SUFDTCxDQUFDO0lBRUQ7OztPQUdHO0lBQ0ssc0RBQXlCLEdBQWpDLFVBQWtDLE1BQTJCOztRQUN6RCxJQUFNLENBQUMsR0FBRyxNQUFNLENBQUMsQ0FBQyxDQUFDO1FBQ25CLElBQU0sQ0FBQyxHQUFHLE1BQU0sQ0FBQyxDQUFDLENBQUM7UUFDbkIsSUFBTSxNQUFNLEdBQUcsTUFBTSxDQUFDLE1BQU0sQ0FBQztRQUU3QixXQUFXO1FBQ1gsSUFBSSxDQUFDLG9CQUFvQixDQUFDLFlBQVksQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7UUFFN0MsZUFBZTtRQUNmLElBQUksTUFBTSxLQUFLLE1BQU0sRUFBRTtZQUNuQixlQUFlO1lBQ2YsZ0JBQWdCO1lBQ2hCLElBQU0sYUFBYSxlQUFHLHVCQUFVLENBQUMsV0FBVyxFQUFFLENBQUMsU0FBUywwQ0FBRSxRQUFRLDBDQUFFLE1BQU0sQ0FBQztZQUMzRSxJQUFNLGFBQWEsR0FBRyxNQUFNLENBQUMsTUFBTSxLQUFLLGFBQWEsQ0FBQztZQUN0RCxJQUFJLENBQUMsb0JBQW9CLENBQUMsZ0JBQWdCLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxhQUFhLENBQUMsQ0FBQztTQUNuRTthQUFNLElBQUksTUFBTSxLQUFLLGNBQWMsRUFBRTtZQUNsQyxtQkFBbUI7WUFDbkIsSUFBSSxDQUFDLG9CQUFvQixDQUFDLGtCQUFrQixDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQztTQUN0RDthQUFNLElBQUksT0FBTyxNQUFNLEtBQUssUUFBUSxFQUFFO1lBQ25DLHVCQUF1QjtZQUN2QixJQUFJLENBQUMsb0JBQW9CLENBQUMsMEJBQTBCLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxNQUFNLENBQUMsQ0FBQztTQUN0RTtJQUNMLENBQUM7SUFFRDs7O09BR0c7SUFDSyxtREFBc0IsR0FBOUIsVUFBK0IsU0FBMEI7UUFBekQsaUJBc0JDO1FBckJHLG1CQUFtQjtRQUNuQixTQUFTLENBQUMsY0FBYyxDQUFDLE9BQU8sQ0FBQyxVQUFDLEtBQUs7WUFDbkMsSUFBSSxLQUFJLENBQUMsY0FBYyxLQUFLLENBQUMsRUFBRTtnQkFDM0IsbUJBQW1CO2dCQUNuQixLQUFJLENBQUMsb0JBQW9CLENBQUMsWUFBWSxDQUFDLEtBQUssQ0FBQyxDQUFDLEVBQUUsS0FBSyxDQUFDLENBQUMsRUFBRSxJQUFJLENBQUMsQ0FBQztnQkFDL0QsU0FBUztnQkFDVCxJQUFJLEtBQUssQ0FBQyxhQUFhLEdBQUcsQ0FBQyxFQUFFO29CQUN6QixLQUFJLENBQUMsb0JBQW9CLENBQUMsMEJBQTBCLENBQUMsS0FBSyxDQUFDLENBQUMsRUFBRSxLQUFLLENBQUMsQ0FBQyxFQUFFLEtBQUssQ0FBQyxhQUFhLENBQUMsQ0FBQztpQkFDL0Y7YUFDSjtpQkFBTSxJQUFJLEtBQUksQ0FBQyxjQUFjLEtBQUssQ0FBQyxFQUFFO2dCQUNsQyx3QkFBd0I7Z0JBQ3hCLElBQUksS0FBSSxDQUFDLHVCQUF1QixFQUFFO29CQUM5QixTQUFTO29CQUNULEtBQUksQ0FBQyx1QkFBdUIsQ0FBQyxhQUFhLENBQUMsS0FBSyxDQUFDLENBQUMsRUFBRSxLQUFLLENBQUMsQ0FBQyxFQUFFLElBQUksQ0FBQyxDQUFDO29CQUNuRSxnQkFBZ0I7b0JBQ2hCLElBQUksS0FBSyxDQUFDLGFBQWEsR0FBRyxDQUFDLEVBQUU7d0JBQ3pCLEtBQUksQ0FBQyx1QkFBdUIsQ0FBQyw2QkFBNkIsQ0FBQyxLQUFLLENBQUMsQ0FBQyxFQUFFLEtBQUssQ0FBQyxDQUFDLEVBQUUsS0FBSyxDQUFDLGFBQWEsQ0FBQyxDQUFDO3FCQUNyRztpQkFDSjthQUNKO1FBQ0wsQ0FBQyxDQUFDLENBQUM7SUFDUCxDQUFDO0lBRUQsYUFBYTtJQUNiLDZDQUFnQixHQUFoQixVQUFpQixJQUFvQjtRQUVqQyxpQkFBaUI7UUFDakIsSUFBSSxDQUFDLFVBQVUsR0FBRyxLQUFLLENBQUM7UUFDeEIsSUFBSSxDQUFDLFVBQVUsR0FBRyxJQUFJLENBQUMsVUFBVSxJQUFJLENBQUMsQ0FBQztRQUV2QyxnQ0FBZ0M7UUFFaEMsZ0JBQWdCO1FBQ2hCLElBQUksSUFBSSxDQUFDLGFBQWEsSUFBSSxJQUFJLENBQUMsYUFBYSxDQUFDLE1BQU0sR0FBRyxDQUFDLEVBQUU7WUFDckQsSUFBSSxDQUFDLDRCQUE0QixDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsQ0FBQztZQUV0RCwwQkFBMEI7WUFDMUIsSUFBSSxDQUFDLDJCQUEyQixDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsQ0FBQztTQUN4RDtRQUVELGdCQUFnQjtRQUNoQixJQUFJLElBQUksQ0FBQyxjQUFjLEtBQUssQ0FBQyxJQUFJLElBQUksQ0FBQyxvQkFBb0IsRUFBRTtZQUN4RCxPQUFPO1lBQ1AsSUFBSSxDQUFDLG9CQUFvQixDQUFDLG1CQUFtQixFQUFFLENBQUM7U0FDbkQ7YUFBTSxJQUFJLElBQUksQ0FBQyxjQUFjLEtBQUssQ0FBQyxJQUFJLElBQUksQ0FBQyx1QkFBdUIsRUFBRTtZQUNsRSxRQUFRO1lBQ1IsSUFBSSxDQUFDLHVCQUF1QixDQUFDLGVBQWUsRUFBRSxDQUFDO1NBQ2xEO0lBQ0wsQ0FBQztJQUVEOzs7O09BSUc7SUFDSyxpREFBb0IsR0FBNUIsVUFBNkIsYUFBb0MsRUFBRSxpQkFBOEM7UUFBakgsaUJBbUVDOztRQWxFRyxnQkFBZ0I7UUFDaEIsSUFBTSxjQUFjLEdBQUcsSUFBSSxDQUFDLG9CQUFvQixJQUFJLElBQUksQ0FBQyxjQUFjLEtBQUssQ0FBQyxDQUFDO1FBQzlFLElBQU0sV0FBVyxHQUFHLElBQUksQ0FBQyx1QkFBdUIsSUFBSSxJQUFJLENBQUMsY0FBYyxLQUFLLENBQUMsQ0FBQztRQUU5RSxJQUFJLENBQUMsQ0FBQyxjQUFjLElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLGFBQWEsSUFBSSxhQUFhLENBQUMsTUFBTSxLQUFLLENBQUMsRUFBRTtZQUNuRixPQUFPO1NBQ1Y7UUFFRCxXQUFXO1FBQ1gsSUFBTSxhQUFhLGVBQUcsdUJBQVUsQ0FBQyxXQUFXLEVBQUUsQ0FBQyxTQUFTLDBDQUFFLFFBQVEsMENBQUUsTUFBTSxDQUFDO1FBQzNFLElBQUksQ0FBQyxhQUFhLEVBQUU7WUFDaEIsT0FBTyxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQztZQUMzQixPQUFPO1NBQ1Y7UUFFRCxvREFBb0Q7UUFFcEQsOEJBQThCO1FBQzlCLElBQU0sUUFBUSxHQUFHLGFBQWEsQ0FBQyxJQUFJLENBQUMsVUFBQSxNQUFNLElBQUksT0FBQSxNQUFNLENBQUMsTUFBTSxLQUFLLGFBQWEsRUFBL0IsQ0FBK0IsQ0FBQyxDQUFDO1FBQy9FLElBQUkscUJBQXFCLEdBQUcsS0FBSyxDQUFDO1FBRWxDLElBQUksQ0FBQyxJQUFJLENBQUMsb0JBQW9CLElBQUksUUFBUSxFQUFFO1lBQ3hDLHFCQUFxQixHQUFHLElBQUksQ0FBQztZQUU3QixTQUFTO1lBQ1QsSUFBTSxRQUFRLEdBQUcsQ0FBQyxRQUFRLENBQUMsTUFBTSxLQUFLLENBQUMsQ0FBQyxDQUFDLENBQUMsc0JBQXNCO1lBRWhFLElBQUksSUFBSSxDQUFDLGNBQWMsS0FBSyxDQUFDLEVBQUU7Z0JBQzNCLE9BQU87Z0JBQ1AsSUFBSSxDQUFDLG9CQUFvQixDQUFDLGlCQUFpQixDQUFDLFFBQVEsQ0FBQyxDQUFDLEVBQUUsUUFBUSxDQUFDLENBQUMsRUFBRSxRQUFRLENBQUMsQ0FBQzthQUNqRjtpQkFBTSxJQUFJLElBQUksQ0FBQyxjQUFjLEtBQUssQ0FBQyxFQUFFO2dCQUNsQyx3QkFBd0I7Z0JBQ3hCLElBQUksSUFBSSxDQUFDLHVCQUF1QixFQUFFO29CQUM5QixJQUFJLENBQUMsdUJBQXVCLENBQUMsb0JBQW9CLENBQUMsUUFBUSxDQUFDLENBQUMsRUFBRSxRQUFRLENBQUMsQ0FBQyxFQUFFLFFBQVEsQ0FBQyxDQUFDO2lCQUN2RjthQUNKO1NBQ0o7UUFFRCxzQkFBc0I7UUFDdEIsSUFBTSxtQkFBbUIsR0FBRyxhQUFhLENBQUMsTUFBTSxDQUFDLFVBQUEsTUFBTSxJQUFJLE9BQUEsTUFBTSxDQUFDLE1BQU0sS0FBSyxhQUFhLEVBQS9CLENBQStCLENBQUMsQ0FBQztRQUk1RixJQUFJLG1CQUFtQixDQUFDLE1BQU0sS0FBSyxDQUFDLEVBQUU7WUFFbEMsT0FBTztTQUNWO1FBRUQsZUFBZTtRQUNmLElBQU0sY0FBYyxHQUFHLElBQUksQ0FBQyxzQkFBc0IsQ0FBQyxtQkFBbUIsQ0FBQyxDQUFDO1FBRXhFLGFBQWE7UUFDYixjQUFjLENBQUMsT0FBTyxDQUFDLFVBQUMsT0FBTyxFQUFFLFdBQVc7WUFDbEMsSUFBQSxLQUFTLFdBQVcsQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLENBQUMsR0FBRyxDQUFDLE1BQU0sQ0FBQyxFQUExQyxDQUFDLFFBQUEsRUFBRSxDQUFDLFFBQXNDLENBQUM7WUFFbEQsSUFBSSxLQUFJLENBQUMsY0FBYyxLQUFLLENBQUMsRUFBRTtnQkFDM0IsT0FBTztnQkFDUCxLQUFJLENBQUMsb0JBQW9CLENBQUMsNkJBQTZCLENBQUMsQ0FBQyxFQUFFLENBQUMsRUFBRSxPQUFPLENBQUMsQ0FBQzthQUMxRTtpQkFBTSxJQUFJLEtBQUksQ0FBQyxjQUFjLEtBQUssQ0FBQyxFQUFFO2dCQUNsQyx3QkFBd0I7Z0JBQ3hCLElBQUksS0FBSSxDQUFDLHVCQUF1QixFQUFFO29CQUM5QixpQkFBaUI7b0JBQ2pCLEtBQUksQ0FBQyx1QkFBdUIsQ0FBQyxnQ0FBZ0MsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxFQUFFLE9BQU8sQ0FBQyxDQUFDO2lCQUNoRjthQUNKO1FBQ0wsQ0FBQyxDQUFDLENBQUM7SUFDUCxDQUFDO0lBRUQ7Ozs7T0FJRztJQUNLLG1EQUFzQixHQUE5QixVQUErQixhQUFvQztRQUMvRCxJQUFNLE1BQU0sR0FBRyxJQUFJLEdBQUcsRUFBaUMsQ0FBQztRQUV4RCxLQUFxQixVQUFhLEVBQWIsK0JBQWEsRUFBYiwyQkFBYSxFQUFiLElBQWEsRUFBRTtZQUEvQixJQUFNLE1BQU0sc0JBQUE7WUFDYixJQUFNLFdBQVcsR0FBTSxNQUFNLENBQUMsQ0FBQyxTQUFJLE1BQU0sQ0FBQyxDQUFHLENBQUM7WUFFOUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxHQUFHLENBQUMsV0FBVyxDQUFDLEVBQUU7Z0JBQzFCLE1BQU0sQ0FBQyxHQUFHLENBQUMsV0FBVyxFQUFFLEVBQUUsQ0FBQyxDQUFDO2FBQy9CO1lBRUQsTUFBTSxDQUFDLEdBQUcsQ0FBQyxXQUFXLENBQUUsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUM7U0FDekM7UUFFRCxPQUFPLE1BQU0sQ0FBQztJQUNsQixDQUFDO0lBSUQ7OztPQUdHO0lBQ0sseURBQTRCLEdBQXBDLFVBQXFDLGFBQWtDO1FBQXZFLGlCQWtCQzs7UUFoQkcsV0FBVztRQUNYLElBQU0sYUFBYSxlQUFHLHVCQUFVLENBQUMsV0FBVyxFQUFFLENBQUMsU0FBUywwQ0FBRSxRQUFRLDBDQUFFLE1BQU0sQ0FBQztRQUMzRSxJQUFJLENBQUMsYUFBYSxFQUFFO1lBQ2hCLE9BQU8sQ0FBQyxJQUFJLENBQUMsWUFBWSxDQUFDLENBQUM7WUFDM0IsT0FBTztTQUNWO1FBRUQsY0FBYztRQUNkLGFBQWEsQ0FBQyxPQUFPLENBQUMsVUFBQyxNQUFNLEVBQUUsS0FBSztZQUdoQyxhQUFhO1lBQ2IsS0FBSSxDQUFDLFlBQVksQ0FBQztnQkFDZCxLQUFJLENBQUMsd0JBQXdCLENBQUMsTUFBTSxFQUFFLGFBQWEsQ0FBQyxDQUFDO1lBQ3pELENBQUMsRUFBRSxLQUFLLEdBQUcsR0FBRyxDQUFDLENBQUM7UUFDcEIsQ0FBQyxDQUFDLENBQUM7SUFDUCxDQUFDO0lBRUQ7Ozs7T0FJRztJQUNLLHFEQUF3QixHQUFoQyxVQUFpQyxNQUF5QixFQUFFLGFBQXFCO1FBQzdFLElBQU0sUUFBUSxHQUFHLE1BQU0sQ0FBQyxNQUFNLEtBQUssYUFBYSxDQUFDO1FBRWpELElBQUksUUFBUSxFQUFFO1lBQ1Ysb0NBQW9DO1lBQ3BDLElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxNQUFNLENBQUMsQ0FBQztTQUNyQzthQUFNO1lBQ0gsaUNBQWlDO1lBQ2pDLElBQUksQ0FBQyw2QkFBNkIsQ0FBQyxNQUFNLENBQUMsQ0FBQztTQUM5QztJQUNMLENBQUM7SUFFRDs7O09BR0c7SUFDSyxpREFBb0IsR0FBNUIsVUFBNkIsTUFBeUI7UUFDbEQscUJBQXFCO1FBQ3JCLElBQUksSUFBSSxDQUFDLGNBQWMsS0FBSyxDQUFDLElBQUksSUFBSSxDQUFDLG9CQUFvQixFQUFFO1lBQ3hELE9BQU87WUFDUCxJQUFJLENBQUMsb0JBQW9CLENBQUMscUJBQXFCLENBQUMsTUFBTSxDQUFDLENBQUMsRUFBRSxNQUFNLENBQUMsQ0FBQyxFQUFFLE1BQU0sQ0FBQyxLQUFLLEVBQUUsS0FBSyxDQUFDLENBQUM7U0FDNUY7YUFBTSxJQUFJLElBQUksQ0FBQyxjQUFjLEtBQUssQ0FBQyxJQUFJLElBQUksQ0FBQyx1QkFBdUIsRUFBRTtZQUNsRSx3QkFBd0I7WUFDeEIsSUFBSSxDQUFDLHVCQUF1QixDQUFDLHdCQUF3QixDQUFDLE1BQU0sQ0FBQyxDQUFDLEVBQUUsTUFBTSxDQUFDLENBQUMsRUFBRSxNQUFNLENBQUMsS0FBSyxFQUFFLEtBQUssQ0FBQyxDQUFDO1NBQ2xHO1FBRUQsMkJBQTJCO1FBQzNCLElBQUksQ0FBQyw4QkFBOEIsQ0FBQyxNQUFNLENBQUMsTUFBTSxFQUFFLE1BQU0sQ0FBQyxLQUFLLEVBQUUsTUFBTSxDQUFDLGFBQWEsQ0FBQyxDQUFDO0lBQzNGLENBQUM7SUFFRDs7O09BR0c7SUFDSywwREFBNkIsR0FBckMsVUFBc0MsTUFBeUI7UUFDM0QsSUFBSSxNQUFNLENBQUMsYUFBYSxFQUFFO1lBQ3RCLHdDQUF3QztZQUN4QyxJQUFJLElBQUksQ0FBQyxjQUFjLEtBQUssQ0FBQyxJQUFJLElBQUksQ0FBQyxvQkFBb0IsRUFBRTtnQkFDeEQsT0FBTztnQkFDUCxJQUFJLENBQUMsb0JBQW9CLENBQUMscUJBQXFCLENBQUMsTUFBTSxDQUFDLENBQUMsRUFBRSxNQUFNLENBQUMsQ0FBQyxFQUFFLE1BQU0sQ0FBQyxLQUFLLEVBQUUsS0FBSyxDQUFDLENBQUM7YUFDNUY7aUJBQU0sSUFBSSxJQUFJLENBQUMsY0FBYyxLQUFLLENBQUMsSUFBSSxJQUFJLENBQUMsdUJBQXVCLEVBQUU7Z0JBQ2xFLFFBQVE7Z0JBQ1IsSUFBSSxDQUFDLHVCQUF1QixDQUFDLHdCQUF3QixDQUFDLE1BQU0sQ0FBQyxDQUFDLEVBQUUsTUFBTSxDQUFDLENBQUMsRUFBRSxNQUFNLENBQUMsS0FBSyxFQUFFLEtBQUssQ0FBQyxDQUFDO2FBQ2xHO1lBRUQsMENBQTBDO1lBQzFDLElBQUksQ0FBQyw2QkFBNkIsQ0FBQyxNQUFNLENBQUMsTUFBTSxFQUFFLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQztTQUNuRTthQUFNO1lBQ0gsbUJBQW1CO1lBQ25CLElBQUksSUFBSSxDQUFDLGNBQWMsS0FBSyxDQUFDLElBQUksSUFBSSxDQUFDLG9CQUFvQixFQUFFO2dCQUN4RCxPQUFPO2dCQUNQLElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxxQkFBcUIsQ0FBQyxNQUFNLENBQUMsQ0FBQyxFQUFFLE1BQU0sQ0FBQyxDQUFDLEVBQUUsTUFBTSxDQUFDLEtBQUssRUFBRSxLQUFLLENBQUMsQ0FBQzthQUM1RjtpQkFBTSxJQUFJLElBQUksQ0FBQyxjQUFjLEtBQUssQ0FBQyxJQUFJLElBQUksQ0FBQyx1QkFBdUIsRUFBRTtnQkFDbEUsUUFBUTtnQkFDUixJQUFJLENBQUMsdUJBQXVCLENBQUMsd0JBQXdCLENBQUMsTUFBTSxDQUFDLENBQUMsRUFBRSxNQUFNLENBQUMsQ0FBQyxFQUFFLE1BQU0sQ0FBQyxLQUFLLEVBQUUsS0FBSyxDQUFDLENBQUM7YUFDbEc7WUFFRCwyQkFBMkI7WUFDM0IsSUFBSSxDQUFDLDhCQUE4QixDQUFDLE1BQU0sQ0FBQyxNQUFNLEVBQUUsTUFBTSxDQUFDLEtBQUssRUFBRSxLQUFLLENBQUMsQ0FBQztTQUMzRTtJQUNMLENBQUM7SUFFRDs7Ozs7T0FLRztJQUNLLDJEQUE4QixHQUF0QyxVQUF1QyxNQUFjLEVBQUUsS0FBYSxFQUFFLGFBQXNCO1FBQ3hGLHdDQUF3QztRQUN4QyxxQkFBcUI7UUFHckIsc0NBQXNDO1FBQ3RDLG9EQUFvRDtJQUN4RCxDQUFDO0lBRUQ7Ozs7T0FJRztJQUNLLDBEQUE2QixHQUFyQyxVQUFzQyxNQUFjLEVBQUUsS0FBYTtRQUFuRSxpQkFpQkM7UUFkRyxhQUFhO1FBQ2IsSUFBSSxDQUFDLFlBQVksQ0FBQztZQUNkLEtBQUksQ0FBQyw4QkFBOEIsQ0FBQyxNQUFNLEVBQUUsQ0FBQyxFQUFFLElBQUksQ0FBQyxDQUFDO1FBQ3pELENBQUMsRUFBRSxHQUFHLENBQUMsQ0FBQztRQUVSLFdBQVc7UUFDWCxJQUFJLENBQUMsWUFBWSxDQUFDO1lBQ2QsS0FBSSxDQUFDLDhCQUE4QixDQUFDLE1BQU0sRUFBRSxLQUFLLEVBQUUsS0FBSyxDQUFDLENBQUM7UUFDOUQsQ0FBQyxFQUFFLEdBQUcsQ0FBQyxDQUFDO1FBRVIsU0FBUztRQUNULElBQUksQ0FBQyxZQUFZLENBQUM7WUFDZCxLQUFJLENBQUMsc0JBQXNCLENBQUMsTUFBTSxFQUFFLEtBQUssR0FBRyxDQUFDLENBQUMsQ0FBQztRQUNuRCxDQUFDLEVBQUUsR0FBRyxDQUFDLENBQUM7SUFDWixDQUFDO0lBRUQ7Ozs7T0FJRztJQUNLLG1EQUFzQixHQUE5QixVQUErQixNQUFjLEVBQUUsVUFBa0I7UUFHN0Qsb0JBQW9CO1FBQ3BCLCtCQUErQjtJQUNuQyxDQUFDO0lBRUQ7OztPQUdHO0lBQ0ssd0RBQTJCLEdBQW5DLFVBQW9DLGFBQWtDOztRQUNsRSxXQUFXO1FBQ1gsSUFBTSxhQUFhLGVBQUcsdUJBQVUsQ0FBQyxXQUFXLEVBQUUsQ0FBQyxTQUFTLDBDQUFFLFFBQVEsMENBQUUsTUFBTSxDQUFDO1FBQzNFLElBQUksQ0FBQyxhQUFhLEVBQUU7WUFDaEIsT0FBTyxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQztZQUMzQixPQUFPO1NBQ1Y7UUFFRCxlQUFlO1FBQ2YsSUFBSSxJQUFJLENBQUMsb0JBQW9CLEVBQUU7WUFFM0IsT0FBTztTQUNWO1FBSUQsV0FBVztRQUNYLElBQU0sUUFBUSxHQUFHLGFBQWEsQ0FBQyxJQUFJLENBQUMsVUFBQSxNQUFNLElBQUksT0FBQSxNQUFNLENBQUMsTUFBTSxLQUFLLGFBQWEsRUFBL0IsQ0FBK0IsQ0FBQyxDQUFDO1FBQy9FLElBQUksQ0FBQyxRQUFRLEVBQUU7WUFFWCxPQUFPO1NBQ1Y7UUFJRCxlQUFlO1FBQ2YsSUFBSSxJQUFJLENBQUMsb0JBQW9CLEVBQUU7WUFDM0IsaUJBQWlCO1lBQ2pCLElBQU0sUUFBUSxHQUFHLENBQUMsUUFBUSxDQUFDLE1BQU0sS0FBSyxDQUFDLENBQUMsQ0FBQyxDQUFDLHNCQUFzQjtZQUVoRSxZQUFZO1lBQ1osSUFBSSxDQUFDLG9CQUFvQixDQUFDLGlCQUFpQixDQUFDLFFBQVEsQ0FBQyxDQUFDLEVBQUUsUUFBUSxDQUFDLENBQUMsRUFBRSxRQUFRLENBQUMsQ0FBQztTQUdqRjtJQUNMLENBQUM7SUFFRCxXQUFXO0lBQ1gsMkNBQWMsR0FBZCxVQUFlLENBQVMsRUFBRSxDQUFTLEVBQUUsTUFBYztRQUMvQyxJQUFJLENBQUMsSUFBSSxDQUFDLFVBQVUsRUFBRTtZQUVsQixPQUFPO1NBQ1Y7UUFFRCxlQUFlO1FBQ2YsSUFBSSxJQUFJLENBQUMsb0JBQW9CLEVBQUU7WUFFM0IsT0FBTztTQUNWO1FBRUQsSUFBTSxTQUFTLEdBQXNCO1lBQ2pDLENBQUMsRUFBRSxDQUFDO1lBQ0osQ0FBQyxFQUFFLENBQUM7WUFDSixNQUFNLEVBQUUsTUFBTSxDQUFDLHFCQUFxQjtTQUN2QyxDQUFDO1FBR0YsbUNBQWdCLENBQUMsV0FBVyxFQUFFLENBQUMsT0FBTyxDQUFDLHFCQUFTLENBQUMsaUJBQWlCLEVBQUUsU0FBUyxDQUFDLENBQUM7UUFFL0Usb0JBQW9CO1FBQ3BCLElBQUksQ0FBQyxvQkFBb0IsR0FBRyxJQUFJLENBQUM7SUFFckMsQ0FBQztJQUVELGNBQWM7SUFDZCw4Q0FBaUIsR0FBakIsVUFBa0IsQ0FBUyxFQUFFLENBQVMsRUFBRSxNQUFjO1FBQ2xELElBQUksQ0FBQyxJQUFJLENBQUMsVUFBVSxFQUFFO1lBQ2xCLE9BQU87U0FDVjtRQUVELGVBQWU7UUFDZixJQUFJLElBQUksQ0FBQyxvQkFBb0IsRUFBRTtZQUMzQixPQUFPO1NBQ1Y7UUFJRCxpQkFBaUI7UUFDakIsSUFBSSxJQUFJLENBQUMsY0FBYyxLQUFLLENBQUMsRUFBRTtZQUMzQixrQkFBa0I7WUFDbEIsSUFBTSxZQUFZLEdBQXlCO2dCQUN2QyxDQUFDLEVBQUUsQ0FBQztnQkFDSixDQUFDLEVBQUUsQ0FBQztnQkFDSixNQUFNLEVBQUUsTUFBTSxDQUFDLHFCQUFxQjthQUN2QyxDQUFDO1lBRUYsc0NBQXNDO1lBQ3RDLGlDQUFpQztZQUNqQyxtQ0FBZ0IsQ0FBQyxXQUFXLEVBQUUsQ0FBQyxPQUFPLENBQUMscUJBQVMsQ0FBQyxpQkFBaUIsRUFBRSxZQUFZLENBQUMsQ0FBQztTQUNyRjthQUFNO1lBQ0gsc0JBQXNCO1lBQ3RCLElBQU0sU0FBUyxHQUFzQjtnQkFDakMsQ0FBQyxFQUFFLENBQUM7Z0JBQ0osQ0FBQyxFQUFFLENBQUM7Z0JBQ0osTUFBTSxFQUFFLE1BQU07YUFDakIsQ0FBQztZQUVGLG1DQUFnQixDQUFDLFdBQVcsRUFBRSxDQUFDLE9BQU8sQ0FBQyxxQkFBUyxDQUFDLGlCQUFpQixFQUFFLFNBQVMsQ0FBQyxDQUFDO1NBQ2xGO1FBRUQsb0JBQW9CO1FBQ3BCLElBQUksQ0FBQyxvQkFBb0IsR0FBRyxJQUFJLENBQUM7SUFHckMsQ0FBQztJQUVELFdBQVc7SUFDWCx5Q0FBWSxHQUFaO1FBQ0ksT0FBTyxJQUFJLENBQUMsVUFBVSxJQUFJLENBQUMsSUFBSSxDQUFDLG9CQUFvQixDQUFDO0lBQ3pELENBQUM7SUFFRDs7O09BR0c7SUFDSCxxREFBd0IsR0FBeEIsVUFBeUIsSUFBNEI7O1FBR2pELHlDQUF5QztRQUN6QyxJQUFJLElBQUksQ0FBQyxtQkFBbUIsRUFBRTtZQUMxQixJQUFJLENBQUMsbUJBQW1CLENBQUMsd0JBQXdCLENBQUMsSUFBSSxDQUFDLENBQUM7U0FDM0Q7UUFFRCxrREFBa0Q7UUFDbEQsSUFBSSxhQUFhLGVBQUcsdUJBQVUsQ0FBQyxXQUFXLEVBQUUsQ0FBQyxTQUFTLDBDQUFFLFFBQVEsMENBQUUsTUFBTSxDQUFDO1FBQ3pFLElBQUksUUFBUSxHQUFHLENBQUMsSUFBSSxDQUFDLE1BQU0sS0FBSyxhQUFhLENBQUMsQ0FBQztRQUUvQyxJQUFJLFFBQVEsRUFBRTtZQUNWLG9DQUFvQztZQUNwQyxJQUFJLENBQUMscUJBQXFCLENBQUMsSUFBSSxDQUFDLE1BQU0sRUFBRSxJQUFJLENBQUMsVUFBVSxDQUFDLENBQUM7U0FFNUQ7SUFDTCxDQUFDO0lBRUQ7Ozs7T0FJRztJQUNLLGtEQUFxQixHQUE3QixVQUE4QixNQUFjLEVBQUUsVUFBa0I7UUFHNUQsK0JBQStCO1FBQy9CLElBQUksSUFBSSxDQUFDLG9CQUFvQixFQUFFO1lBQzNCLElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxtQkFBbUIsQ0FBQyxNQUFNLEVBQUUsVUFBVSxDQUFDLENBQUM7U0FFckU7YUFBTTtZQUNILE9BQU8sQ0FBQyxJQUFJLENBQUMsaURBQWlELENBQUMsQ0FBQztTQUNuRTtJQUNMLENBQUM7SUFFRCxXQUFXO0lBQ1gsOENBQWlCLEdBQWpCO1FBQ0ksT0FBTyxJQUFJLENBQUMsY0FBYyxDQUFDO0lBQy9CLENBQUM7SUFFRCxXQUFXO0lBQ1gsZ0RBQW1CLEdBQW5CO1FBQ0ksT0FBTyxJQUFJLENBQUMsZ0JBQWdCLENBQUM7SUFDakMsQ0FBQztJQUVELG1CQUFtQjtJQUNuQixrREFBcUIsR0FBckI7UUFDSSxPQUFPO1lBQ0gsV0FBVyxFQUFFLElBQUksQ0FBQyxrQkFBa0I7WUFDcEMsVUFBVSxFQUFFLElBQUksQ0FBQyxVQUFVO1lBQzNCLFdBQVcsRUFBRSxJQUFJLENBQUMsb0JBQW9CO1NBQ3pDLENBQUM7SUFDTixDQUFDO0lBRUQsUUFBUTtJQUNBLDJDQUFjLEdBQXRCLFVBQXVCLE9BQWU7UUFBdEMsaUJBcUJDO1FBcEJHLFdBQVc7UUFDWCxJQUFJLENBQUMsbUJBQW1CLEVBQUUsQ0FBQztRQUUzQixJQUFJLGdCQUFnQixHQUFHLE9BQU8sQ0FBQztRQUMvQixJQUFJLENBQUMsc0JBQXNCLENBQUMsZ0JBQWdCLENBQUMsQ0FBQztRQUU5QyxJQUFJLENBQUMsaUJBQWlCLEdBQUcsV0FBVyxDQUFDO1lBQ2pDLGdCQUFnQixFQUFFLENBQUM7WUFDbkIsS0FBSSxDQUFDLHNCQUFzQixDQUFDLGdCQUFnQixDQUFDLENBQUM7WUFFOUMsd0NBQXdDO1lBQ3hDLElBQUksS0FBSSxDQUFDLFVBQVUsS0FBSyxDQUFDLElBQUksS0FBSSxDQUFDLHVCQUF1QixFQUFFO2dCQUN2RCxLQUFJLENBQUMsMkJBQTJCLENBQUMsZ0JBQWdCLENBQUMsQ0FBQzthQUN0RDtZQUVELElBQUksZ0JBQWdCLElBQUksQ0FBQyxFQUFFO2dCQUN2QixLQUFJLENBQUMsbUJBQW1CLEVBQUUsQ0FBQzthQUU5QjtRQUNMLENBQUMsRUFBRSxJQUFJLENBQUMsQ0FBQztJQUNiLENBQUM7SUFFRCxVQUFVO0lBQ0YsbURBQXNCLEdBQTlCLFVBQStCLE9BQWU7UUFDMUMsSUFBSSxJQUFJLENBQUMsU0FBUyxFQUFFO1lBQ2hCLElBQUksQ0FBQyxTQUFTLENBQUMsTUFBTSxHQUFNLE9BQU8sTUFBRyxDQUFDLENBQUUsZ0NBQWdDO1NBQzNFO1FBQ0QsSUFBSSxDQUFDLGdCQUFnQixHQUFHLE9BQU8sQ0FBQztJQUNwQyxDQUFDO0lBRUQ7OztPQUdHO0lBQ0ssd0RBQTJCLEdBQW5DLFVBQW9DLGdCQUF3QjtRQUN4RCxJQUFJLENBQUMsSUFBSSxDQUFDLHVCQUF1QixFQUFFO1lBQy9CLE9BQU87U0FDVjtRQUVELElBQU0sSUFBSSxHQUFHLElBQUksQ0FBQyx1QkFBdUIsQ0FBQztRQUUxQyxJQUFJLGdCQUFnQixLQUFLLENBQUMsRUFBRTtZQUN4QixpQkFBaUI7WUFDakIsSUFBSSxDQUFDLHNCQUFzQixDQUFDLElBQUksQ0FBQyxhQUFhLEVBQUUsSUFBSSxDQUFDLGlCQUFpQixDQUFDLENBQUM7U0FDM0U7YUFBTSxJQUFJLGdCQUFnQixLQUFLLENBQUMsRUFBRTtZQUMvQixvQkFBb0I7WUFDcEIsSUFBSSxDQUFDLDBCQUEwQixDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsQ0FBQztZQUNwRCwwQkFBMEI7WUFDMUIsSUFBSSxDQUFDLHVCQUF1QixDQUFDLElBQUksQ0FBQyxDQUFDO1lBQ25DLGNBQWM7WUFDZCxJQUFJLENBQUMsdUJBQXVCLEdBQUcsSUFBSSxDQUFDO1NBQ3ZDO0lBQ0wsQ0FBQztJQUVEOzs7O09BSUc7SUFDSyxtREFBc0IsR0FBOUIsVUFBK0IsYUFBb0MsRUFBRSxpQkFBNkM7UUFBbEgsaUJBZ0NDOztRQS9CRyxXQUFXO1FBQ1gsSUFBTSxhQUFhLGVBQUcsdUJBQVUsQ0FBQyxXQUFXLEVBQUUsQ0FBQyxTQUFTLDBDQUFFLFFBQVEsMENBQUUsTUFBTSxDQUFDO1FBQzNFLElBQUksQ0FBQyxhQUFhLEVBQUU7WUFDaEIsT0FBTyxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQztZQUMzQixPQUFPO1NBQ1Y7UUFFRCxpQkFBaUI7UUFDakIsYUFBYSxDQUFDLE9BQU8sQ0FBQyxVQUFDLE1BQU07WUFDekIsNkJBQTZCO1lBQzdCLEtBQUksQ0FBQyx1QkFBdUIsQ0FBQyxNQUFNLENBQUMsTUFBTSxFQUFFLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUUxRCxpQkFBaUI7WUFDakIsSUFBTSxTQUFTLEdBQUcsS0FBSSxDQUFDLGFBQWEsQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUM7WUFDcEQsSUFBSSxTQUFTLEtBQUssQ0FBQyxDQUFDLEVBQUU7Z0JBQ2xCLEtBQUksQ0FBQyxxQkFBcUIsQ0FBQyxTQUFTLEVBQUUsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDO2FBQ3ZEO1FBQ0wsQ0FBQyxDQUFDLENBQUM7UUFFSCxtQkFBbUI7UUFDbkIsSUFBSSxDQUFDLFlBQVksQ0FBQztZQUNkLFlBQVk7WUFDWixhQUFhLENBQUMsT0FBTyxDQUFDLFVBQUMsTUFBTTtnQkFDekIsSUFBTSxVQUFVLEdBQUcsaUJBQWlCLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQztnQkFDekQsSUFBSSxLQUFJLENBQUMsbUJBQW1CLEVBQUU7b0JBQzFCLEtBQUksQ0FBQyxtQkFBbUIsQ0FBQyxpQkFBaUIsQ0FBQyxNQUFNLENBQUMsTUFBTSxFQUFFLFVBQVUsQ0FBQyxDQUFDO29CQUN0RSxhQUFhO29CQUNiLEtBQUksQ0FBQyxrQ0FBa0MsQ0FBQyxNQUFNLENBQUMsTUFBTSxFQUFFLFVBQVUsQ0FBQyxDQUFDO2lCQUN0RTtZQUNMLENBQUMsQ0FBQyxDQUFDO1FBQ1AsQ0FBQyxFQUFFLEdBQUcsQ0FBQyxDQUFDO0lBQ1osQ0FBQztJQUVEOzs7T0FHRztJQUNLLHVEQUEwQixHQUFsQyxVQUFtQyxhQUFvQztRQUNuRSxZQUFZO1FBQ1osSUFBSSxDQUFDLG1CQUFtQixFQUFFLENBQUM7UUFFM0IsbUJBQW1CO1FBQ25CLElBQUksQ0FBQyxjQUFjLENBQUMsYUFBYSxFQUFFO1lBQy9CLE9BQU8sQ0FBQyxHQUFHLENBQUMsV0FBVyxDQUFDLENBQUM7UUFDN0IsQ0FBQyxDQUFDLENBQUM7SUFDUCxDQUFDO0lBRUQ7O09BRUc7SUFDSyxnREFBbUIsR0FBM0I7UUFDSSxlQUFlO1FBQ2Ysc0VBQXNFO1FBRXRFLGtCQUFrQjtRQUNsQixJQUFJLENBQUMsNkJBQTZCLEVBQUUsQ0FBQztJQUN6QyxDQUFDO0lBRUQ7O09BRUc7SUFDSywwREFBNkIsR0FBckM7UUFDSSxvREFBb0Q7UUFDcEQsSUFBSSxJQUFJLENBQUMsY0FBYyxLQUFLLENBQUMsSUFBSSxJQUFJLENBQUMsb0JBQW9CLElBQUksSUFBSSxDQUFDLG9CQUFvQixDQUFDLFNBQVMsRUFBRTtZQUMvRixPQUFPO1lBQ1AsSUFBTSxRQUFRLEdBQUcsSUFBSSxDQUFDLG9CQUFvQixDQUFDLFNBQVMsQ0FBQyxRQUFRLENBQUM7WUFDOUQsS0FBSyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUUsQ0FBQyxHQUFHLFFBQVEsQ0FBQyxNQUFNLEVBQUUsQ0FBQyxFQUFFLEVBQUU7Z0JBQ3RDLElBQU0sS0FBSyxHQUFHLFFBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQztnQkFDMUIsSUFBTSxnQkFBZ0IsR0FBRyxLQUFLLENBQUMsWUFBWSxDQUFDLDhCQUFvQixDQUFDLENBQUM7Z0JBQ2xFLElBQUksZ0JBQWdCLEVBQUU7b0JBQ2xCLGdCQUFnQixDQUFDLGdCQUFnQixFQUFFLENBQUM7aUJBQ3ZDO2FBQ0o7U0FDSjthQUFNLElBQUksSUFBSSxDQUFDLGNBQWMsS0FBSyxDQUFDLElBQUksSUFBSSxDQUFDLHVCQUF1QixJQUFJLElBQUksQ0FBQyx1QkFBdUIsQ0FBQyxTQUFTLEVBQUU7WUFDNUcsUUFBUTtZQUNSLElBQU0sUUFBUSxHQUFHLElBQUksQ0FBQyx1QkFBdUIsQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDO1lBQ2pFLEtBQUssSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUMsR0FBRyxRQUFRLENBQUMsTUFBTSxFQUFFLENBQUMsRUFBRSxFQUFFO2dCQUN0QyxJQUFNLEtBQUssR0FBRyxRQUFRLENBQUMsQ0FBQyxDQUFDLENBQUM7Z0JBQzFCLElBQU0sZ0JBQWdCLEdBQUcsS0FBSyxDQUFDLFlBQVksQ0FBQyw4QkFBb0IsQ0FBQyxDQUFDO2dCQUNsRSxJQUFJLGdCQUFnQixFQUFFO29CQUNsQixnQkFBZ0IsQ0FBQyxnQkFBZ0IsRUFBRSxDQUFDO2lCQUN2QzthQUNKO1NBQ0o7SUFDTCxDQUFDO0lBRUQsVUFBVTtJQUNGLG1EQUFzQixHQUE5QixVQUErQixTQUFpQjtRQUM1QyxJQUFJLElBQUksQ0FBQyxjQUFjLEVBQUU7WUFDckIsSUFBSSxDQUFDLGNBQWMsQ0FBQyxNQUFNLEdBQUcsS0FBRyxTQUFXLENBQUM7U0FDL0M7SUFDTCxDQUFDO0lBRUQsZUFBZTtJQUNQLDZDQUFnQixHQUF4QixVQUF5QixPQUFlO1FBQ3BDLFVBQVU7UUFDVixJQUFJLENBQUMsV0FBVyxFQUFFLENBQUM7UUFFbkIsZ0JBQWdCO1FBQ2hCLElBQUksT0FBTyxLQUFLLENBQUMsRUFBRTtZQUNmLElBQUksQ0FBQyxhQUFhLEVBQUUsQ0FBQztTQUN4QjthQUFNLElBQUksT0FBTyxLQUFLLENBQUMsRUFBRTtZQUN0QixJQUFJLENBQUMsVUFBVSxFQUFFLENBQUM7U0FDckI7YUFBTTtZQUNILE9BQU8sQ0FBQyxJQUFJLENBQUMsaURBQVksT0FBTywyREFBVyxDQUFDLENBQUM7WUFDN0MsSUFBSSxDQUFDLGFBQWEsRUFBRSxDQUFDO1NBQ3hCO0lBQ0wsQ0FBQztJQUVELFNBQVM7SUFDRCwwQ0FBYSxHQUFyQjtRQUNJLElBQUksSUFBSSxDQUFDLGFBQWEsRUFBRTtZQUNwQixJQUFJLENBQUMsYUFBYSxDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUM7U0FFcEM7YUFBTTtZQUNILE9BQU8sQ0FBQyxJQUFJLENBQUMsV0FBVyxDQUFDLENBQUM7U0FDN0I7SUFDTCxDQUFDO0lBRUQsVUFBVTtJQUNGLHVDQUFVLEdBQWxCO1FBQ0ksSUFBSSxJQUFJLENBQUMsVUFBVSxFQUFFO1lBQ2pCLElBQUksQ0FBQyxVQUFVLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQztTQUVqQzthQUFNO1lBQ0gsT0FBTyxDQUFDLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQztTQUM5QjtJQUNMLENBQUM7SUFFRCxTQUFTO0lBQ0Qsd0NBQVcsR0FBbkI7UUFDSSxJQUFJLElBQUksQ0FBQyxhQUFhLEVBQUU7WUFDcEIsSUFBSSxDQUFDLGFBQWEsQ0FBQyxNQUFNLEdBQUcsS0FBSyxDQUFDO1NBQ3JDO1FBQ0QsSUFBSSxJQUFJLENBQUMsVUFBVSxFQUFFO1lBQ2pCLElBQUksQ0FBQyxVQUFVLENBQUMsTUFBTSxHQUFHLEtBQUssQ0FBQztTQUNsQztJQUNMLENBQUM7SUFFRCxXQUFXO0lBQ0gsZ0RBQW1CLEdBQTNCO1FBQ0ksSUFBSSxJQUFJLENBQUMsaUJBQWlCLEVBQUU7WUFDeEIsYUFBYSxDQUFDLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDO1lBQ3RDLElBQUksQ0FBQyxpQkFBaUIsR0FBRyxJQUFJLENBQUM7U0FDakM7SUFDTCxDQUFDO0lBRUQ7Ozs7T0FJRztJQUNLLDZFQUFnRCxHQUF4RCxVQUF5RCxhQUFvQyxFQUFFLGlCQUE2QztRQUE1SSxpQkEyQ0M7O1FBMUNHLFdBQVc7UUFDWCxJQUFNLGFBQWEsZUFBRyx1QkFBVSxDQUFDLFdBQVcsRUFBRSxDQUFDLFNBQVMsMENBQUUsUUFBUSwwQ0FBRSxNQUFNLENBQUM7UUFDM0UsSUFBSSxDQUFDLGFBQWEsRUFBRTtZQUNoQixPQUFPLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFDO1lBQzNCLE9BQU87U0FDVjtRQUVELFNBQVM7UUFDVCxJQUFNLGlCQUFpQixHQUFHLGFBQWEsQ0FBQyxJQUFJLENBQUMsVUFBQSxNQUFNLElBQUksT0FBQSxNQUFNLENBQUMsYUFBYSxFQUFwQixDQUFvQixDQUFDLENBQUM7UUFDN0UsSUFBTSx3QkFBd0IsR0FBRyxpQkFBaUIsSUFBSSxpQkFBaUIsQ0FBQyxNQUFNLEtBQUssYUFBYSxDQUFDO1FBSWpHLDBCQUEwQjtRQUMxQixJQUFJLENBQUMsd0JBQXdCLElBQUksaUJBQWlCLEVBQUU7WUFDaEQsSUFBTSxzQkFBb0IsR0FBRyxJQUFJLENBQUMsYUFBYSxDQUFDLGlCQUFpQixDQUFDLE1BQU0sQ0FBQyxDQUFDO1lBQzFFLElBQUksc0JBQW9CLEtBQUssQ0FBQyxDQUFDLEVBQUU7Z0JBRzdCLGNBQWM7Z0JBQ2QsSUFBSSxDQUFDLFlBQVksQ0FBQztvQkFDZCxLQUFJLENBQUMscUJBQXFCLENBQUMsc0JBQW9CLEVBQUUsQ0FBQyxDQUFDLENBQUM7Z0JBQ3hELENBQUMsRUFBRSxHQUFHLENBQUMsQ0FBQzthQUNYO1NBQ0o7UUFFRCxtQkFBbUI7UUFDbkIsYUFBYSxDQUFDLE9BQU8sQ0FBQyxVQUFDLE1BQU0sRUFBRSxLQUFLO1lBQ2hDLElBQU0sVUFBVSxHQUFHLGlCQUFpQixDQUFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsSUFBSSxDQUFDLENBQUM7WUFDekQsSUFBTSxhQUFhLEdBQUcsTUFBTSxDQUFDLGFBQWEsQ0FBQztZQUUzQyxhQUFhO1lBQ2IsS0FBSSxDQUFDLFlBQVksQ0FBQztnQkFDZCxJQUFJLGFBQWEsRUFBRTtvQkFDZiw0QkFBNEI7b0JBQzVCLEtBQUksQ0FBQyxtQ0FBbUMsQ0FBQyxNQUFNLEVBQUUsYUFBYSxFQUFFLFVBQVUsQ0FBQyxDQUFDO2lCQUMvRTtxQkFBTTtvQkFDSCxrQkFBa0I7b0JBQ2xCLEtBQUksQ0FBQyxzQ0FBc0MsQ0FBQyxNQUFNLEVBQUUsYUFBYSxFQUFFLFVBQVUsQ0FBQyxDQUFDO2lCQUNsRjtZQUNMLENBQUMsRUFBRSxLQUFLLEdBQUcsR0FBRyxDQUFDLENBQUM7UUFDcEIsQ0FBQyxDQUFDLENBQUM7SUFDUCxDQUFDO0lBRUQ7Ozs7O09BS0c7SUFDSyxtRUFBc0MsR0FBOUMsVUFBK0MsTUFBMkIsRUFBRSxhQUFxQixFQUFFLFVBQWtCO1FBQXJILGlCQTZCQztRQTVCRyxJQUFNLFFBQVEsR0FBRyxNQUFNLENBQUMsTUFBTSxLQUFLLGFBQWEsQ0FBQztRQUVqRCw2QkFBNkI7UUFDN0IsSUFBSSxJQUFJLENBQUMsbUJBQW1CLEVBQUU7WUFDMUIsU0FBUztZQUNULElBQU0sU0FBUyxHQUFHLElBQUksQ0FBQyxhQUFhLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDO1lBQ3BELElBQUksU0FBUyxLQUFLLENBQUMsQ0FBQyxFQUFFO2dCQUNsQixlQUFlO2dCQUNmLElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxTQUFTLEVBQUUsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDO2FBQ3ZEO1NBQ0o7UUFFRCxvQ0FBb0M7UUFDcEMsSUFBSSxDQUFDLFlBQVksQ0FBQztZQUNkLElBQUksS0FBSSxDQUFDLG1CQUFtQixFQUFFO2dCQUUxQixLQUFJLENBQUMsbUJBQW1CLENBQUMsaUJBQWlCLENBQUMsTUFBTSxDQUFDLE1BQU0sRUFBRSxVQUFVLENBQUMsQ0FBQztnQkFFdEUsYUFBYTtnQkFDYixLQUFJLENBQUMsa0NBQWtDLENBQUMsTUFBTSxDQUFDLE1BQU0sRUFBRSxVQUFVLENBQUMsQ0FBQzthQUN0RTtRQUNMLENBQUMsRUFBRSxHQUFHLENBQUMsQ0FBQztRQUVSLDJCQUEyQjtRQUMzQixJQUFJLENBQUMsWUFBWSxDQUFDO1lBQ2QsS0FBSSxDQUFDLHVCQUF1QixDQUFDLE1BQU0sQ0FBQyxNQUFNLEVBQUUsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDO1FBRTlELENBQUMsRUFBRSxHQUFHLENBQUMsQ0FBQztJQUNaLENBQUM7SUFFRDs7OztPQUlHO0lBQ0ssK0RBQWtDLEdBQTFDLFVBQTJDLE1BQWMsRUFBRSxVQUFrQjtRQUN6RSxJQUFJLENBQUMsdUJBQVUsQ0FBQyxXQUFXLEVBQUUsQ0FBQyxlQUFlLElBQUksQ0FBQyx1QkFBVSxDQUFDLFdBQVcsRUFBRSxDQUFDLGVBQWUsQ0FBQyxLQUFLLEVBQUU7WUFDOUYsT0FBTyxDQUFDLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxDQUFDO1lBQ2hDLE9BQU87U0FDVjtRQUVELElBQUksS0FBSyxHQUFlLHVCQUFVLENBQUMsV0FBVyxFQUFFLENBQUMsZUFBZSxDQUFDLEtBQUssQ0FBQztRQUN2RSxJQUFNLFNBQVMsR0FBRyxLQUFLLENBQUMsU0FBUyxDQUFDLFVBQUEsSUFBSSxJQUFJLE9BQUEsSUFBSSxDQUFDLE1BQU0sS0FBSyxNQUFNLEVBQXRCLENBQXNCLENBQUMsQ0FBQztRQUVsRSxJQUFJLFNBQVMsS0FBSyxDQUFDLENBQUMsRUFBRTtZQUNsQixLQUFLLENBQUMsU0FBUyxDQUFDLENBQUMsS0FBSyxHQUFHLFVBQVUsQ0FBQztTQUV2QzthQUFNO1lBQ0gsT0FBTyxDQUFDLElBQUksQ0FBQyw0Q0FBaUIsTUFBUSxDQUFDLENBQUM7U0FDM0M7SUFDTCxDQUFDO0lBRUQ7Ozs7T0FJRztJQUNLLDBDQUFhLEdBQXJCLFVBQXNCLE1BQWM7UUFDaEMsSUFBSSxDQUFDLHVCQUFVLENBQUMsV0FBVyxFQUFFLENBQUMsZUFBZSxJQUFJLENBQUMsdUJBQVUsQ0FBQyxXQUFXLEVBQUUsQ0FBQyxlQUFlLENBQUMsS0FBSyxFQUFFO1lBQzlGLE9BQU8sQ0FBQyxJQUFJLENBQUMsaUJBQWlCLENBQUMsQ0FBQztZQUNoQyxPQUFPLENBQUMsQ0FBQyxDQUFDO1NBQ2I7UUFFRCxJQUFJLEtBQUssR0FBZSx1QkFBVSxDQUFDLFdBQVcsRUFBRSxDQUFDLGVBQWUsQ0FBQyxLQUFLLENBQUM7UUFDdkUsT0FBTyxLQUFLLENBQUMsU0FBUyxDQUFDLFVBQUEsSUFBSSxJQUFJLE9BQUEsSUFBSSxDQUFDLE1BQU0sS0FBSyxNQUFNLEVBQXRCLENBQXNCLENBQUMsQ0FBQztJQUMzRCxDQUFDO0lBRUQ7Ozs7T0FJRztJQUNLLG9EQUF1QixHQUEvQixVQUFnQyxNQUFjLEVBQUUsS0FBYTtRQUN6RCxpQkFBaUI7UUFDakIsSUFBSSxJQUFJLENBQUMsY0FBYyxLQUFLLENBQUMsSUFBSSxJQUFJLENBQUMsb0JBQW9CLEVBQUU7WUFDeEQsT0FBTztZQUNQLElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxtQkFBbUIsQ0FBQyxNQUFNLEVBQUUsS0FBSyxDQUFDLENBQUM7U0FDaEU7YUFBTSxJQUFJLElBQUksQ0FBQyxjQUFjLEtBQUssQ0FBQyxJQUFJLElBQUksQ0FBQyx1QkFBdUIsRUFBRTtZQUNsRSxRQUFRO1lBQ1IsSUFBSSxDQUFDLHVCQUF1QixDQUFDLHNCQUFzQixDQUFDLE1BQU0sRUFBRSxLQUFLLENBQUMsQ0FBQztTQUN0RTthQUFNO1lBQ0gsT0FBTyxDQUFDLElBQUksQ0FBQyxxQkFBcUIsQ0FBQyxDQUFDO1NBQ3ZDO0lBQ0wsQ0FBQztJQUVEOzs7O09BSUc7SUFDSyxrREFBcUIsR0FBN0IsVUFBOEIsU0FBaUIsRUFBRSxLQUFhO1FBQzFELElBQUksQ0FBQyxJQUFJLENBQUMsbUJBQW1CLEVBQUU7WUFDM0IsT0FBTyxDQUFDLElBQUksQ0FBQyxxQ0FBcUMsQ0FBQyxDQUFDO1lBQ3BELE9BQU87U0FDVjtRQUVELDZCQUE2QjtRQUM3QixJQUFNLHFCQUFxQixHQUFHLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyx3QkFBd0IsQ0FBQyxTQUFTLENBQUMsQ0FBQztRQUMzRixJQUFJLHFCQUFxQixFQUFFO1lBQ3ZCLFVBQVU7WUFDVixJQUFJLEtBQUssR0FBRyxDQUFDLEVBQUU7Z0JBQ1gscUJBQXFCLENBQUMsWUFBWSxDQUFDLEtBQUssQ0FBQyxDQUFDO2FBQzdDO2lCQUFNLElBQUksS0FBSyxHQUFHLENBQUMsRUFBRTtnQkFDbEIscUJBQXFCLENBQUMsWUFBWSxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQzthQUN2RDtTQUNKO2FBQU07WUFDSCxPQUFPLENBQUMsSUFBSSxDQUFDLGdEQUFXLFNBQVMsNkNBQTJCLENBQUMsQ0FBQztTQUNqRTtJQUNMLENBQUM7SUFFRDs7Ozs7T0FLRztJQUNLLGdFQUFtQyxHQUEzQyxVQUE0QyxNQUEyQixFQUFFLGFBQXFCLEVBQUUsVUFBa0I7UUFBbEgsaUJBeUJDO1FBeEJHLElBQU0sU0FBUyxHQUFHLElBQUksQ0FBQyxhQUFhLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDO1FBRXBELG1DQUFtQztRQUNuQyxJQUFJLENBQUMsWUFBWSxDQUFDO1lBR2QsMkJBQTJCO1lBQzNCLElBQUksU0FBUyxLQUFLLENBQUMsQ0FBQyxFQUFFO2dCQUNsQixLQUFJLENBQUMscUJBQXFCLENBQUMsU0FBUyxFQUFFLE1BQU0sQ0FBQyxLQUFLLENBQUMsQ0FBQzthQUN2RDtRQUNMLENBQUMsRUFBRSxHQUFHLENBQUMsQ0FBQztRQUVSLGlCQUFpQjtRQUNqQixJQUFJLENBQUMsWUFBWSxDQUFDO1lBQ2QsSUFBSSxLQUFJLENBQUMsbUJBQW1CLEVBQUU7Z0JBQzFCLEtBQUksQ0FBQyxtQkFBbUIsQ0FBQyxpQkFBaUIsQ0FBQyxNQUFNLENBQUMsTUFBTSxFQUFFLFVBQVUsQ0FBQyxDQUFDO2dCQUN0RSxLQUFJLENBQUMsa0NBQWtDLENBQUMsTUFBTSxDQUFDLE1BQU0sRUFBRSxVQUFVLENBQUMsQ0FBQzthQUN0RTtRQUNMLENBQUMsRUFBRSxHQUFHLENBQUMsQ0FBQztRQUVSLDJDQUEyQztRQUMzQyxJQUFJLENBQUMsWUFBWSxDQUFDO1lBQ2QsS0FBSSxDQUFDLHVCQUF1QixDQUFDLE1BQU0sQ0FBQyxNQUFNLEVBQUUsTUFBTSxDQUFDLEtBQUssQ0FBQyxDQUFDO1FBQzlELENBQUMsRUFBRSxHQUFHLENBQUMsQ0FBQztJQUNaLENBQUM7SUFFRCxzQ0FBUyxHQUFUO1FBQ0ksYUFBYTtRQUNiLElBQUksSUFBSSxDQUFDLG9CQUFvQixFQUFFO1lBQzNCLElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLG1CQUFtQixFQUFFLElBQUksQ0FBQyxpQkFBaUIsRUFBRSxJQUFJLENBQUMsQ0FBQztTQUN6RjtJQUNMLENBQUM7SUE1NkNEO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUM7NERBQ1U7SUFHNUI7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLEtBQUssQ0FBQzt5REFDTztJQUcxQjtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsS0FBSyxDQUFDOzhEQUNZO0lBRy9CO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUM7NkRBQ1c7SUFHN0I7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQzswREFDUTtJQUcxQjtRQURDLFFBQVEsQ0FBQywrQkFBcUIsQ0FBQztxRUFDbUI7SUFHbkQ7UUFEQyxRQUFRLENBQUMsa0NBQXdCLENBQUM7d0VBQ3NCO0lBR3pEO1FBREMsUUFBUSxDQUFDLDZCQUFtQixDQUFDO21FQUNpQjtJQUcvQztRQURDLFFBQVEsQ0FBQyw4QkFBb0IsQ0FBQztvRUFDa0I7SUFHakQ7UUFEQyxRQUFRLENBQUMsaUNBQXVCLENBQUM7dUVBQ3FCO0lBOUJ0QyxrQkFBa0I7UUFEdEMsT0FBTztPQUNhLGtCQUFrQixDQWs3Q3RDO0lBQUQseUJBQUM7Q0FsN0NELEFBazdDQyxDQWw3QytDLEVBQUUsQ0FBQyxTQUFTLEdBazdDM0Q7a0JBbDdDb0Isa0JBQWtCIiwiZmlsZSI6IiIsInNvdXJjZVJvb3QiOiIvIiwic291cmNlc0NvbnRlbnQiOlsiLy8gTGVhcm4gVHlwZVNjcmlwdDpcbi8vICAtIGh0dHBzOi8vZG9jcy5jb2Nvcy5jb20vY3JlYXRvci8yLjQvbWFudWFsL2VuL3NjcmlwdGluZy90eXBlc2NyaXB0Lmh0bWxcbi8vIExlYXJuIEF0dHJpYnV0ZTpcbi8vICAtIGh0dHBzOi8vZG9jcy5jb2Nvcy5jb20vY3JlYXRvci8yLjQvbWFudWFsL2VuL3NjcmlwdGluZy9yZWZlcmVuY2UvYXR0cmlidXRlcy5odG1sXG4vLyBMZWFybiBsaWZlLWN5Y2xlIGNhbGxiYWNrczpcbi8vICAtIGh0dHBzOi8vZG9jcy5jb2Nvcy5jb20vY3JlYXRvci8yLjQvbWFudWFsL2VuL3NjcmlwdGluZy9saWZlLWN5Y2xlLWNhbGxiYWNrcy5odG1sXG5cbmltcG9ydCB7IE5vdGljZVNldHRsZW1lbnQsIE5vdGljZVJvdW5kU3RhcnQsIE5vdGljZUFjdGlvbkRpc3BsYXksIFBsYXllckFjdGlvbkRpc3BsYXksIE5vdGljZVJvdW5kRW5kLCBQbGF5ZXJSb3VuZFJlc3VsdCwgQ2xpY2tCbG9ja1JlcXVlc3QsIENsaWNrSGV4QmxvY2tSZXF1ZXN0LCBOb3RpY2VTdGFydEdhbWUsIE5vdGljZUZpcnN0Q2hvaWNlQm9udXMsIEZsb29kRmlsbFJlc3VsdCwgUm9vbVVzZXIsIEhleENvb3JkIH0gZnJvbSBcIi4uL2JlYW4vR2FtZUJlYW5cIjtcbmltcG9ydCB7IEdsb2JhbEJlYW4gfSBmcm9tIFwiLi4vYmVhbi9HbG9iYWxCZWFuXCI7XG5pbXBvcnQgTGVhdmVEaWFsb2dDb250cm9sbGVyIGZyb20gXCIuLi9oYWxsL0xlYXZlRGlhbG9nQ29udHJvbGxlclwiO1xuaW1wb3J0IHsgQXVkaW9NYW5hZ2VyIH0gZnJvbSBcIi4uL3V0aWwvQXVkaW9NYW5hZ2VyXCI7XG5pbXBvcnQgeyBDb25maWcgfSBmcm9tIFwiLi4vdXRpbC9Db25maWdcIjtcbmltcG9ydCB7IFRvb2xzIH0gZnJvbSBcIi4uL3V0aWwvVG9vbHNcIjtcbmltcG9ydCBDb25ncmF0c0RpYWxvZ0NvbnRyb2xsZXIgZnJvbSBcIi4vQ29uZ3JhdHNEaWFsb2dDb250cm9sbGVyXCI7XG5pbXBvcnQgR2FtZVNjb3JlQ29udHJvbGxlciBmcm9tIFwiLi9HYW1lU2NvcmVDb250cm9sbGVyXCI7XG5pbXBvcnQgQ2hlc3NCb2FyZENvbnRyb2xsZXIgZnJvbSBcIi4vQ2hlc3MvQ2hlc3NCb2FyZENvbnRyb2xsZXJcIjtcbmltcG9ydCBIZXhDaGVzc0JvYXJkQ29udHJvbGxlciBmcm9tIFwiLi9DaGVzcy9IZXhDaGVzc0JvYXJkQ29udHJvbGxlclwiO1xuaW1wb3J0IFBsYXllckdhbWVDb250cm9sbGVyIGZyb20gXCIuLi9wZmIvUGxheWVyR2FtZUNvbnRyb2xsZXIgXCI7XG5pbXBvcnQgeyBXZWJTb2NrZXRNYW5hZ2VyIH0gZnJvbSBcIi4uL25ldC9XZWJTb2NrZXRNYW5hZ2VyXCI7XG5pbXBvcnQgeyBNZXNzYWdlSWQgfSBmcm9tIFwiLi4vbmV0L01lc3NhZ2VJZFwiO1xuXG5jb25zdCB7IGNjY2xhc3MsIHByb3BlcnR5IH0gPSBjYy5fZGVjb3JhdG9yO1xuXG5AY2NjbGFzc1xuZXhwb3J0IGRlZmF1bHQgY2xhc3MgR2FtZVBhZ2VDb250cm9sbGVyIGV4dGVuZHMgY2MuQ29tcG9uZW50IHtcblxuICAgIEBwcm9wZXJ0eShjYy5Ob2RlKVxuICAgIGJvYXJkQnRuQmFjazogY2MuTm9kZSA9IG51bGwgLy/ov5Tlm57mjInpkq5cblxuICAgIEBwcm9wZXJ0eShjYy5MYWJlbClcbiAgICB0aW1lTGFiZWw6IGNjLkxhYmVsID0gbnVsbCAvLyDorqHml7blmajmmL7npLrmoIfnrb5cblxuICAgIEBwcm9wZXJ0eShjYy5MYWJlbClcbiAgICBtaW5lQ291bnRMYWJlbDogY2MuTGFiZWwgPSBudWxsIC8vIOeCuOW8ueaVsOmHj+aYvuekuuagh+etvlxuXG4gICAgQHByb3BlcnR5KGNjLk5vZGUpXG4gICAgc3F1YXJlTWFwTm9kZTogY2MuTm9kZSA9IG51bGwgLy8g5pa55b2i5Zyw5Zu+6IqC54K5IChtYXBUeXBlID0gMClcblxuICAgIEBwcm9wZXJ0eShjYy5Ob2RlKVxuICAgIGhleE1hcE5vZGU6IGNjLk5vZGUgPSBudWxsIC8vIOWFrei+ueW9ouWcsOWbvuiKgueCuSAobWFwVHlwZSA9IDEpXG5cbiAgICBAcHJvcGVydHkoTGVhdmVEaWFsb2dDb250cm9sbGVyKVxuICAgIGxlYXZlRGlhbG9nQ29udHJvbGxlcjogTGVhdmVEaWFsb2dDb250cm9sbGVyID0gbnVsbCAvLyDpgIDlh7rmuLjmiI/lvLnnqpdcblxuICAgIEBwcm9wZXJ0eShDb25ncmF0c0RpYWxvZ0NvbnRyb2xsZXIpXG4gICAgY29uZ3JhdHNEaWFsb2dDb250cm9sbGVyOiBDb25ncmF0c0RpYWxvZ0NvbnRyb2xsZXIgPSBudWxsIC8v57uT566X5by556qXXG5cbiAgICBAcHJvcGVydHkoR2FtZVNjb3JlQ29udHJvbGxlcilcbiAgICBnYW1lU2NvcmVDb250cm9sbGVyOiBHYW1lU2NvcmVDb250cm9sbGVyID0gbnVsbCAvL+WIhuaVsOaOp+WItuWZqFxuXG4gICAgQHByb3BlcnR5KENoZXNzQm9hcmRDb250cm9sbGVyKVxuICAgIGNoZXNzQm9hcmRDb250cm9sbGVyOiBDaGVzc0JvYXJkQ29udHJvbGxlciA9IG51bGwgLy/mlrnlvaLmo4vnm5jmjqfliLblmahcblxuICAgIEBwcm9wZXJ0eShIZXhDaGVzc0JvYXJkQ29udHJvbGxlcilcbiAgICBoZXhDaGVzc0JvYXJkQ29udHJvbGxlcjogSGV4Q2hlc3NCb2FyZENvbnRyb2xsZXIgPSBudWxsIC8v5YWt6L655b2i5qOL55uY5o6n5Yi25ZmoXG5cbiAgICBpc0xlYXZlR2FtZURpYWxvZ1Nob3c6IGJvb2xlYW4gPSBmYWxzZTsgIC8v5piv5ZCm5pi+56S66YCA5Ye65ri45oiP55qE5by556qXXG4gICAgaXNDb25ncmF0c0RpYWxvZzogYm9vbGVhbiA9IGZhbHNlOyAgLy/mmK/lkKbmmL7npLrnu5PnrpfnmoTlvLnnqpdcblxuICAgIC8vIOiuoeaXtuWZqOebuOWFs+WxnuaAp1xuICAgIHByaXZhdGUgY291bnRkb3duSW50ZXJ2YWw6IG51bWJlciA9IG51bGw7IC8vIOWAkuiuoeaXtuWumuaXtuWZqElEXG4gICAgcHJpdmF0ZSBjdXJyZW50Q291bnRkb3duOiBudW1iZXIgPSAwOyAvLyDlvZPliY3lgJLorqHml7bnp5LmlbBcbiAgICBwcml2YXRlIGN1cnJlbnRSb3VuZE51bWJlcjogbnVtYmVyID0gMDsgLy8g5b2T5YmN5Zue5ZCI57yW5Y+3XG5cbiAgICAvLyDmuLjmiI/nirbmgIHnrqHnkIZcbiAgICBwcml2YXRlIGNhbk9wZXJhdGU6IGJvb2xlYW4gPSBmYWxzZTsgLy8g5piv5ZCm5Y+v5Lul5pON5L2c77yI5ZyoTm90aWNlUm91bmRTdGFydOWSjE5vdGljZUFjdGlvbkRpc3BsYXnkuYvpl7TvvIlcbiAgICBwcml2YXRlIGdhbWVTdGF0dXM6IG51bWJlciA9IDA7IC8vIOa4uOaIj+eKtuaAgVxuICAgIHByaXZhdGUgaGFzT3BlcmF0ZWRUaGlzUm91bmQ6IGJvb2xlYW4gPSBmYWxzZTsgLy8g5pys5Zue5ZCI5piv5ZCm5bey57uP5pON5L2c6L+HXG5cbiAgICAvLyDmuLjmiI/mlbDmja5cbiAgICBwcml2YXRlIGN1cnJlbnRNYXBUeXBlOiBudW1iZXIgPSAwOyAvLyDlvZPliY3lnLDlm77nsbvlnosgMC3mlrnlvaLlnLDlm77vvIwxLeWFrei+ueW9ouWcsOWbvlxuICAgIHByaXZhdGUgY3VycmVudE1pbmVDb3VudDogbnVtYmVyID0gMDsgLy8g5b2T5YmN54K45by55pWw6YePXG5cbiAgICAvLyDlvZPliY1Ob3RpY2VBY3Rpb25EaXNwbGF55pWw5o2u77yM55So5LqO5YCS6K6h5pe25pi+56S66YC76L6RXG4gICAgcHJpdmF0ZSBjdXJyZW50Tm90aWNlQWN0aW9uRGF0YTogTm90aWNlQWN0aW9uRGlzcGxheSA9IG51bGw7XG5cblxuICAgIG9uTG9hZCgpIHtcbiAgICAgICAgLy8g5aaC5p6cdGltZUxhYmVs5rKh5pyJ5Zyo57yW6L6R5Zmo5Lit6K6+572u77yM5bCd6K+V6YCa6L+H6Lev5b6E5p+l5om+XG4gICAgICAgIGlmICghdGhpcy50aW1lTGFiZWwpIHtcbiAgICAgICAgICAgIC8vIOagueaNruWcuuaZr+e7k+aehOafpeaJvnRpbWVfbGFiZWzoioLngrlcbiAgICAgICAgICAgIGNvbnN0IHRpbWVCZ05vZGUgPSBjYy5maW5kKCdDYW52YXMvdGltZV9iZycpO1xuICAgICAgICAgICAgaWYgKHRpbWVCZ05vZGUpIHtcbiAgICAgICAgICAgICAgICBjb25zdCB0aW1lTGFiZWxOb2RlID0gdGltZUJnTm9kZS5nZXRDaGlsZEJ5TmFtZSgndGltZV9sYWJlbCcpO1xuICAgICAgICAgICAgICAgIGlmICh0aW1lTGFiZWxOb2RlKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMudGltZUxhYmVsID0gdGltZUxhYmVsTm9kZS5nZXRDb21wb25lbnQoY2MuTGFiZWwpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOWmguaenG1pbmVDb3VudExhYmVs5rKh5pyJ5Zyo57yW6L6R5Zmo5Lit6K6+572u77yM5bCd6K+V6YCa6L+H6Lev5b6E5p+l5om+XG4gICAgICAgIGlmICghdGhpcy5taW5lQ291bnRMYWJlbCkge1xuICAgICAgICAgICAgLy8g5qC55o2u5Zy65pmv57uT5p6E5p+l5om+bWluZV9jb3VudF9sYWJlbOiKgueCuVxuICAgICAgICAgICAgY29uc3QgbWluZUNvdW50QmdOb2RlID0gY2MuZmluZCgnQ2FudmFzL21pbmVfY291bnRfYmcnKTtcbiAgICAgICAgICAgIGlmIChtaW5lQ291bnRCZ05vZGUpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBtaW5lQ291bnRMYWJlbE5vZGUgPSBtaW5lQ291bnRCZ05vZGUuZ2V0Q2hpbGRCeU5hbWUoJ21pbmVfY291bnRfbGFiZWwnKTtcbiAgICAgICAgICAgICAgICBpZiAobWluZUNvdW50TGFiZWxOb2RlKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMubWluZUNvdW50TGFiZWwgPSBtaW5lQ291bnRMYWJlbE5vZGUuZ2V0Q29tcG9uZW50KGNjLkxhYmVsKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICAvLyDlsIbmtYvor5Xmlrnms5XmmrTpnLLliLDlhajlsYDvvIzmlrnkvr/osIPor5VcbiAgICAgICAgKHdpbmRvdyBhcyBhbnkpLnRlc3RHYW1lUmVzZXQgPSAoKSA9PiB7XG4gICAgICAgICAgICB0aGlzLnRlc3RSZXNldCgpO1xuICAgICAgICB9O1xuXG4gICAgICAgIC8vIOaatOmcsiBHYW1lUGFnZUNvbnRyb2xsZXIg5a6e5L6L5Yiw5YWo5bGAXG4gICAgICAgICh3aW5kb3cgYXMgYW55KS5nYW1lUGFnZUNvbnRyb2xsZXIgPSB0aGlzO1xuXG4gICAgfVxuXG5cbiAgICBwcm90ZWN0ZWQgc3RhcnQoKTogdm9pZCB7XG4gICAgICAgVG9vbHMuaW1hZ2VCdXR0b25DbGljayh0aGlzLmJvYXJkQnRuQmFjaywgQ29uZmlnLmJ1dHRvblJlcyArICdzaWRlX2J0bl9iYWNrX25vcm1hbCcsIENvbmZpZy5idXR0b25SZXMgKyAnc2lkZV9idG5fYmFja19wcmVzc2VkJywgKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgdGhpcy5pc0xlYXZlR2FtZURpYWxvZ1Nob3cgPSB0cnVlXG4gICAgICAgICAgICAgICAgICB0aGlzLmxlYXZlRGlhbG9nQ29udHJvbGxlci5zaG93KDEsKCk9PntcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5pc0xlYXZlR2FtZURpYWxvZ1Nob3cgPSBmYWxzZVxuICAgICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgIH0pO1xuXG4gICAgICAgIC8vIOebkeWQrOaji+ebmOeCueWHu+S6i+S7tlxuICAgICAgICBpZiAodGhpcy5jaGVzc0JvYXJkQ29udHJvbGxlcikge1xuICAgICAgICAgICAgdGhpcy5jaGVzc0JvYXJkQ29udHJvbGxlci5ub2RlLm9uKCdjaGVzcy1ib2FyZC1jbGljaycsIHRoaXMub25DaGVzc0JvYXJkQ2xpY2ssIHRoaXMpO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g55uR5ZCs5YWt6L655b2i5qOL55uY54K55Ye75LqL5Lu2XG4gICAgICAgIGlmICh0aGlzLmhleENoZXNzQm9hcmRDb250cm9sbGVyKSB7XG4gICAgICAgICAgICB0aGlzLmhleENoZXNzQm9hcmRDb250cm9sbGVyLm5vZGUub24oJ2hleC1jaGVzcy1ib2FyZC1jbGljaycsIHRoaXMub25IZXhDaGVzc0JvYXJkQ2xpY2ssIHRoaXMpO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5aSE55CG5qOL55uY54K55Ye75LqL5Lu2XG4gICAgICogQHBhcmFtIGV2ZW50IOS6i+S7tuaVsOaNriB7eDogbnVtYmVyLCB5OiBudW1iZXIsIGFjdGlvbjogbnVtYmVyfVxuICAgICAqL1xuICAgIHByaXZhdGUgb25DaGVzc0JvYXJkQ2xpY2soZXZlbnQ6IGFueSkge1xuICAgICAgICBjb25zdCB7IHgsIHksIGFjdGlvbiB9ID0gZXZlbnQuZGV0YWlsIHx8IGV2ZW50O1xuXG4gICAgICAgIC8vIOajgOafpeaYr+WQpuWPr+S7peaTjeS9nO+8iOWcqOaTjeS9nOaXtumXtOWGhe+8iVxuICAgICAgICBpZiAoIXRoaXMuaXNDYW5PcGVyYXRlKCkpIHtcblxuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g5qOA5p+l5pys5Zue5ZCI5piv5ZCm5bey57uP5pON5L2c6L+HXG4gICAgICAgIGlmICh0aGlzLmhhc09wZXJhdGVkVGhpc1JvdW5kKSB7XG5cbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOWPkemAgeeCueWHu+aTjeS9nFxuICAgICAgICB0aGlzLnNlbmRDbGlja0Jsb2NrKHgsIHksIGFjdGlvbik7XG5cbiAgICAgICAgLy8g5pON5L2c5pyJ5pWI77yM6YCa55+l5qOL55uY55Sf5oiQ6aKE5Yi25L2TXG4gICAgICAgIGlmICh0aGlzLmNoZXNzQm9hcmRDb250cm9sbGVyKSB7XG4gICAgICAgICAgICBpZiAoYWN0aW9uID09PSAxKSB7XG4gICAgICAgICAgICAgICAgLy8g5oyW5o6Y5pON5L2c77yM55Sf5oiQ5LiN5bim5peX5a2Q55qE6aKE5Yi25L2TXG4gICAgICAgICAgICAgICAgdGhpcy5jaGVzc0JvYXJkQ29udHJvbGxlci5wbGFjZVBsYXllck9uR3JpZCh4LCB5LCBmYWxzZSk7XG4gICAgICAgICAgICB9IGVsc2UgaWYgKGFjdGlvbiA9PT0gMikge1xuICAgICAgICAgICAgICAgIC8vIOagh+iusOaTjeS9nO+8jOeUn+aIkOW4puaXl+WtkOeahOmihOWItuS9k1xuICAgICAgICAgICAgICAgIHRoaXMuY2hlc3NCb2FyZENvbnRyb2xsZXIucGxhY2VQbGF5ZXJPbkdyaWQoeCwgeSwgdHJ1ZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICAvLyDmoIforrDmnKzlm57lkIjlt7Lnu4/mk43kvZzov4fvvIznpoHmraLlkI7nu63kuqTkupJcbiAgICAgICAgdGhpcy5oYXNPcGVyYXRlZFRoaXNSb3VuZCA9IHRydWU7XG5cbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDlpITnkIblha3ovrnlvaLmo4vnm5jngrnlh7vkuovku7ZcbiAgICAgKiBAcGFyYW0gZXZlbnQg5LqL5Lu25pWw5o2uIHtxOiBudW1iZXIsIHI6IG51bWJlciwgYWN0aW9uOiBudW1iZXJ9XG4gICAgICovXG4gICAgcHJpdmF0ZSBvbkhleENoZXNzQm9hcmRDbGljayhldmVudDogYW55KSB7XG4gICAgICAgIGNvbnN0IHsgcSwgciwgYWN0aW9uIH0gPSBldmVudC5kZXRhaWwgfHwgZXZlbnQ7XG5cbiAgICAgICAgLy8g5qOA5p+l5piv5ZCm5Y+v5Lul5pON5L2c77yI5Zyo5pON5L2c5pe26Ze05YaF77yJXG4gICAgICAgIGlmICghdGhpcy5pc0Nhbk9wZXJhdGUoKSkge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g5qOA5p+l5pys5Zue5ZCI5piv5ZCm5bey57uP5pON5L2c6L+HXG4gICAgICAgIGlmICh0aGlzLmhhc09wZXJhdGVkVGhpc1JvdW5kKSB7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cblxuICAgICAgICAvLyDlj5HpgIHlha3ovrnlvaLngrnlh7vmk43kvZzvvIjpnIDopoHlsIblha3ovrnlvaLlnZDmoIfovazmjaLkuLrmnI3liqHlmajmnJ/mnJvnmoTmoLzlvI/vvIlcbiAgICAgICAgdGhpcy5zZW5kSGV4Q2xpY2tCbG9jayhxLCByLCBhY3Rpb24pO1xuXG4gICAgICAgIC8vIOaTjeS9nOacieaViO+8jOmAmuefpeWFrei+ueW9ouaji+ebmOeUn+aIkOmihOWItuS9k1xuICAgICAgICBpZiAodGhpcy5oZXhDaGVzc0JvYXJkQ29udHJvbGxlcikge1xuICAgICAgICAgICAgaWYgKGFjdGlvbiA9PT0gMSkge1xuICAgICAgICAgICAgICAgIC8vIOaMluaOmOaTjeS9nO+8jOeUn+aIkOS4jeW4puaXl+WtkOeahOmihOWItuS9k1xuICAgICAgICAgICAgICAgIHRoaXMuaGV4Q2hlc3NCb2FyZENvbnRyb2xsZXIucGxhY2VQbGF5ZXJPbkhleEdyaWQocSwgciwgZmFsc2UpO1xuICAgICAgICAgICAgfSBlbHNlIGlmIChhY3Rpb24gPT09IDIpIHtcbiAgICAgICAgICAgICAgICAvLyDmoIforrDmk43kvZzvvIznlJ/miJDluKbml5flrZDnmoTpooTliLbkvZNcbiAgICAgICAgICAgICAgICB0aGlzLmhleENoZXNzQm9hcmRDb250cm9sbGVyLnBsYWNlUGxheWVyT25IZXhHcmlkKHEsIHIsIHRydWUpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgLy8g5qCH6K6w5pys5Zue5ZCI5bey57uP5pON5L2c6L+H77yM56aB5q2i5ZCO57ut5Lqk5LqSXG4gICAgICAgIHRoaXMuaGFzT3BlcmF0ZWRUaGlzUm91bmQgPSB0cnVlO1xuICAgIH1cblxuXG4gICAgLy/nu5PnrpdcbiAgICBzZXRDb25ncmF0c0RpYWxvZyhub3RpY2VTZXR0bGVtZW50OiBOb3RpY2VTZXR0bGVtZW50KSB7XG5cbiAgICAgICAgdGhpcy5zZXRDb25ncmF0cyhub3RpY2VTZXR0bGVtZW50KVxuXG4gICAgICAgIC8v6YCA5Ye65by556qX5q2j5Zyo5pi+56S655qE6K+dICDlsLHlhYjlhbPpl61cbiAgICAgICAgaWYgKHRoaXMuaXNMZWF2ZUdhbWVEaWFsb2dTaG93KSB7XG4gICAgICAgICAgICB0aGlzLmxlYXZlRGlhbG9nQ29udHJvbGxlci5oaWRlKClcbiAgICAgICAgfVxuXG4gICAgICAgIHRoaXMuaXNDb25ncmF0c0RpYWxvZyA9IHRydWVcbiAgICAgICAgLy/lvLnlh7rnu5PnrpflvLnnqpdcbiAgICAgICAgdGhpcy5jb25ncmF0c0RpYWxvZ0NvbnRyb2xsZXIuc2hvdyhub3RpY2VTZXR0bGVtZW50LCAoKSA9PiB7XG4gICAgICAgICAgICB0aGlzLmlzQ29uZ3JhdHNEaWFsb2cgPSBmYWxzZVxuICAgICAgICB9KVxuXG4gICAgfVxuXG4gICAgcHJvdGVjdGVkIG9uRGlzYWJsZSgpOiB2b2lkIHtcbiAgICAgICAgLy/pgIDlh7rlvLnnqpfmraPlnKjmmL7npLrnmoTor50gIOWwseWFiOWFs+mXrVxuICAgICAgICBpZiAodGhpcy5pc0xlYXZlR2FtZURpYWxvZ1Nob3cpIHtcbiAgICAgICAgICAgIHRoaXMubGVhdmVEaWFsb2dDb250cm9sbGVyLmhpZGUoKVxuICAgICAgICB9XG5cbiAgICAgICAgLy/nu5PnrpflvLnnqpfmraPlnKjmmL7npLrnmoTor53lsLHlhYjlhbPpl63mjolcbiAgICAgICAgaWYgKHRoaXMuaXNDb25ncmF0c0RpYWxvZykge1xuICAgICAgICAgICAgdGhpcy5jb25ncmF0c0RpYWxvZ0NvbnRyb2xsZXIuaGlkZSgpXG4gICAgICAgIH1cblxuICAgICAgICAvLyDmuIXnkIborqHml7blmahcbiAgICAgICAgdGhpcy5jbGVhckNvdW50ZG93blRpbWVyKCk7XG4gICAgfVxuXG5cbiAgICAvL+e7k+eul1xuICAgIHNldENvbmdyYXRzKG5vdGljZVNldHRsZW1lbnQ6IE5vdGljZVNldHRsZW1lbnQpIHtcbiAgICAgICAgLy8g6I635Y+W55So5oi35YiX6KGo77yM5LyY5YWI5L2/55SoIGZpbmFsUmFua2luZ++8jOWFtuasoeS9v+eUqCB1c2Vyc1xuICAgICAgICBsZXQgdXNlckxpc3QgPSBub3RpY2VTZXR0bGVtZW50LmZpbmFsUmFua2luZyB8fCBub3RpY2VTZXR0bGVtZW50LnVzZXJzO1xuXG4gICAgICAgIC8vIOajgOafpeeUqOaIt+WIl+ihqOaYr+WQpuWtmOWcqFxuICAgICAgICBpZiAoIW5vdGljZVNldHRsZW1lbnQgfHwgIXVzZXJMaXN0IHx8ICFBcnJheS5pc0FycmF5KHVzZXJMaXN0KSkge1xuICAgICAgICAgICAgY29uc29sZS53YXJuKCdOb3RpY2VTZXR0bGVtZW50IOeUqOaIt+aVsOaNruaXoOaViDonLCBub3RpY2VTZXR0bGVtZW50KTtcbiAgICAgICAgICAgIEF1ZGlvTWFuYWdlci53aW5BdWRpbygpOyAvLyDpu5jorqTmkq3mlL7og5zliKnpn7PmlYhcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnN0IGN1cnJlbnRVc2VySWQgPSBHbG9iYWxCZWFuLkdldEluc3RhbmNlKCkubG9naW5EYXRhLnVzZXJJbmZvLnVzZXJJZDtcbiAgICAgICAgY29uc3QgaW5kZXggPSB1c2VyTGlzdC5maW5kSW5kZXgoKGl0ZW0pID0+IGl0ZW0udXNlcklkID09PSBjdXJyZW50VXNlcklkKTsvL+aQnOe0olxuICAgICAgICBpZiAoaW5kZXggPj0gMCkgeyAvL+iHquW3seWPguS4jueahOivnSDmiY3kvJrmmL7npLrmraPluLjnmoTog5zliKnlkozlpLHotKXnmoTpn7PmlYjvvIzoh6rlt7HkuI3lj4LkuI7nmoTor50g5bCx5YWo6YOo5pi+56S66IOc5Yip55qE6Z+z5pWIXG4gICAgICAgICAgICBpZiAodXNlckxpc3RbaW5kZXhdLnJhbmsgPT09IDEpIHsgLy/liKTmlq3oh6rlt7HmmK/kuI3mmK/nrKzkuIDlkI1cbiAgICAgICAgICAgICAgICBBdWRpb01hbmFnZXIud2luQXVkaW8oKTtcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgQXVkaW9NYW5hZ2VyLmxvc2VBdWRpbygpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgQXVkaW9NYW5hZ2VyLndpbkF1ZGlvKCk7XG4gICAgICAgIH1cblxuICAgIH1cblxuICAgIC8vIOWkhOeQhua4uOaIj+W8gOWni+mAmuefpe+8jOiOt+WPlueCuOW8ueaVsOmHj+WSjOWcsOWbvuexu+Wei1xuICAgIG9uR2FtZVN0YXJ0KGRhdGE6IE5vdGljZVN0YXJ0R2FtZSkge1xuICAgICAgXG5cbiAgICAgICAgLy8g5L+d5a2Y5Zyw5Zu+57G75Z6LXG4gICAgICAgIHRoaXMuY3VycmVudE1hcFR5cGUgPSBkYXRhLm1hcFR5cGUgfHwgMDtcblxuICAgICAgICAvLyDmoLnmja7lnLDlm77nsbvlnovph43nva7lr7nlupTnmoTmo4vnm5jmjqfliLblmahcbiAgICAgICAgaWYgKHRoaXMuY3VycmVudE1hcFR5cGUgPT09IDApIHtcbiAgICAgICAgICAgIC8vIOaWueW9ouWcsOWbvlxuICAgICAgICAgICAgaWYgKHRoaXMuY2hlc3NCb2FyZENvbnRyb2xsZXIpIHtcbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgdGhpcy5jaGVzc0JvYXJkQ29udHJvbGxlci5yZXNldEdhbWVTY2VuZSgpO1xuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKFwi4p2MIGNoZXNzQm9hcmRDb250cm9sbGVyIOS4jeWtmOWcqO+8gVwiKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSBlbHNlIGlmICh0aGlzLmN1cnJlbnRNYXBUeXBlID09PSAxKSB7XG4gICAgICAgICAgICAvLyDlha3ovrnlvaLlnLDlm75cbiAgICAgICAgICAgIGlmICh0aGlzLmhleENoZXNzQm9hcmRDb250cm9sbGVyKSB7XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgIHRoaXMuaGV4Q2hlc3NCb2FyZENvbnRyb2xsZXIucmVzZXRHYW1lU2NlbmUoKTtcblxuICAgICAgICAgICAgICAgIC8vIOW/veeVpeacjeWKoeWZqOeahCB2YWxpZEhleENvb3Jkc++8jOS9v+eUqOWJjeerr+iKgueCueWdkOagh1xuICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgdGhpcy5oZXhDaGVzc0JvYXJkQ29udHJvbGxlci5zZXRWYWxpZEhleENvb3JkcyhbXSk7ICAvLyDkvKDlhaXnqbrmlbDnu4TvvIzkvJrooqvlv73nlaVcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihcIuKdjCBoZXhDaGVzc0JvYXJkQ29udHJvbGxlciDkuI3lrZjlnKjvvIFcIik7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICAvLyDph43nva7muLjmiI/nirbmgIFcbiAgICAgICAgdGhpcy5jYW5PcGVyYXRlID0gZmFsc2U7XG4gICAgICAgIHRoaXMuaGFzT3BlcmF0ZWRUaGlzUm91bmQgPSBmYWxzZTtcbiAgICAgICAgdGhpcy5jdXJyZW50Um91bmROdW1iZXIgPSAwO1xuICAgICAgICB0aGlzLmN1cnJlbnRDb3VudGRvd24gPSAwO1xuICAgICAgICB0aGlzLmdhbWVTdGF0dXMgPSAwO1xuXG4gICAgICAgIC8vIOagueaNruWcsOWbvuexu+Wei+iOt+WPlueCuOW8ueaVsOmHj1xuICAgICAgICBpZiAoZGF0YS5tYXBUeXBlID09PSAwICYmIGRhdGEubWFwQ29uZmlnKSB7XG4gICAgICAgICAgICAvLyDmlrnlvaLlnLDlm75cbiAgICAgICAgICAgIHRoaXMuY3VycmVudE1pbmVDb3VudCA9IGRhdGEubWFwQ29uZmlnLm1pbmVDb3VudCB8fCAxMztcbiAgICAgICAgfSBlbHNlIGlmIChkYXRhLm1hcFR5cGUgPT09IDEpIHtcbiAgICAgICAgICAgIC8vIOWFrei+ueW9ouWcsOWbvu+8jOagueaNruWJjeerr+iKgueCueaVsOmHj+iuoeeul+eCuOW8ueaVsOmHj1xuICAgICAgICAgICAgaWYgKHRoaXMuaGV4Q2hlc3NCb2FyZENvbnRyb2xsZXIpIHtcbiAgICAgICAgICAgICAgICB0aGlzLmN1cnJlbnRNaW5lQ291bnQgPSB0aGlzLmhleENoZXNzQm9hcmRDb250cm9sbGVyLmdldFJlY29tbWVuZGVkTWluZUNvdW50KCk7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIHRoaXMuY3VycmVudE1pbmVDb3VudCA9IDE1OyAvLyDlpIfnlKjlm7rlrprlgLxcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIC8vIOm7mOiupOWAvFxuICAgICAgICAgICAgdGhpcy5jdXJyZW50TWluZUNvdW50ID0gMTM7XG4gICAgICAgIH1cblxuICAgICAgICAvLyDmm7TmlrDngrjlvLnmlbBVSVxuICAgICAgICB0aGlzLnVwZGF0ZU1pbmVDb3VudERpc3BsYXkodGhpcy5jdXJyZW50TWluZUNvdW50KTtcblxuICAgICAgICAvLyDmoLnmja7lnLDlm77nsbvlnovmjqfliLblnLDlm77oioLngrnnmoTmmL7npLrkuI7pmpDol49cbiAgICAgICAgdGhpcy5zd2l0Y2hNYXBEaXNwbGF5KHRoaXMuY3VycmVudE1hcFR5cGUpO1xuXG4gICAgICAgIC8vIOWIneWni+WMluWIhuaVsOeVjOmdou+8iOS9v+eUqOWQjuerr+S8oOWbnuadpeeahOecn+WunuaVsOaNru+8iVxuICAgICAgICBpZiAodGhpcy5nYW1lU2NvcmVDb250cm9sbGVyKSB7XG4gICAgICAgICAgICB0aGlzLmdhbWVTY29yZUNvbnRyb2xsZXIuaW5pdGlhbGl6ZVNjb3JlVmlldygpO1xuXG4gICAgICAgIH1cblxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOa1i+ivlemHjee9ruWKn+iDve+8iOWPr+S7peWcqOa1j+iniOWZqOaOp+WItuWPsOaJi+WKqOiwg+eUqO+8iVxuICAgICAqL1xuICAgIHB1YmxpYyB0ZXN0UmVzZXQoKSB7XG4gICAgICAgIGlmICh0aGlzLmNoZXNzQm9hcmRDb250cm9sbGVyKSB7XG4gICAgICAgICAgICB0aGlzLmNoZXNzQm9hcmRDb250cm9sbGVyLnJlc2V0R2FtZVNjZW5lKCk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKFwi4p2MIGNoZXNzQm9hcmRDb250cm9sbGVyIOS4jeWtmOWcqO+8gVwiKTtcbiAgICAgICAgfVxuICAgIH1cblxuXG5cbiAgICAvLyDlpITnkIbmiavpm7flm57lkIjlvIDlp4vpgJrnn6VcbiAgICBvbk5vdGljZVJvdW5kU3RhcnQoZGF0YTogTm90aWNlUm91bmRTdGFydCkge1xuICAgICAgIFxuXG4gICAgICAgIHRoaXMuY3VycmVudFJvdW5kTnVtYmVyID0gZGF0YS5yb3VuZE51bWJlciB8fCAxO1xuICAgICAgICB0aGlzLmN1cnJlbnRDb3VudGRvd24gPSBkYXRhLmNvdW50RG93biB8fCAyNTtcbiAgICAgICAgdGhpcy5nYW1lU3RhdHVzID0gZGF0YS5nYW1lU3RhdHVzIHx8IDA7XG5cbiAgICAgICAgLy8g5paw5Zue5ZCI5byA5aeL77yM6YeN572u5pON5L2c54q25oCBXG4gICAgICAgIHRoaXMuY2FuT3BlcmF0ZSA9IHRydWU7XG4gICAgICAgIHRoaXMuaGFzT3BlcmF0ZWRUaGlzUm91bmQgPSBmYWxzZTtcblxuICAgICAgICAvLyDmuIXnkIbmo4vnm5jkuIrnmoTmiYDmnInnjqnlrrbpooTliLbkvZNcbiAgICAgICAgaWYgKHRoaXMuY3VycmVudE1hcFR5cGUgPT09IDAgJiYgdGhpcy5jaGVzc0JvYXJkQ29udHJvbGxlcikge1xuICAgICAgICAgICAgLy8g5pa55b2i5Zyw5Zu+XG4gICAgICAgICAgICB0aGlzLmNoZXNzQm9hcmRDb250cm9sbGVyLmNsZWFyQWxsUGxheWVyTm9kZXMoKTtcbiAgICAgICAgfSBlbHNlIGlmICh0aGlzLmN1cnJlbnRNYXBUeXBlID09PSAxICYmIHRoaXMuaGV4Q2hlc3NCb2FyZENvbnRyb2xsZXIpIHtcbiAgICAgICAgICAgIC8vIOWFrei+ueW9ouWcsOWbvlxuICAgICAgICAgICAgdGhpcy5oZXhDaGVzc0JvYXJkQ29udHJvbGxlci5jbGVhckFsbFBsYXllck5vZGVzKCk7XG4gICAgICAgIH1cblxuICAgICAgIFxuXG4gICAgICAgIC8vIOW8gOWni+WAkuiuoeaXtlxuICAgICAgICB0aGlzLnN0YXJ0Q291bnRkb3duKHRoaXMuY3VycmVudENvdW50ZG93bik7XG4gICAgfVxuXG4gICAgLy8g5aSE55CG5omr6Zu35pON5L2c5bGV56S66YCa55+lXG4gICAgb25Ob3RpY2VBY3Rpb25EaXNwbGF5KGRhdGE6IE5vdGljZUFjdGlvbkRpc3BsYXkpIHtcblxuICAgICAgICAvLyDkv53lrZjlvZPliY1Ob3RpY2VBY3Rpb25EaXNwbGF55pWw5o2u77yM55So5LqO5YCS6K6h5pe25pi+56S66YC76L6RXG4gICAgICAgIHRoaXMuY3VycmVudE5vdGljZUFjdGlvbkRhdGEgPSBkYXRhO1xuXG4gICAgICAgIC8vIOi/m+WFpeWxleekuumYtuaute+8jOS4jeiDveWGjeaTjeS9nFxuICAgICAgICB0aGlzLmNhbk9wZXJhdGUgPSBmYWxzZTtcbiAgICAgICAgdGhpcy5nYW1lU3RhdHVzID0gZGF0YS5nYW1lU3RhdHVzIHx8IDA7XG5cbiAgICAgICAgLy8g5qC55o2uY291bnREb3du6YeN572u5YCS6K6h5pe25Li6NeenklxuICAgICAgICB0aGlzLmN1cnJlbnRDb3VudGRvd24gPSBkYXRhLmNvdW50RG93biB8fCA1O1xuICAgICAgICB0aGlzLnVwZGF0ZUNvdW50ZG93bkRpc3BsYXkodGhpcy5jdXJyZW50Q291bnRkb3duKTtcbiAgICAgICAgdGhpcy5zdGFydENvdW50ZG93bih0aGlzLmN1cnJlbnRDb3VudGRvd24pO1xuXG4gICAgICAgIC8vIOabtOaWsOWJqeS9meeCuOW8ueaVsOmHj+aYvuekulxuICAgICAgICBpZiAoZGF0YS5yZW1haW5pbmdNaW5lcyAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICB0aGlzLnVwZGF0ZU1pbmVDb3VudERpc3BsYXkoZGF0YS5yZW1haW5pbmdNaW5lcyk7XG4gICAgICAgIH1cblxuICAgICAgICAvLyDlnKjmo4vnm5jkuIrmmL7npLrmiYDmnInnjqnlrrbnmoTmk43kvZzvvIjlpLTlg4/vvIlcbiAgICAgICAgdGhpcy5kaXNwbGF5UGxheWVyQWN0aW9ucyhkYXRhLnBsYXllckFjdGlvbnMsIGRhdGEucGxheWVyVG90YWxTY29yZXMpO1xuXG4gICAgICAgIC8vIOeri+WNs+aYvuekuuWFiOaJiysx77yI5aaC5p6c5YWI5omL5LiN5piv5oiR77yJXG4gICAgICAgIHRoaXMuc2hvd0ZpcnN0Q2hvaWNlQm9udXNJbW1lZGlhdGVseShkYXRhLnBsYXllckFjdGlvbnMpO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOeri+WNs+aYvuekuuWFiOaJiysx5aWW5YqxXG4gICAgICogQHBhcmFtIHBsYXllckFjdGlvbnMg546p5a625pON5L2c5YiX6KGoXG4gICAgICovXG4gICAgcHJpdmF0ZSBzaG93Rmlyc3RDaG9pY2VCb251c0ltbWVkaWF0ZWx5KHBsYXllckFjdGlvbnM6IFBsYXllckFjdGlvbkRpc3BsYXlbXSkge1xuICAgICAgICAvLyDojrflj5blvZPliY3nlKjmiLdJRFxuICAgICAgICBjb25zdCBjdXJyZW50VXNlcklkID0gR2xvYmFsQmVhbi5HZXRJbnN0YW5jZSgpLmxvZ2luRGF0YT8udXNlckluZm8/LnVzZXJJZDtcbiAgICAgICAgaWYgKCFjdXJyZW50VXNlcklkKSB7XG4gICAgICAgICAgICBjb25zb2xlLndhcm4oXCLml6Dms5Xojrflj5blvZPliY3nlKjmiLdJRFwiKTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOafpeaJvuWFiOaJi+eOqeWutlxuICAgICAgICBjb25zdCBmaXJzdENob2ljZVBsYXllciA9IHBsYXllckFjdGlvbnMuZmluZChhY3Rpb24gPT4gYWN0aW9uLmlzRmlyc3RDaG9pY2UpO1xuXG4gICAgICAgIGlmIChmaXJzdENob2ljZVBsYXllcikge1xuICAgICAgICAgICAgY29uc3QgZmlyc3RDaG9pY2VVc2VySW5kZXggPSB0aGlzLmZpbmRVc2VySW5kZXgoZmlyc3RDaG9pY2VQbGF5ZXIudXNlcklkKTtcbiAgICAgICAgICAgIGlmIChmaXJzdENob2ljZVVzZXJJbmRleCAhPT0gLTEpIHtcbiAgICAgICAgICAgICAgICAvLyDnq4vljbPmmL7npLrlhYjmiYsrMVxuICAgICAgICAgICAgICAgIHRoaXMuc2hvd1Njb3JlSW5TY29yZVBhbmVsKGZpcnN0Q2hvaWNlVXNlckluZGV4LCAxKTtcbiAgICAgICAgICAgICAgICAvLyDlkIzml7blnKhwbGF5ZXJfZ2FtZV9wZmLmmL7npLrlhYjmiYsrMVxuICAgICAgICAgICAgICAgIHRoaXMuc2hvd1Njb3JlT25QbGF5ZXJBdmF0YXIoZmlyc3RDaG9pY2VQbGF5ZXIudXNlcklkLCAxKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOW7tui/n+abtOaWsOaji+ebmOeahOWbnuiwg+aWueazlVxuICAgICAqIEBwYXJhbSBkYXRhIE5vdGljZUFjdGlvbkRpc3BsYXnmlbDmja5cbiAgICAgKi9cbiAgICBwcml2YXRlIGRlbGF5ZWRVcGRhdGVCb2FyZChkYXRhOiBOb3RpY2VBY3Rpb25EaXNwbGF5KSB7XG5cbiAgICAgICAgdGhpcy51cGRhdGVCb2FyZEFmdGVyQWN0aW9ucyhkYXRhKTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDmm7TmlrDmo4vnm5jvvIjliKDpmaTmoLzlrZDjgIHnlJ/miJDpooTliLbkvZPjgIHov57plIHliqjnlLvvvIlcbiAgICAgKiBAcGFyYW0gZGF0YSBOb3RpY2VBY3Rpb25EaXNwbGF55pWw5o2uXG4gICAgICovXG4gICAgcHJpdmF0ZSB1cGRhdGVCb2FyZEFmdGVyQWN0aW9ucyhkYXRhOiBOb3RpY2VBY3Rpb25EaXNwbGF5KSB7XG4gICAgICAgIC8vIOazqOaEj++8muWIhuaVsOWKqOeUu+WSjOWktOWDj+WIoOmZpOeOsOWcqOeUseWAkuiuoeaXtumAu+i+keaOp+WItu+8jOi/memHjOebtOaOpeWkhOeQhuagvOWtkOmakOiXj+WSjOaVsOWtl+eUn+aIkFxuXG4gICAgICAgIC8vIOeri+WNs+WkhOeQhuavj+S4queOqeWutueahOaTjeS9nOe7k+aenFxuICAgICAgICAvLyDlhYjmjInkvY3nva7liIbnu4TvvIzlpITnkIblkIzkuIDkvY3nva7mnInlpJrkuKrmk43kvZznmoTmg4XlhrVcbiAgICAgICAgY29uc3QgcHJvY2Vzc2VkUG9zaXRpb25zID0gbmV3IFNldDxzdHJpbmc+KCk7XG5cbiAgICAgICAgZGF0YS5wbGF5ZXJBY3Rpb25zLmZvckVhY2goYWN0aW9uID0+IHtcbiAgICAgICAgICAgIGNvbnN0IHBvc2l0aW9uS2V5ID0gYCR7YWN0aW9uLnh9LCR7YWN0aW9uLnl9YDtcblxuICAgICAgICAgICAgLy8g5aaC5p6c6L+Z5Liq5L2N572u5bey57uP5aSE55CG6L+H77yM6Lez6L+HXG4gICAgICAgICAgICBpZiAocHJvY2Vzc2VkUG9zaXRpb25zLmhhcyhwb3NpdGlvbktleSkpIHtcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIC8vIOafpeaJvuWQjOS4gOS9jee9rueahOaJgOacieaTjeS9nFxuICAgICAgICAgICAgY29uc3Qgc2FtZVBvc2l0aW9uQWN0aW9ucyA9IGRhdGEucGxheWVyQWN0aW9ucy5maWx0ZXIoYSA9PlxuICAgICAgICAgICAgICAgIGEueCA9PT0gYWN0aW9uLnggJiYgYS55ID09PSBhY3Rpb24ueVxuICAgICAgICAgICAgKTtcblxuICAgICAgICAgICAgLy8g5aSE55CG5ZCM5LiA5L2N572u55qE5pON5L2c57uT5p6c77yI5qC85a2Q6ZqQ6JeP5ZKM5pWw5a2X55Sf5oiQ77yJXG4gICAgICAgICAgICB0aGlzLnByb2Nlc3NQb3NpdGlvblJlc3VsdChhY3Rpb24ueCwgYWN0aW9uLnksIHNhbWVQb3NpdGlvbkFjdGlvbnMpO1xuXG4gICAgICAgICAgICAvLyDmoIforrDov5nkuKrkvY3nva7lt7LlpITnkIZcbiAgICAgICAgICAgIHByb2Nlc3NlZFBvc2l0aW9ucy5hZGQocG9zaXRpb25LZXkpO1xuICAgICAgICB9KTtcblxuICAgICAgICAvLyDlpITnkIbov57plIHlsZXlvIDnu5PmnpxcbiAgICAgICAgaWYgKGRhdGEuZmxvb2RGaWxsUmVzdWx0cyAmJiBkYXRhLmZsb29kRmlsbFJlc3VsdHMubGVuZ3RoID4gMCkge1xuICAgICAgICAgICAgZGF0YS5mbG9vZEZpbGxSZXN1bHRzLmZvckVhY2goZmxvb2RGaWxsID0+IHtcbiAgICAgICAgICAgICAgICB0aGlzLnByb2Nlc3NGbG9vZEZpbGxSZXN1bHQoZmxvb2RGaWxsKTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog6K6p5omA5pyJ5aS05YOP5raI5aSx77yI5pSv5oyB5pa55b2i5Zyw5Zu+5ZKM5YWt6L655b2i5Zyw5Zu+77yJXG4gICAgICogQHBhcmFtIHBsYXllckFjdGlvbnMg546p5a625pON5L2c5YiX6KGoXG4gICAgICogQHBhcmFtIG9uQ29tcGxldGUg5a6M5oiQ5Zue6LCDXG4gICAgICovXG4gICAgcHJpdmF0ZSBoaWRlQWxsQXZhdGFycyhwbGF5ZXJBY3Rpb25zOiBQbGF5ZXJBY3Rpb25EaXNwbGF5W10sIG9uQ29tcGxldGU6ICgpID0+IHZvaWQpIHtcbiAgICAgICAgLy8g5qC55o2u5Zyw5Zu+57G75Z6L6LCD55So5a+55bqU55qE5o6n5Yi25ZmoXG4gICAgICAgIGlmICh0aGlzLmN1cnJlbnRNYXBUeXBlID09PSAwICYmIHRoaXMuY2hlc3NCb2FyZENvbnRyb2xsZXIpIHtcbiAgICAgICAgICAgIC8vIOaWueW9ouWcsOWbvu+8muebtOaOpeiwg+eUqOS4gOasoeWktOWDj+WIoOmZpO+8jOS4jeWMuuWIhuS9jee9rlxuICAgICAgICAgICAgdGhpcy5jaGVzc0JvYXJkQ29udHJvbGxlci5oaWRlQXZhdGFyc0F0UG9zaXRpb24oMCwgMCwgKCkgPT4ge1xuICAgICAgICAgICAgICAgIG9uQ29tcGxldGUoKTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9IGVsc2UgaWYgKHRoaXMuY3VycmVudE1hcFR5cGUgPT09IDEgJiYgdGhpcy5oZXhDaGVzc0JvYXJkQ29udHJvbGxlcikge1xuICAgICAgICAgICAgLy8g5YWt6L655b2i5Zyw5Zu+77ya55u05o6l6LCD55So5pa55rOV77yI5bey57uP57yW6K+R5oiQ5Yqf77yJXG4gICAgICAgICAgICB0aGlzLmhleENoZXNzQm9hcmRDb250cm9sbGVyLmhpZGVBbGxIZXhBdmF0YXJzKCgpID0+IHtcbiAgICAgICAgICAgICAgICBvbkNvbXBsZXRlKCk7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIC8vIOayoeacieWPr+eUqOeahOaOp+WItuWZqO+8jOebtOaOpeaJp+ihjOWbnuiwg1xuICAgICAgICAgICAgY29uc29sZS53YXJuKFwi5rKh5pyJ5Y+v55So55qE5qOL55uY5o6n5Yi25Zmo77yM6Lez6L+H5aS05YOP5raI5aSx5Yqo55S7XCIpO1xuICAgICAgICAgICAgb25Db21wbGV0ZSgpO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5aSE55CG5ZCM5LiA5L2N572u55qE5aSa5Liq5pON5L2c57uT5p6cXG4gICAgICogQHBhcmFtIHgg5qC85a2QeOWdkOagh++8iOaWueW9ouWcsOWbvu+8ieaIlnHlnZDmoIfvvIjlha3ovrnlvaLlnLDlm77vvIlcbiAgICAgKiBAcGFyYW0geSDmoLzlrZB55Z2Q5qCH77yI5pa55b2i5Zyw5Zu+77yJ5oiWcuWdkOagh++8iOWFrei+ueW9ouWcsOWbvu+8iVxuICAgICAqIEBwYXJhbSBhY3Rpb25zIOivpeS9jee9rueahOaJgOacieaTjeS9nFxuICAgICAqL1xuICAgIHByaXZhdGUgcHJvY2Vzc1Bvc2l0aW9uUmVzdWx0KHg6IG51bWJlciwgeTogbnVtYmVyLCBhY3Rpb25zOiBQbGF5ZXJBY3Rpb25EaXNwbGF5W10pIHtcbiAgICAgICAgLy8g5qC55o2u5Zyw5Zu+57G75Z6L5Yig6Zmk6K+l5L2N572u55qE5qC85a2Q77yI56uL5Y2z6ZqQ6JeP77yM5LiN5pKt5pS+5Yqo55S777yJXG4gICAgICAgIGlmICh0aGlzLmN1cnJlbnRNYXBUeXBlID09PSAwKSB7XG4gICAgICAgICAgICAvLyDmlrnlvaLlnLDlm75cbiAgICAgICAgICAgIHRoaXMuY2hlc3NCb2FyZENvbnRyb2xsZXIucmVtb3ZlR3JpZEF0KHgsIHksIHRydWUpO1xuICAgICAgICB9IGVsc2UgaWYgKHRoaXMuY3VycmVudE1hcFR5cGUgPT09IDEpIHtcbiAgICAgICAgICAgIC8vIOWFrei+ueW9ouWcsOWbvu+8jHjlrp7pmYXmmK9x5Z2Q5qCH77yMeeWunumZheaYr3LlnZDmoIdcbiAgICAgICAgICAgIGlmICh0aGlzLmhleENoZXNzQm9hcmRDb250cm9sbGVyKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5oZXhDaGVzc0JvYXJkQ29udHJvbGxlci5oaWRlSGV4R3JpZEF0KHgsIHksIHRydWUpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgLy8g5qOA5p+l5piv5ZCm5pyJ5Zyw6Zu36KKr54K55Ye777yIYWN0aW9uPTHkuJRyZXN1bHQ9XCJtaW5lXCLvvIlcbiAgICAgICAgY29uc3QgbWluZUNsaWNrQWN0aW9uID0gYWN0aW9ucy5maW5kKGFjdGlvbiA9PlxuICAgICAgICAgICAgYWN0aW9uLmFjdGlvbiA9PT0gMSAmJiBhY3Rpb24ucmVzdWx0ID09PSBcIm1pbmVcIlxuICAgICAgICApO1xuXG4gICAgICAgIGlmIChtaW5lQ2xpY2tBY3Rpb24pIHtcbiAgICAgICAgICAgIC8vIOWmguaenOacieWcsOmbt+iiq+eCueWHu++8jOebtOaOpeaYvuekuueCuOW8ue+8jOS4jeeuoeaYr+WQpuacieagh+iusFxuICAgICAgICAgICAgLy8g5Yik5pat5piv5ZCm5piv5b2T5YmN55So5oi354K55Yiw55qE6Zu3XG4gICAgICAgICAgICBjb25zdCBjdXJyZW50VXNlcklkID0gR2xvYmFsQmVhbi5HZXRJbnN0YW5jZSgpLmxvZ2luRGF0YT8udXNlckluZm8/LnVzZXJJZDtcbiAgICAgICAgICAgIGNvbnN0IGlzQ3VycmVudFVzZXIgPSBtaW5lQ2xpY2tBY3Rpb24udXNlcklkID09PSBjdXJyZW50VXNlcklkO1xuXG4gICAgICAgICAgICAvLyDmoLnmja7lnLDlm77nsbvlnovosIPnlKjlr7nlupTnmoTmlrnms5VcbiAgICAgICAgICAgIGlmICh0aGlzLmN1cnJlbnRNYXBUeXBlID09PSAwKSB7XG4gICAgICAgICAgICAgICAgLy8g5pa55b2i5Zyw5Zu+XG4gICAgICAgICAgICAgICAgdGhpcy5jaGVzc0JvYXJkQ29udHJvbGxlci5jcmVhdGVCb29tUHJlZmFiKHgsIHksIGlzQ3VycmVudFVzZXIpO1xuICAgICAgICAgICAgfSBlbHNlIGlmICh0aGlzLmN1cnJlbnRNYXBUeXBlID09PSAxKSB7XG4gICAgICAgICAgICAgICAgLy8g5YWt6L655b2i5Zyw5Zu+77yMeOWunumZheaYr3HlnZDmoIfvvIx55a6e6ZmF5pivcuWdkOagh1xuICAgICAgICAgICAgICAgIGlmICh0aGlzLmhleENoZXNzQm9hcmRDb250cm9sbGVyKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuaGV4Q2hlc3NCb2FyZENvbnRyb2xsZXIuY3JlYXRlSGV4Qm9vbVByZWZhYih4LCB5LCBpc0N1cnJlbnRVc2VyKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cblxuICAgICAgICAvLyDlpoLmnpzmsqHmnInlnLDpm7fooqvngrnlh7vvvIzmjInljp/pgLvovpHlpITnkIbnrKzkuIDkuKrmk43kvZznmoTnu5PmnpxcbiAgICAgICAgY29uc3QgZmlyc3RBY3Rpb24gPSBhY3Rpb25zWzBdO1xuICAgICAgICBjb25zdCByZXN1bHQgPSBmaXJzdEFjdGlvbi5yZXN1bHQ7XG5cbiAgICAgICAgaWYgKHJlc3VsdCA9PT0gXCJjb3JyZWN0X21hcmtcIikge1xuICAgICAgICAgICAgLy8g5q2j56Gu5qCH6K6w77ya55Sf5oiQYmlhb2pp6aKE5Yi25L2TXG4gICAgICAgICAgICBpZiAodGhpcy5jdXJyZW50TWFwVHlwZSA9PT0gMCkge1xuICAgICAgICAgICAgICAgIC8vIOaWueW9ouWcsOWbvlxuICAgICAgICAgICAgICAgIHRoaXMuY2hlc3NCb2FyZENvbnRyb2xsZXIuY3JlYXRlQmlhb2ppUHJlZmFiKHgsIHkpO1xuICAgICAgICAgICAgfSBlbHNlIGlmICh0aGlzLmN1cnJlbnRNYXBUeXBlID09PSAxKSB7XG4gICAgICAgICAgICAgICAgLy8g5YWt6L655b2i5Zyw5Zu+XG4gICAgICAgICAgICAgICAgaWYgKHRoaXMuaGV4Q2hlc3NCb2FyZENvbnRyb2xsZXIpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5oZXhDaGVzc0JvYXJkQ29udHJvbGxlci5jcmVhdGVIZXhCaWFvamlQcmVmYWIoeCwgeSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9IGVsc2UgaWYgKHR5cGVvZiByZXN1bHQgPT09IFwibnVtYmVyXCIpIHtcbiAgICAgICAgICAgIC8vIOaVsOWtl++8muabtOaWsG5laWdoYm9yTWluZXPmmL7npLpcbiAgICAgICAgICAgIGlmICh0aGlzLmN1cnJlbnRNYXBUeXBlID09PSAwKSB7XG4gICAgICAgICAgICAgICAgLy8g5pa55b2i5Zyw5Zu+XG4gICAgICAgICAgICAgICAgdGhpcy5jaGVzc0JvYXJkQ29udHJvbGxlci51cGRhdGVOZWlnaGJvck1pbmVzRGlzcGxheSh4LCB5LCByZXN1bHQpO1xuICAgICAgICAgICAgfSBlbHNlIGlmICh0aGlzLmN1cnJlbnRNYXBUeXBlID09PSAxKSB7XG4gICAgICAgICAgICAgICAgLy8g5YWt6L655b2i5Zyw5Zu+XG4gICAgICAgICAgICAgICAgaWYgKHRoaXMuaGV4Q2hlc3NCb2FyZENvbnRyb2xsZXIpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5oZXhDaGVzc0JvYXJkQ29udHJvbGxlci51cGRhdGVIZXhOZWlnaGJvck1pbmVzRGlzcGxheSh4LCB5LCByZXN1bHQpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOWkhOeQhuWNleS4queOqeWutuaTjeS9nOe7k+aenO+8iOS/neeVmeWOn+aWueazleS7pemYsuWFtuS7luWcsOaWueiwg+eUqO+8iVxuICAgICAqIEBwYXJhbSBhY3Rpb24g546p5a625pON5L2c5pWw5o2uXG4gICAgICovXG4gICAgcHJpdmF0ZSBwcm9jZXNzUGxheWVyQWN0aW9uUmVzdWx0KGFjdGlvbjogUGxheWVyQWN0aW9uRGlzcGxheSkge1xuICAgICAgICBjb25zdCB4ID0gYWN0aW9uLng7XG4gICAgICAgIGNvbnN0IHkgPSBhY3Rpb24ueTtcbiAgICAgICAgY29uc3QgcmVzdWx0ID0gYWN0aW9uLnJlc3VsdDtcblxuICAgICAgICAvLyDliKDpmaTor6XkvY3nva7nmoTmoLzlrZBcbiAgICAgICAgdGhpcy5jaGVzc0JvYXJkQ29udHJvbGxlci5yZW1vdmVHcmlkQXQoeCwgeSk7XG5cbiAgICAgICAgLy8g5qC55o2u57uT5p6c55Sf5oiQ55u45bqU55qE6aKE5Yi25L2TXG4gICAgICAgIGlmIChyZXN1bHQgPT09IFwibWluZVwiKSB7XG4gICAgICAgICAgICAvLyDlnLDpm7fvvJrnlJ/miJBib29t6aKE5Yi25L2TXG4gICAgICAgICAgICAvLyDliKTmlq3mmK/lkKbmmK/lvZPliY3nlKjmiLfngrnliLDnmoTpm7dcbiAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRVc2VySWQgPSBHbG9iYWxCZWFuLkdldEluc3RhbmNlKCkubG9naW5EYXRhPy51c2VySW5mbz8udXNlcklkO1xuICAgICAgICAgICAgY29uc3QgaXNDdXJyZW50VXNlciA9IGFjdGlvbi51c2VySWQgPT09IGN1cnJlbnRVc2VySWQ7XG4gICAgICAgICAgICB0aGlzLmNoZXNzQm9hcmRDb250cm9sbGVyLmNyZWF0ZUJvb21QcmVmYWIoeCwgeSwgaXNDdXJyZW50VXNlcik7XG4gICAgICAgIH0gZWxzZSBpZiAocmVzdWx0ID09PSBcImNvcnJlY3RfbWFya1wiKSB7XG4gICAgICAgICAgICAvLyDmraPnoa7moIforrDvvJrnlJ/miJBiaWFvamnpooTliLbkvZNcbiAgICAgICAgICAgIHRoaXMuY2hlc3NCb2FyZENvbnRyb2xsZXIuY3JlYXRlQmlhb2ppUHJlZmFiKHgsIHkpO1xuICAgICAgICB9IGVsc2UgaWYgKHR5cGVvZiByZXN1bHQgPT09IFwibnVtYmVyXCIpIHtcbiAgICAgICAgICAgIC8vIOaVsOWtl++8muabtOaWsG5laWdoYm9yTWluZXPmmL7npLpcbiAgICAgICAgICAgIHRoaXMuY2hlc3NCb2FyZENvbnRyb2xsZXIudXBkYXRlTmVpZ2hib3JNaW5lc0Rpc3BsYXkoeCwgeSwgcmVzdWx0KTtcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOWkhOeQhui/numUgeWxleW8gOe7k+aenFxuICAgICAqIEBwYXJhbSBmbG9vZEZpbGwg6L+e6ZSB5bGV5byA5pWw5o2uXG4gICAgICovXG4gICAgcHJpdmF0ZSBwcm9jZXNzRmxvb2RGaWxsUmVzdWx0KGZsb29kRmlsbDogRmxvb2RGaWxsUmVzdWx0KSB7XG4gICAgICAgIC8vIOeri+WNs+WkhOeQhuaJgOaciei/numUgeagvOWtkO+8jOS4jeWGjeacieW7tui/n1xuICAgICAgICBmbG9vZEZpbGwucmV2ZWFsZWRCbG9ja3MuZm9yRWFjaCgoYmxvY2spID0+IHtcbiAgICAgICAgICAgIGlmICh0aGlzLmN1cnJlbnRNYXBUeXBlID09PSAwKSB7XG4gICAgICAgICAgICAgICAgLy8g5pa55b2i5Zyw5Zu+77ya56uL5Y2z6ZqQ6JeP5qC85a2Q5bm25pi+56S65pWw5a2XXG4gICAgICAgICAgICAgICAgdGhpcy5jaGVzc0JvYXJkQ29udHJvbGxlci5yZW1vdmVHcmlkQXQoYmxvY2sueCwgYmxvY2sueSwgdHJ1ZSk7XG4gICAgICAgICAgICAgICAgLy8g56uL5Y2z5pi+56S65pWw5a2XXG4gICAgICAgICAgICAgICAgaWYgKGJsb2NrLm5laWdoYm9yTWluZXMgPiAwKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuY2hlc3NCb2FyZENvbnRyb2xsZXIudXBkYXRlTmVpZ2hib3JNaW5lc0Rpc3BsYXkoYmxvY2sueCwgYmxvY2sueSwgYmxvY2submVpZ2hib3JNaW5lcyk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSBlbHNlIGlmICh0aGlzLmN1cnJlbnRNYXBUeXBlID09PSAxKSB7XG4gICAgICAgICAgICAgICAgLy8g5YWt6L655b2i5Zyw5Zu+77yMeOWunumZheaYr3HlnZDmoIfvvIx55a6e6ZmF5pivcuWdkOagh1xuICAgICAgICAgICAgICAgIGlmICh0aGlzLmhleENoZXNzQm9hcmRDb250cm9sbGVyKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIOeri+WNs+makOiXj+agvOWtkFxuICAgICAgICAgICAgICAgICAgICB0aGlzLmhleENoZXNzQm9hcmRDb250cm9sbGVyLmhpZGVIZXhHcmlkQXQoYmxvY2sueCwgYmxvY2sueSwgdHJ1ZSk7XG4gICAgICAgICAgICAgICAgICAgIC8vIOeri+WNs+aYvuekuuaVsOWtl++8iOWmguaenOacieeahOivne+8iVxuICAgICAgICAgICAgICAgICAgICBpZiAoYmxvY2submVpZ2hib3JNaW5lcyA+IDApIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuaGV4Q2hlc3NCb2FyZENvbnRyb2xsZXIudXBkYXRlSGV4TmVpZ2hib3JNaW5lc0Rpc3BsYXkoYmxvY2sueCwgYmxvY2sueSwgYmxvY2submVpZ2hib3JNaW5lcyk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgIH1cblxuICAgIC8vIOWkhOeQhuaJq+mbt+WbnuWQiOe7k+adn+mAmuefpVxuICAgIG9uTm90aWNlUm91bmRFbmQoZGF0YTogTm90aWNlUm91bmRFbmQpIHtcblxuICAgICAgICAvLyDov5vlhaXlm57lkIjnu5PmnZ/pmLbmrrXvvIzkuI3og73lho3mk43kvZxcbiAgICAgICAgdGhpcy5jYW5PcGVyYXRlID0gZmFsc2U7XG4gICAgICAgIHRoaXMuZ2FtZVN0YXR1cyA9IGRhdGEuZ2FtZVN0YXR1cyB8fCAxO1xuXG4gICAgICAgIC8vIOS4jeWGjeWkhOeQhuWAkuiuoeaXtu+8jOiuqeWuouaIt+err+iHqueEtuWAkuiuoeaXtuWIsDDvvIzmlrnkvr/lsZXnpLo1NDMyMVxuXG4gICAgICAgIC8vIOWkhOeQhueOqeWutuWIhuaVsOWKqOeUu+WSjOWktOWDj+aYvuekulxuICAgICAgICBpZiAoZGF0YS5wbGF5ZXJSZXN1bHRzICYmIGRhdGEucGxheWVyUmVzdWx0cy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICB0aGlzLmRpc3BsYXlQbGF5ZXJTY29yZUFuaW1hdGlvbnMoZGF0YS5wbGF5ZXJSZXN1bHRzKTtcblxuICAgICAgICAgICAgLy8g5aaC5p6c5pys5Zue5ZCI5oiR5rKh5pyJ5pON5L2c77yM5qC55o2u5ZCO56uv5raI5oGv55Sf5oiQ5oiR55qE5aS05YOPXG4gICAgICAgICAgICB0aGlzLmhhbmRsZU15QXZhdGFySWZOb3RPcGVyYXRlZChkYXRhLnBsYXllclJlc3VsdHMpO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g5riF55CG5qOL55uY5LiK55qE5omA5pyJ546p5a626aKE5Yi25L2TXG4gICAgICAgIGlmICh0aGlzLmN1cnJlbnRNYXBUeXBlID09PSAwICYmIHRoaXMuY2hlc3NCb2FyZENvbnRyb2xsZXIpIHtcbiAgICAgICAgICAgIC8vIOaWueW9ouWcsOWbvlxuICAgICAgICAgICAgdGhpcy5jaGVzc0JvYXJkQ29udHJvbGxlci5jbGVhckFsbFBsYXllck5vZGVzKCk7XG4gICAgICAgIH0gZWxzZSBpZiAodGhpcy5jdXJyZW50TWFwVHlwZSA9PT0gMSAmJiB0aGlzLmhleENoZXNzQm9hcmRDb250cm9sbGVyKSB7XG4gICAgICAgICAgICAvLyDlha3ovrnlvaLlnLDlm75cbiAgICAgICAgICAgIHRoaXMuaGV4Q2hlc3NCb2FyZENvbnRyb2xsZXIuY2xlYXJBbGxQbGF5ZXJzKCk7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDlnKjmo4vnm5jkuIrmmL7npLrmiYDmnInnjqnlrrbnmoTmk43kvZxcbiAgICAgKiBAcGFyYW0gcGxheWVyQWN0aW9ucyDnjqnlrrbmk43kvZzliJfooahcbiAgICAgKiBAcGFyYW0gcGxheWVyVG90YWxTY29yZXMg546p5a625oC75YiG5pWw5o2uXG4gICAgICovXG4gICAgcHJpdmF0ZSBkaXNwbGF5UGxheWVyQWN0aW9ucyhwbGF5ZXJBY3Rpb25zOiBQbGF5ZXJBY3Rpb25EaXNwbGF5W10sIHBsYXllclRvdGFsU2NvcmVzPzoge1t1c2VySWQ6IHN0cmluZ106IG51bWJlcn0pIHtcbiAgICAgICAgLy8g5qOA5p+l5piv5ZCm5pyJ5Y+v55So55qE5qOL55uY5o6n5Yi25ZmoXG4gICAgICAgIGNvbnN0IGhhc1NxdWFyZUJvYXJkID0gdGhpcy5jaGVzc0JvYXJkQ29udHJvbGxlciAmJiB0aGlzLmN1cnJlbnRNYXBUeXBlID09PSAwO1xuICAgICAgICBjb25zdCBoYXNIZXhCb2FyZCA9IHRoaXMuaGV4Q2hlc3NCb2FyZENvbnRyb2xsZXIgJiYgdGhpcy5jdXJyZW50TWFwVHlwZSA9PT0gMTtcblxuICAgICAgICBpZiAoKCFoYXNTcXVhcmVCb2FyZCAmJiAhaGFzSGV4Qm9hcmQpIHx8ICFwbGF5ZXJBY3Rpb25zIHx8IHBsYXllckFjdGlvbnMubGVuZ3RoID09PSAwKSB7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cblxuICAgICAgICAvLyDojrflj5blvZPliY3nlKjmiLdJRFxuICAgICAgICBjb25zdCBjdXJyZW50VXNlcklkID0gR2xvYmFsQmVhbi5HZXRJbnN0YW5jZSgpLmxvZ2luRGF0YT8udXNlckluZm8/LnVzZXJJZDtcbiAgICAgICAgaWYgKCFjdXJyZW50VXNlcklkKSB7XG4gICAgICAgICAgICBjb25zb2xlLndhcm4oXCLml6Dms5Xojrflj5blvZPliY3nlKjmiLdJRFwiKTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOazqOaEj++8muWIhuaVsOWKqOeUu+W3sue7j+WcqHVwZGF0ZUJvYXJkQWZ0ZXJBY3Rpb25z55qE56ys5LiA5q2l5pi+56S65LqG77yM6L+Z6YeM5LiN5YaN6YeN5aSN5pi+56S6XG5cbiAgICAgICAgLy8g5qOA5p+l5pys5Zue5ZCI5piv5ZCm6L+b6KGM5LqG5pON5L2c77yM5aaC5p6c5rKh5pyJ77yM6ZyA6KaB5pi+56S66Ieq5bex55qE5aS05YOPXG4gICAgICAgIGNvbnN0IG15QWN0aW9uID0gcGxheWVyQWN0aW9ucy5maW5kKGFjdGlvbiA9PiBhY3Rpb24udXNlcklkID09PSBjdXJyZW50VXNlcklkKTtcbiAgICAgICAgbGV0IHNob3VsZERpc3BsYXlNeUF2YXRhciA9IGZhbHNlO1xuXG4gICAgICAgIGlmICghdGhpcy5oYXNPcGVyYXRlZFRoaXNSb3VuZCAmJiBteUFjdGlvbikge1xuICAgICAgICAgICAgc2hvdWxkRGlzcGxheU15QXZhdGFyID0gdHJ1ZTtcblxuICAgICAgICAgICAgLy8g55Sf5oiQ5oiR55qE5aS05YOPXG4gICAgICAgICAgICBjb25zdCB3aXRoRmxhZyA9IChteUFjdGlvbi5hY3Rpb24gPT09IDIpOyAvLyBhY3Rpb249MuihqOekuuagh+iusOaTjeS9nO+8jOaYvuekuuaXl+WtkFxuXG4gICAgICAgICAgICBpZiAodGhpcy5jdXJyZW50TWFwVHlwZSA9PT0gMCkge1xuICAgICAgICAgICAgICAgIC8vIOaWueW9ouWcsOWbvlxuICAgICAgICAgICAgICAgIHRoaXMuY2hlc3NCb2FyZENvbnRyb2xsZXIucGxhY2VQbGF5ZXJPbkdyaWQobXlBY3Rpb24ueCwgbXlBY3Rpb24ueSwgd2l0aEZsYWcpO1xuICAgICAgICAgICAgfSBlbHNlIGlmICh0aGlzLmN1cnJlbnRNYXBUeXBlID09PSAxKSB7XG4gICAgICAgICAgICAgICAgLy8g5YWt6L655b2i5Zyw5Zu+77yMeOWunumZheaYr3HlnZDmoIfvvIx55a6e6ZmF5pivcuWdkOagh1xuICAgICAgICAgICAgICAgIGlmICh0aGlzLmhleENoZXNzQm9hcmRDb250cm9sbGVyKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuaGV4Q2hlc3NCb2FyZENvbnRyb2xsZXIucGxhY2VQbGF5ZXJPbkhleEdyaWQobXlBY3Rpb24ueCwgbXlBY3Rpb24ueSwgd2l0aEZsYWcpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOi/h+a7pOaOieiHquW3seeahOaTjeS9nO+8jOWPquaYvuekuuWFtuS7lueOqeWutueahOaTjeS9nFxuICAgICAgICBjb25zdCBvdGhlclBsYXllcnNBY3Rpb25zID0gcGxheWVyQWN0aW9ucy5maWx0ZXIoYWN0aW9uID0+IGFjdGlvbi51c2VySWQgIT09IGN1cnJlbnRVc2VySWQpO1xuXG4gICAgICAgXG5cbiAgICAgICAgaWYgKG90aGVyUGxheWVyc0FjdGlvbnMubGVuZ3RoID09PSAwKSB7XG4gICAgICAgICAgIFxuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g5oyJ5L2N572u5YiG57uE5YW25LuW546p5a6255qE5pON5L2cXG4gICAgICAgIGNvbnN0IHBvc2l0aW9uR3JvdXBzID0gdGhpcy5ncm91cEFjdGlvbnNCeVBvc2l0aW9uKG90aGVyUGxheWVyc0FjdGlvbnMpO1xuXG4gICAgICAgIC8vIOS4uuavj+S4quS9jee9rueUn+aIkOmihOWItuS9k1xuICAgICAgICBwb3NpdGlvbkdyb3Vwcy5mb3JFYWNoKChhY3Rpb25zLCBwb3NpdGlvbktleSkgPT4ge1xuICAgICAgICAgICAgY29uc3QgW3gsIHldID0gcG9zaXRpb25LZXkuc3BsaXQoJywnKS5tYXAoTnVtYmVyKTtcblxuICAgICAgICAgICAgaWYgKHRoaXMuY3VycmVudE1hcFR5cGUgPT09IDApIHtcbiAgICAgICAgICAgICAgICAvLyDmlrnlvaLlnLDlm75cbiAgICAgICAgICAgICAgICB0aGlzLmNoZXNzQm9hcmRDb250cm9sbGVyLmRpc3BsYXlPdGhlclBsYXllcnNBdFBvc2l0aW9uKHgsIHksIGFjdGlvbnMpO1xuICAgICAgICAgICAgfSBlbHNlIGlmICh0aGlzLmN1cnJlbnRNYXBUeXBlID09PSAxKSB7XG4gICAgICAgICAgICAgICAgLy8g5YWt6L655b2i5Zyw5Zu+77yMeOWunumZheaYr3HlnZDmoIfvvIx55a6e6ZmF5pivcuWdkOagh1xuICAgICAgICAgICAgICAgIGlmICh0aGlzLmhleENoZXNzQm9hcmRDb250cm9sbGVyKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIOebtOaOpeiwg+eUqOaWueazle+8iOW3sue7j+e8luivkeaIkOWKn++8iVxuICAgICAgICAgICAgICAgICAgICB0aGlzLmhleENoZXNzQm9hcmRDb250cm9sbGVyLmRpc3BsYXlPdGhlclBsYXllcnNBdEhleFBvc2l0aW9uKHgsIHksIGFjdGlvbnMpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5oyJ5L2N572u5YiG57uE546p5a625pON5L2cXG4gICAgICogQHBhcmFtIHBsYXllckFjdGlvbnMg546p5a625pON5L2c5YiX6KGoXG4gICAgICogQHJldHVybnMgTWFwPHN0cmluZywgUGxheWVyQWN0aW9uRGlzcGxheVtdPiDkvY3nva7kuLprZXnvvIzmk43kvZzliJfooajkuLp2YWx1ZVxuICAgICAqL1xuICAgIHByaXZhdGUgZ3JvdXBBY3Rpb25zQnlQb3NpdGlvbihwbGF5ZXJBY3Rpb25zOiBQbGF5ZXJBY3Rpb25EaXNwbGF5W10pOiBNYXA8c3RyaW5nLCBQbGF5ZXJBY3Rpb25EaXNwbGF5W10+IHtcbiAgICAgICAgY29uc3QgZ3JvdXBzID0gbmV3IE1hcDxzdHJpbmcsIFBsYXllckFjdGlvbkRpc3BsYXlbXT4oKTtcblxuICAgICAgICBmb3IgKGNvbnN0IGFjdGlvbiBvZiBwbGF5ZXJBY3Rpb25zKSB7XG4gICAgICAgICAgICBjb25zdCBwb3NpdGlvbktleSA9IGAke2FjdGlvbi54fSwke2FjdGlvbi55fWA7XG5cbiAgICAgICAgICAgIGlmICghZ3JvdXBzLmhhcyhwb3NpdGlvbktleSkpIHtcbiAgICAgICAgICAgICAgICBncm91cHMuc2V0KHBvc2l0aW9uS2V5LCBbXSk7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIGdyb3Vwcy5nZXQocG9zaXRpb25LZXkpIS5wdXNoKGFjdGlvbik7XG4gICAgICAgIH1cblxuICAgICAgICByZXR1cm4gZ3JvdXBzO1xuICAgIH1cblxuXG5cbiAgICAvKipcbiAgICAgKiDmmL7npLrnjqnlrrbliIbmlbDliqjnlLtcbiAgICAgKiBAcGFyYW0gcGxheWVyUmVzdWx0cyDnjqnlrrblm57lkIjnu5PmnpzliJfooahcbiAgICAgKi9cbiAgICBwcml2YXRlIGRpc3BsYXlQbGF5ZXJTY29yZUFuaW1hdGlvbnMocGxheWVyUmVzdWx0czogUGxheWVyUm91bmRSZXN1bHRbXSkge1xuICAgICAgXG4gICAgICAgIC8vIOiOt+WPluW9k+WJjeeUqOaIt0lEXG4gICAgICAgIGNvbnN0IGN1cnJlbnRVc2VySWQgPSBHbG9iYWxCZWFuLkdldEluc3RhbmNlKCkubG9naW5EYXRhPy51c2VySW5mbz8udXNlcklkO1xuICAgICAgICBpZiAoIWN1cnJlbnRVc2VySWQpIHtcbiAgICAgICAgICAgIGNvbnNvbGUud2FybihcIuaXoOazleiOt+WPluW9k+WJjeeUqOaIt0lEXCIpO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g5Li65q+P5Liq546p5a625pi+56S65YiG5pWw5Yqo55S7XG4gICAgICAgIHBsYXllclJlc3VsdHMuZm9yRWFjaCgocmVzdWx0LCBpbmRleCkgPT4ge1xuICAgICAgICAgXG5cbiAgICAgICAgICAgIC8vIOW7tui/n+aYvuekuu+8jOiuqeWKqOeUu+mUmeW8gFxuICAgICAgICAgICAgdGhpcy5zY2hlZHVsZU9uY2UoKCkgPT4ge1xuICAgICAgICAgICAgICAgIHRoaXMuc2hvd1BsYXllclNjb3JlQW5pbWF0aW9uKHJlc3VsdCwgY3VycmVudFVzZXJJZCk7XG4gICAgICAgICAgICB9LCBpbmRleCAqIDAuMik7XG4gICAgICAgIH0pO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOaYvuekuuWNleS4queOqeWutueahOWIhuaVsOWKqOeUu1xuICAgICAqIEBwYXJhbSByZXN1bHQg546p5a625Zue5ZCI57uT5p6cXG4gICAgICogQHBhcmFtIGN1cnJlbnRVc2VySWQg5b2T5YmN55So5oi3SURcbiAgICAgKi9cbiAgICBwcml2YXRlIHNob3dQbGF5ZXJTY29yZUFuaW1hdGlvbihyZXN1bHQ6IFBsYXllclJvdW5kUmVzdWx0LCBjdXJyZW50VXNlcklkOiBzdHJpbmcpIHtcbiAgICAgICAgY29uc3QgaXNNeXNlbGYgPSByZXN1bHQudXNlcklkID09PSBjdXJyZW50VXNlcklkO1xuXG4gICAgICAgIGlmIChpc015c2VsZikge1xuICAgICAgICAgICAgLy8g6Ieq5bex55qE5YiG5pWw5Yqo55S777ya5ZyocGxheWVyX2dhbWVfcGZi6YeM5Y+q5pi+56S65pys5Zue5ZCI5b6X5YiGXG4gICAgICAgICAgICB0aGlzLnNob3dNeVNjb3JlQW5pbWF0aW9uKHJlc3VsdCk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAvLyDlhbbku5bkurrnmoTliIbmlbDliqjnlLvvvJrmoLnmja5pc0ZpcnN0Q2hvaWNl5Yaz5a6a5pi+56S66YC76L6RXG4gICAgICAgICAgICB0aGlzLnNob3dPdGhlclBsYXllclNjb3JlQW5pbWF0aW9uKHJlc3VsdCk7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDmmL7npLroh6rlt7HnmoTliIbmlbDliqjnlLtcbiAgICAgKiBAcGFyYW0gcmVzdWx0IOeOqeWutuWbnuWQiOe7k+aenFxuICAgICAqL1xuICAgIHByaXZhdGUgc2hvd015U2NvcmVBbmltYXRpb24ocmVzdWx0OiBQbGF5ZXJSb3VuZFJlc3VsdCkge1xuICAgICAgICAvLyDlnKjmo4vnm5jkuIrnmoTlpLTlg4/pooTliLbkvZPkuK3mmL7npLrmnKzlm57lkIjlvpfliIZcbiAgICAgICAgaWYgKHRoaXMuY3VycmVudE1hcFR5cGUgPT09IDAgJiYgdGhpcy5jaGVzc0JvYXJkQ29udHJvbGxlcikge1xuICAgICAgICAgICAgLy8g5pa55b2i5Zyw5Zu+XG4gICAgICAgICAgICB0aGlzLmNoZXNzQm9hcmRDb250cm9sbGVyLnNob3dTY29yZU9uUGxheWVyTm9kZShyZXN1bHQueCwgcmVzdWx0LnksIHJlc3VsdC5zY29yZSwgZmFsc2UpO1xuICAgICAgICB9IGVsc2UgaWYgKHRoaXMuY3VycmVudE1hcFR5cGUgPT09IDEgJiYgdGhpcy5oZXhDaGVzc0JvYXJkQ29udHJvbGxlcikge1xuICAgICAgICAgICAgLy8g5YWt6L655b2i5Zyw5Zu+77yMeOWunumZheaYr3HlnZDmoIfvvIx55a6e6ZmF5pivcuWdkOagh1xuICAgICAgICAgICAgdGhpcy5oZXhDaGVzc0JvYXJkQ29udHJvbGxlci5zaG93U2NvcmVPbkhleFBsYXllck5vZGUocmVzdWx0LngsIHJlc3VsdC55LCByZXN1bHQuc2NvcmUsIGZhbHNlKTtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOWcqHBsYXllcl9zY29yZV9wZmLkuK3mmL7npLrliIbmlbDliqjnlLtcbiAgICAgICAgdGhpcy5zaG93U2NvcmVBbmltYXRpb25JblNjb3JlUGFuZWwocmVzdWx0LnVzZXJJZCwgcmVzdWx0LnNjb3JlLCByZXN1bHQuaXNGaXJzdENob2ljZSk7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5pi+56S65YW25LuW546p5a6255qE5YiG5pWw5Yqo55S7XG4gICAgICogQHBhcmFtIHJlc3VsdCDnjqnlrrblm57lkIjnu5PmnpxcbiAgICAgKi9cbiAgICBwcml2YXRlIHNob3dPdGhlclBsYXllclNjb3JlQW5pbWF0aW9uKHJlc3VsdDogUGxheWVyUm91bmRSZXN1bHQpIHtcbiAgICAgICAgaWYgKHJlc3VsdC5pc0ZpcnN0Q2hvaWNlKSB7XG4gICAgICAgICAgICAvLyDlhbbku5bkurrkuLrlhYjmiYvvvJpwbGF5ZXJfZ2FtZV9wZmLph4zkuI3mmL7npLorMe+8jOWPquaYvuekuuacrOWbnuWQiOW+l+WIhlxuICAgICAgICAgICAgaWYgKHRoaXMuY3VycmVudE1hcFR5cGUgPT09IDAgJiYgdGhpcy5jaGVzc0JvYXJkQ29udHJvbGxlcikge1xuICAgICAgICAgICAgICAgIC8vIOaWueW9ouWcsOWbvlxuICAgICAgICAgICAgICAgIHRoaXMuY2hlc3NCb2FyZENvbnRyb2xsZXIuc2hvd1Njb3JlT25QbGF5ZXJOb2RlKHJlc3VsdC54LCByZXN1bHQueSwgcmVzdWx0LnNjb3JlLCBmYWxzZSk7XG4gICAgICAgICAgICB9IGVsc2UgaWYgKHRoaXMuY3VycmVudE1hcFR5cGUgPT09IDEgJiYgdGhpcy5oZXhDaGVzc0JvYXJkQ29udHJvbGxlcikge1xuICAgICAgICAgICAgICAgIC8vIOWFrei+ueW9ouWcsOWbvlxuICAgICAgICAgICAgICAgIHRoaXMuaGV4Q2hlc3NCb2FyZENvbnRyb2xsZXIuc2hvd1Njb3JlT25IZXhQbGF5ZXJOb2RlKHJlc3VsdC54LCByZXN1bHQueSwgcmVzdWx0LnNjb3JlLCBmYWxzZSk7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIC8vIOWcqHBsYXllcl9zY29yZV9wZmLph4zlhYjmmL7npLorMe+8jOWGjeaYvuekuuacrOWbnuWQiOW+l+WIhu+8jOeEtuWQjuabtOaWsOaAu+WIhlxuICAgICAgICAgICAgdGhpcy5zaG93Rmlyc3RDaG9pY2VTY29yZUFuaW1hdGlvbihyZXN1bHQudXNlcklkLCByZXN1bHQuc2NvcmUpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgLy8g5YW25LuW5Lq66Z2e5YWI5omL77ya5q2j5bi45pi+56S65pys5Zue5ZCI5b6X5YiGXG4gICAgICAgICAgICBpZiAodGhpcy5jdXJyZW50TWFwVHlwZSA9PT0gMCAmJiB0aGlzLmNoZXNzQm9hcmRDb250cm9sbGVyKSB7XG4gICAgICAgICAgICAgICAgLy8g5pa55b2i5Zyw5Zu+XG4gICAgICAgICAgICAgICAgdGhpcy5jaGVzc0JvYXJkQ29udHJvbGxlci5zaG93U2NvcmVPblBsYXllck5vZGUocmVzdWx0LngsIHJlc3VsdC55LCByZXN1bHQuc2NvcmUsIGZhbHNlKTtcbiAgICAgICAgICAgIH0gZWxzZSBpZiAodGhpcy5jdXJyZW50TWFwVHlwZSA9PT0gMSAmJiB0aGlzLmhleENoZXNzQm9hcmRDb250cm9sbGVyKSB7XG4gICAgICAgICAgICAgICAgLy8g5YWt6L655b2i5Zyw5Zu+XG4gICAgICAgICAgICAgICAgdGhpcy5oZXhDaGVzc0JvYXJkQ29udHJvbGxlci5zaG93U2NvcmVPbkhleFBsYXllck5vZGUocmVzdWx0LngsIHJlc3VsdC55LCByZXN1bHQuc2NvcmUsIGZhbHNlKTtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgLy8g5ZyocGxheWVyX3Njb3JlX3BmYuS4reaYvuekuuWIhuaVsOWKqOeUu1xuICAgICAgICAgICAgdGhpcy5zaG93U2NvcmVBbmltYXRpb25JblNjb3JlUGFuZWwocmVzdWx0LnVzZXJJZCwgcmVzdWx0LnNjb3JlLCBmYWxzZSk7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDlnKjliIbmlbDpnaLmnb/kuK3mmL7npLrliIbmlbDliqjnlLtcbiAgICAgKiBAcGFyYW0gdXNlcklkIOeUqOaIt0lEXG4gICAgICogQHBhcmFtIHNjb3JlIOacrOWbnuWQiOW+l+WIhlxuICAgICAqIEBwYXJhbSBpc0ZpcnN0Q2hvaWNlIOaYr+WQpuS4uuWFiOaJi1xuICAgICAqL1xuICAgIHByaXZhdGUgc2hvd1Njb3JlQW5pbWF0aW9uSW5TY29yZVBhbmVsKHVzZXJJZDogc3RyaW5nLCBzY29yZTogbnVtYmVyLCBpc0ZpcnN0Q2hvaWNlOiBib29sZWFuKSB7XG4gICAgICAgIC8vIOi/memHjOmcgOimgeaJvuWIsOWvueW6lOeahFBsYXllclNjb3JlQ29udHJvbGxlcuW5tuiwg+eUqOWIhuaVsOWKqOeUu1xuICAgICAgICAvLyDnlLHkuo7msqHmnInnm7TmjqXnmoTlvJXnlKjvvIzov5nph4zlhYjnlKjml6Xlv5forrDlvZVcbiAgICAgICBcblxuICAgICAgICAvLyBUT0RPOiDlrp7njrDlnKhwbGF5ZXJfc2NvcmVfcGZi5Lit5pi+56S65YiG5pWw5Yqo55S755qE6YC76L6RXG4gICAgICAgIC8vIOmcgOimgeaJvuWIsOWvueW6lOeUqOaIt+eahFBsYXllclNjb3JlQ29udHJvbGxlcuWunuS+i+W5tuiwg+eUqHNob3dBZGRTY29yZeaWueazlVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOaYvuekuuWFiOaJi+eOqeWutueahOWIhuaVsOWKqOeUu++8iOWFiOaYvuekuisx77yM5YaN5pi+56S65pys5Zue5ZCI5b6X5YiG77yJXG4gICAgICogQHBhcmFtIHVzZXJJZCDnlKjmiLdJRFxuICAgICAqIEBwYXJhbSBzY29yZSDmnKzlm57lkIjlvpfliIZcbiAgICAgKi9cbiAgICBwcml2YXRlIHNob3dGaXJzdENob2ljZVNjb3JlQW5pbWF0aW9uKHVzZXJJZDogc3RyaW5nLCBzY29yZTogbnVtYmVyKSB7XG4gICAgICAgXG5cbiAgICAgICAgLy8g5YWI5pi+56S6KzHnmoTlhYjmiYvlpZblirFcbiAgICAgICAgdGhpcy5zY2hlZHVsZU9uY2UoKCkgPT4ge1xuICAgICAgICAgICAgdGhpcy5zaG93U2NvcmVBbmltYXRpb25JblNjb3JlUGFuZWwodXNlcklkLCAxLCB0cnVlKTtcbiAgICAgICAgfSwgMC4xKTtcblxuICAgICAgICAvLyDlho3mmL7npLrmnKzlm57lkIjlvpfliIZcbiAgICAgICAgdGhpcy5zY2hlZHVsZU9uY2UoKCkgPT4ge1xuICAgICAgICAgICAgdGhpcy5zaG93U2NvcmVBbmltYXRpb25JblNjb3JlUGFuZWwodXNlcklkLCBzY29yZSwgZmFsc2UpO1xuICAgICAgICB9LCAxLjIpO1xuXG4gICAgICAgIC8vIOacgOWQjuabtOaWsOaAu+WIhlxuICAgICAgICB0aGlzLnNjaGVkdWxlT25jZSgoKSA9PiB7XG4gICAgICAgICAgICB0aGlzLnVwZGF0ZVBsYXllclRvdGFsU2NvcmUodXNlcklkLCBzY29yZSArIDEpO1xuICAgICAgICB9LCAyLjQpO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOabtOaWsOeOqeWutuaAu+WIhlxuICAgICAqIEBwYXJhbSB1c2VySWQg55So5oi3SURcbiAgICAgKiBAcGFyYW0gdG90YWxTY29yZSDmlrDnmoTmgLvliIZcbiAgICAgKi9cbiAgICBwcml2YXRlIHVwZGF0ZVBsYXllclRvdGFsU2NvcmUodXNlcklkOiBzdHJpbmcsIHRvdGFsU2NvcmU6IG51bWJlcikge1xuICAgICBcblxuICAgICAgICAvLyBUT0RPOiDlrp7njrDmm7TmlrDnjqnlrrbmgLvliIbnmoTpgLvovpFcbiAgICAgICAgLy8g6ZyA6KaB5pu05pawR2xvYmFsQmVhbuS4reeahOeUqOaIt+aVsOaNru+8jOW5tuWIt+aWsFVJ5pi+56S6XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5aaC5p6c5pys5Zue5ZCI5oiR5rKh5pyJ5pON5L2c77yM5qC55o2u5ZCO56uv5raI5oGv55Sf5oiQ5oiR55qE5aS05YOPXG4gICAgICogQHBhcmFtIHBsYXllclJlc3VsdHMg546p5a625Zue5ZCI57uT5p6c5YiX6KGoXG4gICAgICovXG4gICAgcHJpdmF0ZSBoYW5kbGVNeUF2YXRhcklmTm90T3BlcmF0ZWQocGxheWVyUmVzdWx0czogUGxheWVyUm91bmRSZXN1bHRbXSkge1xuICAgICAgICAvLyDojrflj5blvZPliY3nlKjmiLdJRFxuICAgICAgICBjb25zdCBjdXJyZW50VXNlcklkID0gR2xvYmFsQmVhbi5HZXRJbnN0YW5jZSgpLmxvZ2luRGF0YT8udXNlckluZm8/LnVzZXJJZDtcbiAgICAgICAgaWYgKCFjdXJyZW50VXNlcklkKSB7XG4gICAgICAgICAgICBjb25zb2xlLndhcm4oXCLml6Dms5Xojrflj5blvZPliY3nlKjmiLdJRFwiKTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOajgOafpeacrOWbnuWQiOaYr+WQpui/m+ihjOS6huaTjeS9nFxuICAgICAgICBpZiAodGhpcy5oYXNPcGVyYXRlZFRoaXNSb3VuZCkge1xuICAgICAgICAgICBcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICBcblxuICAgICAgICAvLyDmn6Xmib7miJHnmoTmk43kvZznu5PmnpxcbiAgICAgICAgY29uc3QgbXlSZXN1bHQgPSBwbGF5ZXJSZXN1bHRzLmZpbmQocmVzdWx0ID0+IHJlc3VsdC51c2VySWQgPT09IGN1cnJlbnRVc2VySWQpO1xuICAgICAgICBpZiAoIW15UmVzdWx0KSB7XG4gICAgICAgICAgIFxuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgICBcblxuICAgICAgICAvLyDmoLnmja7lkI7nq6/mtojmga/nlJ/miJDmiJHnmoTlpLTlg49cbiAgICAgICAgaWYgKHRoaXMuY2hlc3NCb2FyZENvbnRyb2xsZXIpIHtcbiAgICAgICAgICAgIC8vIOagueaNruaTjeS9nOexu+Wei+WGs+WumuaYr+WQpuaYvuekuuaXl+WtkFxuICAgICAgICAgICAgY29uc3Qgd2l0aEZsYWcgPSAobXlSZXN1bHQuYWN0aW9uID09PSAyKTsgLy8gYWN0aW9uPTLooajnpLrmoIforrDmk43kvZzvvIzmmL7npLrml5flrZBcblxuICAgICAgICAgICAgLy8g55Sf5oiQ5oiR55qE5aS05YOP6aKE5Yi25L2TXG4gICAgICAgICAgICB0aGlzLmNoZXNzQm9hcmRDb250cm9sbGVyLnBsYWNlUGxheWVyT25HcmlkKG15UmVzdWx0LngsIG15UmVzdWx0LnksIHdpdGhGbGFnKTtcblxuICAgICAgICAgICBcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8vIOWPkemAgeeCueWHu+aWueWdl+a2iOaBr1xuICAgIHNlbmRDbGlja0Jsb2NrKHg6IG51bWJlciwgeTogbnVtYmVyLCBhY3Rpb246IG51bWJlcikge1xuICAgICAgICBpZiAoIXRoaXMuY2FuT3BlcmF0ZSkge1xuXG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cblxuICAgICAgICAvLyDmo4Dmn6XmnKzlm57lkIjmmK/lkKblt7Lnu4/mk43kvZzov4dcbiAgICAgICAgaWYgKHRoaXMuaGFzT3BlcmF0ZWRUaGlzUm91bmQpIHtcblxuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgICAgY29uc3QgY2xpY2tEYXRhOiBDbGlja0Jsb2NrUmVxdWVzdCA9IHtcbiAgICAgICAgICAgIHg6IHgsXG4gICAgICAgICAgICB5OiB5LFxuICAgICAgICAgICAgYWN0aW9uOiBhY3Rpb24gLy8gMT3mjJbmjpjmlrnlnZfvvIwyPeagh+iusC/lj5bmtojmoIforrDlnLDpm7dcbiAgICAgICAgfTtcblxuXG4gICAgICAgIFdlYlNvY2tldE1hbmFnZXIuR2V0SW5zdGFuY2UoKS5zZW5kTXNnKE1lc3NhZ2VJZC5Nc2dUeXBlQ2xpY2tCbG9jaywgY2xpY2tEYXRhKTtcblxuICAgICAgICAvLyDmoIforrDmnKzlm57lkIjlt7Lnu4/mk43kvZzov4fvvIzpmLLmraLph43lpI3mk43kvZxcbiAgICAgICAgdGhpcy5oYXNPcGVyYXRlZFRoaXNSb3VuZCA9IHRydWU7XG5cbiAgICB9XG5cbiAgICAvLyDlj5HpgIHlha3ovrnlvaLngrnlh7vmlrnlnZfmtojmga9cbiAgICBzZW5kSGV4Q2xpY2tCbG9jayhxOiBudW1iZXIsIHI6IG51bWJlciwgYWN0aW9uOiBudW1iZXIpIHtcbiAgICAgICAgaWYgKCF0aGlzLmNhbk9wZXJhdGUpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOajgOafpeacrOWbnuWQiOaYr+WQpuW3sue7j+aTjeS9nOi/h1xuICAgICAgICBpZiAodGhpcy5oYXNPcGVyYXRlZFRoaXNSb3VuZCkge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgICBcblxuICAgICAgICAvLyDmoLnmja7lvZPliY3lnLDlm77nsbvlnovlhrPlrprlj5HpgIHmoLzlvI9cbiAgICAgICAgaWYgKHRoaXMuY3VycmVudE1hcFR5cGUgPT09IDEpIHtcbiAgICAgICAgICAgIC8vIOWFrei+ueW9ouWcsOWbvu+8muS9v+eUqOWFrei+ueW9ouWdkOagh+agvOW8j1xuICAgICAgICAgICAgY29uc3QgaGV4Q2xpY2tEYXRhOiBDbGlja0hleEJsb2NrUmVxdWVzdCA9IHtcbiAgICAgICAgICAgICAgICBxOiBxLFxuICAgICAgICAgICAgICAgIHI6IHIsXG4gICAgICAgICAgICAgICAgYWN0aW9uOiBhY3Rpb24gLy8gMT3mjJbmjpjmlrnlnZfvvIwyPeagh+iusC/lj5bmtojmoIforrDlnLDpm7dcbiAgICAgICAgICAgIH07XG5cbiAgICAgICAgICAgIC8vIOazqOaEj++8mui/memHjOS7jeeEtuS9v+eUqCBNc2dUeXBlQ2xpY2tCbG9ja++8jOS9huaVsOaNruagvOW8j+S4jeWQjFxuICAgICAgICAgICAgLy8g5ZCO56uv5bqU6K+l5qC55o2u5b2T5YmN5oi/6Ze055qEIG1hcFR5cGUg5p2l6Kej5p6Q5LiN5ZCM55qE5Z2Q5qCH5qC85byPXG4gICAgICAgICAgICBXZWJTb2NrZXRNYW5hZ2VyLkdldEluc3RhbmNlKCkuc2VuZE1zZyhNZXNzYWdlSWQuTXNnVHlwZUNsaWNrQmxvY2ssIGhleENsaWNrRGF0YSk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAvLyDmlrnlvaLlnLDlm77vvJrovazmjaLkuLp4LHnlnZDmoIfvvIjlpIfnlKjmlrnmoYjvvIlcbiAgICAgICAgICAgIGNvbnN0IGNsaWNrRGF0YTogQ2xpY2tCbG9ja1JlcXVlc3QgPSB7XG4gICAgICAgICAgICAgICAgeDogcSwgLy8g5bCGceS9nOS4unhcbiAgICAgICAgICAgICAgICB5OiByLCAvLyDlsIZy5L2c5Li6eVxuICAgICAgICAgICAgICAgIGFjdGlvbjogYWN0aW9uXG4gICAgICAgICAgICB9O1xuXG4gICAgICAgICAgICBXZWJTb2NrZXRNYW5hZ2VyLkdldEluc3RhbmNlKCkuc2VuZE1zZyhNZXNzYWdlSWQuTXNnVHlwZUNsaWNrQmxvY2ssIGNsaWNrRGF0YSk7XG4gICAgICAgIH1cblxuICAgICAgICAvLyDmoIforrDmnKzlm57lkIjlt7Lnu4/mk43kvZzov4fvvIzpmLLmraLph43lpI3mk43kvZxcbiAgICAgICAgdGhpcy5oYXNPcGVyYXRlZFRoaXNSb3VuZCA9IHRydWU7XG5cbiAgICAgICAgXG4gICAgfVxuXG4gICAgLy8g5qOA5p+l5piv5ZCm5Y+v5Lul5pON5L2cXG4gICAgaXNDYW5PcGVyYXRlKCk6IGJvb2xlYW4ge1xuICAgICAgICByZXR1cm4gdGhpcy5jYW5PcGVyYXRlICYmICF0aGlzLmhhc09wZXJhdGVkVGhpc1JvdW5kO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOWkhOeQhummlumAieeOqeWutuWlluWKsemAmuefpVxuICAgICAqIEBwYXJhbSBkYXRhIE5vdGljZUZpcnN0Q2hvaWNlQm9udXMg5raI5oGv5pWw5o2uXG4gICAgICovXG4gICAgb25Ob3RpY2VGaXJzdENob2ljZUJvbnVzKGRhdGE6IE5vdGljZUZpcnN0Q2hvaWNlQm9udXMpIHtcbiAgICAgIFxuXG4gICAgICAgIC8vIOi9rOWPkee7mUdhbWVTY29yZUNvbnRyb2xsZXLlpITnkIbmiYDmnInnjqnlrrbnmoTliIbmlbDmm7TmlrDlkozliqDliIbliqjnlLtcbiAgICAgICAgaWYgKHRoaXMuZ2FtZVNjb3JlQ29udHJvbGxlcikge1xuICAgICAgICAgICAgdGhpcy5nYW1lU2NvcmVDb250cm9sbGVyLm9uTm90aWNlRmlyc3RDaG9pY2VCb251cyhkYXRhKTtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOWIpOaWreaYr+WQpuS4uuW9k+WJjeeUqOaIt++8jOWmguaenOaYr+WImeWQjOaXtuabtOaWsHBsYXllcl9nYW1lX3BmYuS4reeahGNoYW5nZV9zY29yZVxuICAgICAgICBsZXQgY3VycmVudFVzZXJJZCA9IEdsb2JhbEJlYW4uR2V0SW5zdGFuY2UoKS5sb2dpbkRhdGE/LnVzZXJJbmZvPy51c2VySWQ7XG4gICAgICAgIGxldCBpc015c2VsZiA9IChkYXRhLnVzZXJJZCA9PT0gY3VycmVudFVzZXJJZCk7XG5cbiAgICAgICAgaWYgKGlzTXlzZWxmKSB7XG4gICAgICAgICAgICAvLyDmm7TmlrBwbGF5ZXJfZ2FtZV9wZmLkuK3nmoRjaGFuZ2Vfc2NvcmXmmL7npLpcbiAgICAgICAgICAgIHRoaXMudXBkYXRlUGxheWVyR2FtZVNjb3JlKGRhdGEudXNlcklkLCBkYXRhLmJvbnVzU2NvcmUpO1xuICAgICAgICAgIFxuICAgICAgICB9XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5pu05pawcGxheWVyX2dhbWVfcGZi5Lit55qEY2hhbmdlX3Njb3Jl5pi+56S6XG4gICAgICogQHBhcmFtIHVzZXJJZCDnlKjmiLdJRFxuICAgICAqIEBwYXJhbSBib251c1Njb3JlIOWlluWKseWIhuaVsFxuICAgICAqL1xuICAgIHByaXZhdGUgdXBkYXRlUGxheWVyR2FtZVNjb3JlKHVzZXJJZDogc3RyaW5nLCBib251c1Njb3JlOiBudW1iZXIpIHtcbiAgICAgICBcblxuICAgICAgICAvLyDosIPnlKhDaGVzc0JvYXJkQ29udHJvbGxlcuaYvuekuuWKoOWIhuaViOaenFxuICAgICAgICBpZiAodGhpcy5jaGVzc0JvYXJkQ29udHJvbGxlcikge1xuICAgICAgICAgICAgdGhpcy5jaGVzc0JvYXJkQ29udHJvbGxlci5zaG93UGxheWVyR2FtZVNjb3JlKHVzZXJJZCwgYm9udXNTY29yZSk7XG4gICAgICAgICAgIFxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgY29uc29sZS53YXJuKFwiQ2hlc3NCb2FyZENvbnRyb2xsZXLmnKrorr7nva7vvIzml6Dms5XmmL7npLpwbGF5ZXJfZ2FtZV9wZmLliqDliIbmlYjmnpxcIik7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDojrflj5blvZPliY3lnLDlm77nsbvlnotcbiAgICBnZXRDdXJyZW50TWFwVHlwZSgpOiBudW1iZXIge1xuICAgICAgICByZXR1cm4gdGhpcy5jdXJyZW50TWFwVHlwZTtcbiAgICB9XG5cbiAgICAvLyDojrflj5blvZPliY3ngrjlvLnmlbDph49cbiAgICBnZXRDdXJyZW50TWluZUNvdW50KCk6IG51bWJlciB7XG4gICAgICAgIHJldHVybiB0aGlzLmN1cnJlbnRNaW5lQ291bnQ7XG4gICAgfVxuXG4gICAgLy8g6I635Y+W5b2T5YmN5Zue5ZCI5pON5L2c54q25oCB77yI55So5LqO6LCD6K+V77yJXG4gICAgZ2V0Q3VycmVudFJvdW5kU3RhdHVzKCk6IHtyb3VuZE51bWJlcjogbnVtYmVyLCBjYW5PcGVyYXRlOiBib29sZWFuLCBoYXNPcGVyYXRlZDogYm9vbGVhbn0ge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgcm91bmROdW1iZXI6IHRoaXMuY3VycmVudFJvdW5kTnVtYmVyLFxuICAgICAgICAgICAgY2FuT3BlcmF0ZTogdGhpcy5jYW5PcGVyYXRlLFxuICAgICAgICAgICAgaGFzT3BlcmF0ZWQ6IHRoaXMuaGFzT3BlcmF0ZWRUaGlzUm91bmRcbiAgICAgICAgfTtcbiAgICB9XG5cbiAgICAvLyDlvIDlp4vlgJLorqHml7ZcbiAgICBwcml2YXRlIHN0YXJ0Q291bnRkb3duKHNlY29uZHM6IG51bWJlcikge1xuICAgICAgICAvLyDmuIXpmaTkuYvliY3nmoTorqHml7blmahcbiAgICAgICAgdGhpcy5jbGVhckNvdW50ZG93blRpbWVyKCk7XG5cbiAgICAgICAgbGV0IHJlbWFpbmluZ1NlY29uZHMgPSBzZWNvbmRzO1xuICAgICAgICB0aGlzLnVwZGF0ZUNvdW50ZG93bkRpc3BsYXkocmVtYWluaW5nU2Vjb25kcyk7XG5cbiAgICAgICAgdGhpcy5jb3VudGRvd25JbnRlcnZhbCA9IHNldEludGVydmFsKCgpID0+IHtcbiAgICAgICAgICAgIHJlbWFpbmluZ1NlY29uZHMtLTtcbiAgICAgICAgICAgIHRoaXMudXBkYXRlQ291bnRkb3duRGlzcGxheShyZW1haW5pbmdTZWNvbmRzKTtcblxuICAgICAgICAgICAgLy8g5ZyoTm90aWNlQWN0aW9uRGlzcGxheemYtuaute+8jOagueaNruWAkuiuoeaXtuaJp+ihjOS4jeWQjOeahOaYvuekuumAu+i+kVxuICAgICAgICAgICAgaWYgKHRoaXMuZ2FtZVN0YXR1cyA9PT0gMCAmJiB0aGlzLmN1cnJlbnROb3RpY2VBY3Rpb25EYXRhKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5oYW5kbGVDb3VudGRvd25CYXNlZERpc3BsYXkocmVtYWluaW5nU2Vjb25kcyk7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIGlmIChyZW1haW5pbmdTZWNvbmRzIDw9IDApIHtcbiAgICAgICAgICAgICAgICB0aGlzLmNsZWFyQ291bnRkb3duVGltZXIoKTtcblxuICAgICAgICAgICAgfVxuICAgICAgICB9LCAxMDAwKTtcbiAgICB9XG5cbiAgICAvLyDmm7TmlrDlgJLorqHml7bmmL7npLpcbiAgICBwcml2YXRlIHVwZGF0ZUNvdW50ZG93bkRpc3BsYXkoc2Vjb25kczogbnVtYmVyKSB7XG4gICAgICAgIGlmICh0aGlzLnRpbWVMYWJlbCkge1xuICAgICAgICAgICAgdGhpcy50aW1lTGFiZWwuc3RyaW5nID0gYCR7c2Vjb25kc31zYDsgIC8vIOaYvuekuuaVsOWtl+WKoHPvvJo1cywgNHMsIDNzLCAycywgMXMsIDBzXG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5jdXJyZW50Q291bnRkb3duID0gc2Vjb25kcztcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDmoLnmja7lgJLorqHml7blpITnkIbkuI3lkIzml7bmnLrnmoTmmL7npLrpgLvovpFcbiAgICAgKiBAcGFyYW0gcmVtYWluaW5nU2Vjb25kcyDliankvZnnp5LmlbBcbiAgICAgKi9cbiAgICBwcml2YXRlIGhhbmRsZUNvdW50ZG93bkJhc2VkRGlzcGxheShyZW1haW5pbmdTZWNvbmRzOiBudW1iZXIpIHtcbiAgICAgICAgaWYgKCF0aGlzLmN1cnJlbnROb3RpY2VBY3Rpb25EYXRhKSB7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cblxuICAgICAgICBjb25zdCBkYXRhID0gdGhpcy5jdXJyZW50Tm90aWNlQWN0aW9uRGF0YTtcblxuICAgICAgICBpZiAocmVtYWluaW5nU2Vjb25kcyA9PT0gNCkge1xuICAgICAgICAgICAgLy8gNHPml7bvvJrlkIzml7blsZXnpLrmnKzlm57lkIjliqDlh4/liIZcbiAgICAgICAgICAgIHRoaXMuc2hvd0N1cnJlbnRSb3VuZFNjb3JlcyhkYXRhLnBsYXllckFjdGlvbnMsIGRhdGEucGxheWVyVG90YWxTY29yZXMpO1xuICAgICAgICB9IGVsc2UgaWYgKHJlbWFpbmluZ1NlY29uZHMgPT09IDMpIHtcbiAgICAgICAgICAgIC8vIDNz5pe277ya6ZqQ6JeP5Yqg5YeP5YiG5bm25Yig6Zmk5aS05YOP6aKE5Yi25L2TXG4gICAgICAgICAgICB0aGlzLmhpZGVTY29yZUVmZmVjdHNBbmRBdmF0YXJzKGRhdGEucGxheWVyQWN0aW9ucyk7XG4gICAgICAgICAgICAvLyAzc+aXtu+8mueri+WNs+aJp+ihjOagvOWtkOmakOiXj+WSjOeUn+aIkOaVsOWtl+mihOWItuS9k+etieaTjeS9nFxuICAgICAgICAgICAgdGhpcy51cGRhdGVCb2FyZEFmdGVyQWN0aW9ucyhkYXRhKTtcbiAgICAgICAgICAgIC8vIOa4heepuuaVsOaNru+8jOmBv+WFjemHjeWkjeaJp+ihjFxuICAgICAgICAgICAgdGhpcy5jdXJyZW50Tm90aWNlQWN0aW9uRGF0YSA9IG51bGw7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDmmL7npLrmnKzlm57lkIjmiYDmnInnjqnlrrbnmoTliqDlh4/liIZcbiAgICAgKiBAcGFyYW0gcGxheWVyQWN0aW9ucyDnjqnlrrbmk43kvZzliJfooahcbiAgICAgKiBAcGFyYW0gcGxheWVyVG90YWxTY29yZXMg546p5a625oC75YiG5pWw5o2uXG4gICAgICovXG4gICAgcHJpdmF0ZSBzaG93Q3VycmVudFJvdW5kU2NvcmVzKHBsYXllckFjdGlvbnM6IFBsYXllckFjdGlvbkRpc3BsYXlbXSwgcGxheWVyVG90YWxTY29yZXM6IHtbdXNlcklkOiBzdHJpbmddOiBudW1iZXJ9KSB7XG4gICAgICAgIC8vIOiOt+WPluW9k+WJjeeUqOaIt0lEXG4gICAgICAgIGNvbnN0IGN1cnJlbnRVc2VySWQgPSBHbG9iYWxCZWFuLkdldEluc3RhbmNlKCkubG9naW5EYXRhPy51c2VySW5mbz8udXNlcklkO1xuICAgICAgICBpZiAoIWN1cnJlbnRVc2VySWQpIHtcbiAgICAgICAgICAgIGNvbnNvbGUud2FybihcIuaXoOazleiOt+WPluW9k+WJjeeUqOaIt0lEXCIpO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g5Li65q+P5Liq546p5a625pi+56S65pys5Zue5ZCI55qE5Yqg5YeP5YiGXG4gICAgICAgIHBsYXllckFjdGlvbnMuZm9yRWFjaCgoYWN0aW9uKSA9PiB7XG4gICAgICAgICAgICAvLyDlnKhwbGF5ZXJfZ2FtZV9wZmLkuK3mmL7npLrmnKzlm57lkIjnmoTliqDlh4/liIZcbiAgICAgICAgICAgIHRoaXMuc2hvd1Njb3JlT25QbGF5ZXJBdmF0YXIoYWN0aW9uLnVzZXJJZCwgYWN0aW9uLnNjb3JlKTtcblxuICAgICAgICAgICAgLy8g5Zyo5YiG5pWw6Z2i5p2/5pi+56S65pys5Zue5ZCI55qE5Yqg5YeP5YiGXG4gICAgICAgICAgICBjb25zdCB1c2VySW5kZXggPSB0aGlzLmZpbmRVc2VySW5kZXgoYWN0aW9uLnVzZXJJZCk7XG4gICAgICAgICAgICBpZiAodXNlckluZGV4ICE9PSAtMSkge1xuICAgICAgICAgICAgICAgIHRoaXMuc2hvd1Njb3JlSW5TY29yZVBhbmVsKHVzZXJJbmRleCwgYWN0aW9uLnNjb3JlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSk7XG5cbiAgICAgICAgLy8g5bu26L+f5pu05paw5oC75YiG77yM6K6p5Yqg5YeP5YiG5Yqo55S75YWI5pi+56S6XG4gICAgICAgIHRoaXMuc2NoZWR1bGVPbmNlKCgpID0+IHtcbiAgICAgICAgICAgIC8vIOabtOaWsOaJgOacieeOqeWutueahOaAu+WIhlxuICAgICAgICAgICAgcGxheWVyQWN0aW9ucy5mb3JFYWNoKChhY3Rpb24pID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCB0b3RhbFNjb3JlID0gcGxheWVyVG90YWxTY29yZXNbYWN0aW9uLnVzZXJJZF0gfHwgMDtcbiAgICAgICAgICAgICAgICBpZiAodGhpcy5nYW1lU2NvcmVDb250cm9sbGVyKSB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuZ2FtZVNjb3JlQ29udHJvbGxlci51cGRhdGVQbGF5ZXJTY29yZShhY3Rpb24udXNlcklkLCB0b3RhbFNjb3JlKTtcbiAgICAgICAgICAgICAgICAgICAgLy8g5pu05paw5YWo5bGA5pWw5o2u5Lit55qE5oC75YiGXG4gICAgICAgICAgICAgICAgICAgIHRoaXMudXBkYXRlUGxheWVyVG90YWxTY29yZUluR2xvYmFsRGF0YShhY3Rpb24udXNlcklkLCB0b3RhbFNjb3JlKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfSwgMS4yKTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDpmpDol4/liqDlh4/liIbmlYjmnpzlubbliKDpmaTlpLTlg4/pooTliLbkvZNcbiAgICAgKiBAcGFyYW0gcGxheWVyQWN0aW9ucyDnjqnlrrbmk43kvZzliJfooahcbiAgICAgKi9cbiAgICBwcml2YXRlIGhpZGVTY29yZUVmZmVjdHNBbmRBdmF0YXJzKHBsYXllckFjdGlvbnM6IFBsYXllckFjdGlvbkRpc3BsYXlbXSkge1xuICAgICAgICAvLyDpmpDol4/miYDmnInliqDlh4/liIbmlYjmnpxcbiAgICAgICAgdGhpcy5oaWRlQWxsU2NvcmVFZmZlY3RzKCk7XG5cbiAgICAgICAgLy8g5Yig6Zmk5aS05YOP6aKE5Yi25L2T77yI5LiN562J5b6F5a6M5oiQ5Zue6LCD77yJXG4gICAgICAgIHRoaXMuaGlkZUFsbEF2YXRhcnMocGxheWVyQWN0aW9ucywgKCkgPT4ge1xuICAgICAgICAgICAgY29uc29sZS5sb2coXCLmiYDmnInlpLTlg4/lt7LpmpDol4/lrozmiJBcIik7XG4gICAgICAgIH0pO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOmakOiXj+aJgOacieWKoOWHj+WIhuaViOaenFxuICAgICAqL1xuICAgIHByaXZhdGUgaGlkZUFsbFNjb3JlRWZmZWN0cygpIHtcbiAgICAgICAgLy8g6ZqQ6JeP5YiG5pWw6Z2i5p2/55qE5Yqg5YeP5YiG5pWI5p6cXG4gICAgICAgIC8vIOazqOaEj++8mui/memHjOaaguaXtuS4jeWkhOeQhuWIhuaVsOmdouadv+eahOmakOiXj++8jOWboOS4ulBsYXllclNjb3JlQ29udHJvbGxlcueahGhpZGVTY29yZUVmZmVjdHPkvJrlnKgx56eS5ZCO6Ieq5Yqo6ZqQ6JePXG5cbiAgICAgICAgLy8g6ZqQ6JeP5qOL55uY5LiK5omA5pyJ5aS05YOP55qE5Yqg5YeP5YiG5pWI5p6cXG4gICAgICAgIHRoaXMuaGlkZUFsbFBsYXllckdhbWVTY29yZUVmZmVjdHMoKTtcbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDpmpDol4/mo4vnm5jkuIrmiYDmnInlpLTlg4/nmoTliqDlh4/liIbmlYjmnpxcbiAgICAgKi9cbiAgICBwcml2YXRlIGhpZGVBbGxQbGF5ZXJHYW1lU2NvcmVFZmZlY3RzKCkge1xuICAgICAgICAvLyDpgY3ljobmo4vnm5jkuIrnmoTmiYDmnIlQbGF5ZXJHYW1lQ29udHJvbGxlcu+8jOiwg+eUqGhpZGVTY29yZUVmZmVjdHPmlrnms5VcbiAgICAgICAgaWYgKHRoaXMuY3VycmVudE1hcFR5cGUgPT09IDAgJiYgdGhpcy5jaGVzc0JvYXJkQ29udHJvbGxlciAmJiB0aGlzLmNoZXNzQm9hcmRDb250cm9sbGVyLmJvYXJkTm9kZSkge1xuICAgICAgICAgICAgLy8g5pa55b2i5Zyw5Zu+XG4gICAgICAgICAgICBjb25zdCBjaGlsZHJlbiA9IHRoaXMuY2hlc3NCb2FyZENvbnRyb2xsZXIuYm9hcmROb2RlLmNoaWxkcmVuO1xuICAgICAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBjaGlsZHJlbi5sZW5ndGg7IGkrKykge1xuICAgICAgICAgICAgICAgIGNvbnN0IGNoaWxkID0gY2hpbGRyZW5baV07XG4gICAgICAgICAgICAgICAgY29uc3QgcGxheWVyQ29udHJvbGxlciA9IGNoaWxkLmdldENvbXBvbmVudChQbGF5ZXJHYW1lQ29udHJvbGxlcik7XG4gICAgICAgICAgICAgICAgaWYgKHBsYXllckNvbnRyb2xsZXIpIHtcbiAgICAgICAgICAgICAgICAgICAgcGxheWVyQ29udHJvbGxlci5oaWRlU2NvcmVFZmZlY3RzKCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9IGVsc2UgaWYgKHRoaXMuY3VycmVudE1hcFR5cGUgPT09IDEgJiYgdGhpcy5oZXhDaGVzc0JvYXJkQ29udHJvbGxlciAmJiB0aGlzLmhleENoZXNzQm9hcmRDb250cm9sbGVyLmJvYXJkTm9kZSkge1xuICAgICAgICAgICAgLy8g5YWt6L655b2i5Zyw5Zu+XG4gICAgICAgICAgICBjb25zdCBjaGlsZHJlbiA9IHRoaXMuaGV4Q2hlc3NCb2FyZENvbnRyb2xsZXIuYm9hcmROb2RlLmNoaWxkcmVuO1xuICAgICAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBjaGlsZHJlbi5sZW5ndGg7IGkrKykge1xuICAgICAgICAgICAgICAgIGNvbnN0IGNoaWxkID0gY2hpbGRyZW5baV07XG4gICAgICAgICAgICAgICAgY29uc3QgcGxheWVyQ29udHJvbGxlciA9IGNoaWxkLmdldENvbXBvbmVudChQbGF5ZXJHYW1lQ29udHJvbGxlcik7XG4gICAgICAgICAgICAgICAgaWYgKHBsYXllckNvbnRyb2xsZXIpIHtcbiAgICAgICAgICAgICAgICAgICAgcGxheWVyQ29udHJvbGxlci5oaWRlU2NvcmVFZmZlY3RzKCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuXG4gICAgLy8g5pu05paw54K45by55pWw5pi+56S6XG4gICAgcHJpdmF0ZSB1cGRhdGVNaW5lQ291bnREaXNwbGF5KG1pbmVDb3VudDogbnVtYmVyKSB7XG4gICAgICAgIGlmICh0aGlzLm1pbmVDb3VudExhYmVsKSB7XG4gICAgICAgICAgICB0aGlzLm1pbmVDb3VudExhYmVsLnN0cmluZyA9IGAke21pbmVDb3VudH1gO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgLy8g5qC55o2u5Zyw5Zu+57G75Z6L5YiH5o2i5Zyw5Zu+5pi+56S6XG4gICAgcHJpdmF0ZSBzd2l0Y2hNYXBEaXNwbGF5KG1hcFR5cGU6IG51bWJlcikge1xuICAgICAgICAvLyDlhYjpmpDol4/miYDmnInlnLDlm75cbiAgICAgICAgdGhpcy5oaWRlQWxsTWFwcygpO1xuXG4gICAgICAgIC8vIOagueaNruWcsOWbvuexu+Wei+aYvuekuuWvueW6lOeahOWcsOWbvlxuICAgICAgICBpZiAobWFwVHlwZSA9PT0gMCkge1xuICAgICAgICAgICAgdGhpcy5zaG93U3F1YXJlTWFwKCk7XG4gICAgICAgIH0gZWxzZSBpZiAobWFwVHlwZSA9PT0gMSkge1xuICAgICAgICAgICAgdGhpcy5zaG93SGV4TWFwKCk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBjb25zb2xlLndhcm4oYOacquefpeeahOWcsOWbvuexu+WeizogJHttYXBUeXBlfe+8jOm7mOiupOaYvuekuuaWueW9ouWcsOWbvmApO1xuICAgICAgICAgICAgdGhpcy5zaG93U3F1YXJlTWFwKCk7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDmmL7npLrmlrnlvaLlnLDlm75cbiAgICBwcml2YXRlIHNob3dTcXVhcmVNYXAoKSB7XG4gICAgICAgIGlmICh0aGlzLnNxdWFyZU1hcE5vZGUpIHtcbiAgICAgICAgICAgIHRoaXMuc3F1YXJlTWFwTm9kZS5hY3RpdmUgPSB0cnVlO1xuICAgICAgICAgICBcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGNvbnNvbGUud2Fybign5pa55b2i5Zyw5Zu+6IqC54K55pyq5oyC6L29Jyk7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDmmL7npLrlha3ovrnlvaLlnLDlm75cbiAgICBwcml2YXRlIHNob3dIZXhNYXAoKSB7XG4gICAgICAgIGlmICh0aGlzLmhleE1hcE5vZGUpIHtcbiAgICAgICAgICAgIHRoaXMuaGV4TWFwTm9kZS5hY3RpdmUgPSB0cnVlO1xuICAgICAgICAgICBcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGNvbnNvbGUud2Fybign5YWt6L655b2i5Zyw5Zu+6IqC54K55pyq5oyC6L29Jyk7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyDpmpDol4/miYDmnInlnLDlm75cbiAgICBwcml2YXRlIGhpZGVBbGxNYXBzKCkge1xuICAgICAgICBpZiAodGhpcy5zcXVhcmVNYXBOb2RlKSB7XG4gICAgICAgICAgICB0aGlzLnNxdWFyZU1hcE5vZGUuYWN0aXZlID0gZmFsc2U7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHRoaXMuaGV4TWFwTm9kZSkge1xuICAgICAgICAgICAgdGhpcy5oZXhNYXBOb2RlLmFjdGl2ZSA9IGZhbHNlO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgLy8g5riF6Zmk5YCS6K6h5pe25a6a5pe25ZmoXG4gICAgcHJpdmF0ZSBjbGVhckNvdW50ZG93blRpbWVyKCkge1xuICAgICAgICBpZiAodGhpcy5jb3VudGRvd25JbnRlcnZhbCkge1xuICAgICAgICAgICAgY2xlYXJJbnRlcnZhbCh0aGlzLmNvdW50ZG93bkludGVydmFsKTtcbiAgICAgICAgICAgIHRoaXMuY291bnRkb3duSW50ZXJ2YWwgPSBudWxsO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5pi+56S65omA5pyJ546p5a6255qE5YiG5pWw5Yqo55S75ZKM5pu05paw5oC75YiG77yI5Y+C6ICD5YWI5omL5Yqg5YiG6YC76L6R77yJXG4gICAgICogQHBhcmFtIHBsYXllckFjdGlvbnMg546p5a625pON5L2c5YiX6KGoXG4gICAgICogQHBhcmFtIHBsYXllclRvdGFsU2NvcmVzIOeOqeWutuaAu+WIhuaVsOaNrlxuICAgICAqL1xuICAgIHByaXZhdGUgZGlzcGxheVBsYXllclNjb3JlQW5pbWF0aW9uc0FuZFVwZGF0ZVRvdGFsU2NvcmVzKHBsYXllckFjdGlvbnM6IFBsYXllckFjdGlvbkRpc3BsYXlbXSwgcGxheWVyVG90YWxTY29yZXM6IHtbdXNlcklkOiBzdHJpbmddOiBudW1iZXJ9KSB7XG4gICAgICAgIC8vIOiOt+WPluW9k+WJjeeUqOaIt0lEXG4gICAgICAgIGNvbnN0IGN1cnJlbnRVc2VySWQgPSBHbG9iYWxCZWFuLkdldEluc3RhbmNlKCkubG9naW5EYXRhPy51c2VySW5mbz8udXNlcklkO1xuICAgICAgICBpZiAoIWN1cnJlbnRVc2VySWQpIHtcbiAgICAgICAgICAgIGNvbnNvbGUud2FybihcIuaXoOazleiOt+WPluW9k+WJjeeUqOaIt0lEXCIpO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g5p+l5om+5YWI5omL546p5a62XG4gICAgICAgIGNvbnN0IGZpcnN0Q2hvaWNlUGxheWVyID0gcGxheWVyQWN0aW9ucy5maW5kKGFjdGlvbiA9PiBhY3Rpb24uaXNGaXJzdENob2ljZSk7XG4gICAgICAgIGNvbnN0IGlzQ3VycmVudFVzZXJGaXJzdENob2ljZSA9IGZpcnN0Q2hvaWNlUGxheWVyICYmIGZpcnN0Q2hvaWNlUGxheWVyLnVzZXJJZCA9PT0gY3VycmVudFVzZXJJZDtcblxuICAgICAgXG5cbiAgICAgICAgLy8g5aaC5p6c5oiR5LiN5piv5YWI5omL77yM5YWI5Li65YWI5omL546p5a625Zyo5YiG5pWw6Z2i5p2/5pi+56S6KzFcbiAgICAgICAgaWYgKCFpc0N1cnJlbnRVc2VyRmlyc3RDaG9pY2UgJiYgZmlyc3RDaG9pY2VQbGF5ZXIpIHtcbiAgICAgICAgICAgIGNvbnN0IGZpcnN0Q2hvaWNlVXNlckluZGV4ID0gdGhpcy5maW5kVXNlckluZGV4KGZpcnN0Q2hvaWNlUGxheWVyLnVzZXJJZCk7XG4gICAgICAgICAgICBpZiAoZmlyc3RDaG9pY2VVc2VySW5kZXggIT09IC0xKSB7XG4gICAgICAgICAgICAgIFxuXG4gICAgICAgICAgICAgICAgLy8gMC4x56eS5ZCO5pi+56S65YWI5omLKzFcbiAgICAgICAgICAgICAgICB0aGlzLnNjaGVkdWxlT25jZSgoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc2hvd1Njb3JlSW5TY29yZVBhbmVsKGZpcnN0Q2hvaWNlVXNlckluZGV4LCAxKTtcbiAgICAgICAgICAgICAgICB9LCAwLjEpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgLy8g5Li65q+P5Liq546p5a625pi+56S65YiG5pWw5Yqo55S75ZKM5pu05paw5oC75YiGXG4gICAgICAgIHBsYXllckFjdGlvbnMuZm9yRWFjaCgoYWN0aW9uLCBpbmRleCkgPT4ge1xuICAgICAgICAgICAgY29uc3QgdG90YWxTY29yZSA9IHBsYXllclRvdGFsU2NvcmVzW2FjdGlvbi51c2VySWRdIHx8IDA7XG4gICAgICAgICAgICBjb25zdCBpc0ZpcnN0Q2hvaWNlID0gYWN0aW9uLmlzRmlyc3RDaG9pY2U7XG5cbiAgICAgICAgICAgIC8vIOW7tui/n+aYvuekuu+8jOiuqeWKqOeUu+mUmeW8gFxuICAgICAgICAgICAgdGhpcy5zY2hlZHVsZU9uY2UoKCkgPT4ge1xuICAgICAgICAgICAgICAgIGlmIChpc0ZpcnN0Q2hvaWNlKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIOWFiOaJi+eOqeWutu+8mueJueauiuWkhOeQhu+8iOWFiOaYvuekuisx77yM5YaN5pi+56S65pys5Zue5ZCI5YiG5pWw77yJXG4gICAgICAgICAgICAgICAgICAgIHRoaXMuc2hvd0ZpcnN0Q2hvaWNlUGxheWVyU2NvcmVBbmltYXRpb24oYWN0aW9uLCBjdXJyZW50VXNlcklkLCB0b3RhbFNjb3JlKTtcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAvLyDpnZ7lhYjmiYvnjqnlrrbvvJrnm7TmjqXmmL7npLrmnKzlm57lkIjliIbmlbBcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5zaG93UGxheWVyU2NvcmVBbmltYXRpb25BbmRVcGRhdGVUb3RhbChhY3Rpb24sIGN1cnJlbnRVc2VySWQsIHRvdGFsU2NvcmUpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0sIGluZGV4ICogMC4yKTtcbiAgICAgICAgfSk7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5pi+56S65Y2V5Liq546p5a6255qE5YiG5pWw5Yqo55S75ZKM5pu05paw5oC75YiG77yI5Y+C6ICD5YWI5omL5Yqg5YiG6YC76L6R77yJXG4gICAgICogQHBhcmFtIGFjdGlvbiDnjqnlrrbmk43kvZzmlbDmja5cbiAgICAgKiBAcGFyYW0gY3VycmVudFVzZXJJZCDlvZPliY3nlKjmiLdJRFxuICAgICAqIEBwYXJhbSB0b3RhbFNjb3JlIOeOqeWutuaAu+WIhlxuICAgICAqL1xuICAgIHByaXZhdGUgc2hvd1BsYXllclNjb3JlQW5pbWF0aW9uQW5kVXBkYXRlVG90YWwoYWN0aW9uOiBQbGF5ZXJBY3Rpb25EaXNwbGF5LCBjdXJyZW50VXNlcklkOiBzdHJpbmcsIHRvdGFsU2NvcmU6IG51bWJlcikge1xuICAgICAgICBjb25zdCBpc015c2VsZiA9IGFjdGlvbi51c2VySWQgPT09IGN1cnJlbnRVc2VySWQ7XG5cbiAgICAgICAgLy8gMS4g5Zyo5YiG5pWw6Z2i5p2/5pi+56S65Yqg5YeP5YiG5Yqo55S777yI5Y+C6ICD5YWI5omL5Yqg5YiG55qE6YC76L6R77yJXG4gICAgICAgIGlmICh0aGlzLmdhbWVTY29yZUNvbnRyb2xsZXIpIHtcbiAgICAgICAgICAgIC8vIOaJvuWIsOeUqOaIt+e0ouW8lVxuICAgICAgICAgICAgY29uc3QgdXNlckluZGV4ID0gdGhpcy5maW5kVXNlckluZGV4KGFjdGlvbi51c2VySWQpO1xuICAgICAgICAgICAgaWYgKHVzZXJJbmRleCAhPT0gLTEpIHtcbiAgICAgICAgICAgICAgICAvLyDlnKjliIbmlbDpnaLmnb/mmL7npLrliqDlh4/liIbmlYjmnpxcbiAgICAgICAgICAgICAgICB0aGlzLnNob3dTY29yZUluU2NvcmVQYW5lbCh1c2VySW5kZXgsIGFjdGlvbi5zY29yZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICAvLyAyLiDmm7TmlrDmgLvliIbvvIjlj4LogIPlhYjmiYvliqDliIbnmoR1cGRhdGVQbGF5ZXJTY29yZe+8iVxuICAgICAgICB0aGlzLnNjaGVkdWxlT25jZSgoKSA9PiB7XG4gICAgICAgICAgICBpZiAodGhpcy5nYW1lU2NvcmVDb250cm9sbGVyKSB7XG4gICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICB0aGlzLmdhbWVTY29yZUNvbnRyb2xsZXIudXBkYXRlUGxheWVyU2NvcmUoYWN0aW9uLnVzZXJJZCwgdG90YWxTY29yZSk7XG5cbiAgICAgICAgICAgICAgICAvLyDmm7TmlrDlhajlsYDmlbDmja7kuK3nmoTmgLvliIZcbiAgICAgICAgICAgICAgICB0aGlzLnVwZGF0ZVBsYXllclRvdGFsU2NvcmVJbkdsb2JhbERhdGEoYWN0aW9uLnVzZXJJZCwgdG90YWxTY29yZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0sIDEuMik7XG5cbiAgICAgICAgLy8gMy4g5Zyo5omA5pyJ546p5a625aS05YOP5LiK5pi+56S65Yqg5YeP5YiG77yI5LiN5LuF5LuF5piv6Ieq5bex77yJXG4gICAgICAgIHRoaXMuc2NoZWR1bGVPbmNlKCgpID0+IHtcbiAgICAgICAgICAgIHRoaXMuc2hvd1Njb3JlT25QbGF5ZXJBdmF0YXIoYWN0aW9uLnVzZXJJZCwgYWN0aW9uLnNjb3JlKTtcbiAgICAgICAgICAgIFxuICAgICAgICB9LCAwLjEpO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOabtOaWsOWFqOWxgOaVsOaNruS4reeahOeOqeWutuaAu+WIhlxuICAgICAqIEBwYXJhbSB1c2VySWQg55So5oi3SURcbiAgICAgKiBAcGFyYW0gdG90YWxTY29yZSDmlrDnmoTmgLvliIZcbiAgICAgKi9cbiAgICBwcml2YXRlIHVwZGF0ZVBsYXllclRvdGFsU2NvcmVJbkdsb2JhbERhdGEodXNlcklkOiBzdHJpbmcsIHRvdGFsU2NvcmU6IG51bWJlcikge1xuICAgICAgICBpZiAoIUdsb2JhbEJlYW4uR2V0SW5zdGFuY2UoKS5ub3RpY2VTdGFydEdhbWUgfHwgIUdsb2JhbEJlYW4uR2V0SW5zdGFuY2UoKS5ub3RpY2VTdGFydEdhbWUudXNlcnMpIHtcbiAgICAgICAgICAgIGNvbnNvbGUud2FybihcIuayoeaciea4uOaIj+aVsOaNru+8jOaXoOazleabtOaWsOeOqeWutuaAu+WIhlwiKTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIGxldCB1c2VyczogUm9vbVVzZXJbXSA9IEdsb2JhbEJlYW4uR2V0SW5zdGFuY2UoKS5ub3RpY2VTdGFydEdhbWUudXNlcnM7XG4gICAgICAgIGNvbnN0IHVzZXJJbmRleCA9IHVzZXJzLmZpbmRJbmRleCh1c2VyID0+IHVzZXIudXNlcklkID09PSB1c2VySWQpO1xuXG4gICAgICAgIGlmICh1c2VySW5kZXggIT09IC0xKSB7XG4gICAgICAgICAgICB1c2Vyc1t1c2VySW5kZXhdLnNjb3JlID0gdG90YWxTY29yZTtcbiAgICAgICAgICAgIFxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgY29uc29sZS53YXJuKGDmib7kuI3liLDnjqnlrrY6IHVzZXJJZD0ke3VzZXJJZH1gKTtcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOafpeaJvueUqOaIt+e0ouW8lVxuICAgICAqIEBwYXJhbSB1c2VySWQg55So5oi3SURcbiAgICAgKiBAcmV0dXJucyDnlKjmiLfntKLlvJXvvIzmib7kuI3liLDov5Tlm54tMVxuICAgICAqL1xuICAgIHByaXZhdGUgZmluZFVzZXJJbmRleCh1c2VySWQ6IHN0cmluZyk6IG51bWJlciB7XG4gICAgICAgIGlmICghR2xvYmFsQmVhbi5HZXRJbnN0YW5jZSgpLm5vdGljZVN0YXJ0R2FtZSB8fCAhR2xvYmFsQmVhbi5HZXRJbnN0YW5jZSgpLm5vdGljZVN0YXJ0R2FtZS51c2Vycykge1xuICAgICAgICAgICAgY29uc29sZS53YXJuKFwi5rKh5pyJ5ri45oiP5pWw5o2u77yM5peg5rOV5p+l5om+55So5oi357Si5byVXCIpO1xuICAgICAgICAgICAgcmV0dXJuIC0xO1xuICAgICAgICB9XG5cbiAgICAgICAgbGV0IHVzZXJzOiBSb29tVXNlcltdID0gR2xvYmFsQmVhbi5HZXRJbnN0YW5jZSgpLm5vdGljZVN0YXJ0R2FtZS51c2VycztcbiAgICAgICAgcmV0dXJuIHVzZXJzLmZpbmRJbmRleCh1c2VyID0+IHVzZXIudXNlcklkID09PSB1c2VySWQpO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOWcqOeOqeWutuWktOWDj+S4iuaYvuekuuWKoOWHj+WIhlxuICAgICAqIEBwYXJhbSB1c2VySWQg55So5oi3SURcbiAgICAgKiBAcGFyYW0gc2NvcmUg5YiG5pWw5Y+Y5YyWXG4gICAgICovXG4gICAgcHJpdmF0ZSBzaG93U2NvcmVPblBsYXllckF2YXRhcih1c2VySWQ6IHN0cmluZywgc2NvcmU6IG51bWJlcikge1xuICAgICAgICAvLyDmoLnmja7lnLDlm77nsbvlnovosIPnlKjlr7nlupTnmoTmjqfliLblmahcbiAgICAgICAgaWYgKHRoaXMuY3VycmVudE1hcFR5cGUgPT09IDAgJiYgdGhpcy5jaGVzc0JvYXJkQ29udHJvbGxlcikge1xuICAgICAgICAgICAgLy8g5pa55b2i5Zyw5Zu+XG4gICAgICAgICAgICB0aGlzLmNoZXNzQm9hcmRDb250cm9sbGVyLnNob3dQbGF5ZXJHYW1lU2NvcmUodXNlcklkLCBzY29yZSk7XG4gICAgICAgIH0gZWxzZSBpZiAodGhpcy5jdXJyZW50TWFwVHlwZSA9PT0gMSAmJiB0aGlzLmhleENoZXNzQm9hcmRDb250cm9sbGVyKSB7XG4gICAgICAgICAgICAvLyDlha3ovrnlvaLlnLDlm75cbiAgICAgICAgICAgIHRoaXMuaGV4Q2hlc3NCb2FyZENvbnRyb2xsZXIuc2hvd0hleFBsYXllckdhbWVTY29yZSh1c2VySWQsIHNjb3JlKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGNvbnNvbGUud2FybihcIuayoeacieWPr+eUqOeahOaji+ebmOaOp+WItuWZqO+8jOaXoOazleaYvuekuuWktOWDj+WIhuaVsFwiKTtcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOWcqOWIhuaVsOmdouadv+aYvuekuuWKoOWHj+WIhuaViOaenFxuICAgICAqIEBwYXJhbSB1c2VySW5kZXgg55So5oi357Si5byVXG4gICAgICogQHBhcmFtIHNjb3JlIOWIhuaVsOWPmOWMllxuICAgICAqL1xuICAgIHByaXZhdGUgc2hvd1Njb3JlSW5TY29yZVBhbmVsKHVzZXJJbmRleDogbnVtYmVyLCBzY29yZTogbnVtYmVyKSB7XG4gICAgICAgIGlmICghdGhpcy5nYW1lU2NvcmVDb250cm9sbGVyKSB7XG4gICAgICAgICAgICBjb25zb2xlLndhcm4oXCJnYW1lU2NvcmVDb250cm9sbGVyIOS4jeWtmOWcqO+8jOaXoOazleWcqOWIhuaVsOmdouadv+aYvuekuuWIhuaVsFwiKTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOiOt+WPluWvueW6lOeahFBsYXllclNjb3JlQ29udHJvbGxlclxuICAgICAgICBjb25zdCBwbGF5ZXJTY29yZUNvbnRyb2xsZXIgPSB0aGlzLmdhbWVTY29yZUNvbnRyb2xsZXIuZ2V0UGxheWVyU2NvcmVDb250cm9sbGVyKHVzZXJJbmRleCk7XG4gICAgICAgIGlmIChwbGF5ZXJTY29yZUNvbnRyb2xsZXIpIHtcbiAgICAgICAgICAgIC8vIOaYvuekuuWKoOWHj+WIhuaViOaenFxuICAgICAgICAgICAgaWYgKHNjb3JlID4gMCkge1xuICAgICAgICAgICAgICAgIHBsYXllclNjb3JlQ29udHJvbGxlci5zaG93QWRkU2NvcmUoc2NvcmUpO1xuICAgICAgICAgICAgfSBlbHNlIGlmIChzY29yZSA8IDApIHtcbiAgICAgICAgICAgICAgICBwbGF5ZXJTY29yZUNvbnRyb2xsZXIuc2hvd1N1YlNjb3JlKE1hdGguYWJzKHNjb3JlKSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBjb25zb2xlLndhcm4oYOaJvuS4jeWIsOeUqOaIt+e0ouW8lSAke3VzZXJJbmRleH0g5a+55bqU55qEUGxheWVyU2NvcmVDb250cm9sbGVyYCk7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvKipcbiAgICAgKiDmmL7npLrlhYjmiYvnjqnlrrbnmoTliIbmlbDliqjnlLvvvIjlnKjliIbmlbDpnaLmnb/lhYjmmL7npLorMe+8jOWGjeaYvuekuuacrOWbnuWQiOWIhuaVsO+8iVxuICAgICAqIEBwYXJhbSBhY3Rpb24g546p5a625pON5L2c5pWw5o2uXG4gICAgICogQHBhcmFtIGN1cnJlbnRVc2VySWQg5b2T5YmN55So5oi3SURcbiAgICAgKiBAcGFyYW0gdG90YWxTY29yZSDnjqnlrrbmgLvliIZcbiAgICAgKi9cbiAgICBwcml2YXRlIHNob3dGaXJzdENob2ljZVBsYXllclNjb3JlQW5pbWF0aW9uKGFjdGlvbjogUGxheWVyQWN0aW9uRGlzcGxheSwgY3VycmVudFVzZXJJZDogc3RyaW5nLCB0b3RhbFNjb3JlOiBudW1iZXIpIHtcbiAgICAgICAgY29uc3QgdXNlckluZGV4ID0gdGhpcy5maW5kVXNlckluZGV4KGFjdGlvbi51c2VySWQpO1xuXG4gICAgICAgIC8vIOesrOS4gOatpe+8muWcqOWIhuaVsOmdouadv+aYvuekuisx5YWI5omL5aWW5Yqx77yIMS4y56eS77yM5LiO6Z2e5YWI5omL546p5a625ZCM5q2l77yJXG4gICAgICAgIHRoaXMuc2NoZWR1bGVPbmNlKCgpID0+IHtcblxuXG4gICAgICAgICAgICAvLyDliIbmlbDpnaLmnb/mmL7npLrmnKzlm57lkIjliIbmlbDvvIgrMeW3sue7j+WcqOWJjemdouaYvuekuui/h+S6hu+8iVxuICAgICAgICAgICAgaWYgKHVzZXJJbmRleCAhPT0gLTEpIHtcbiAgICAgICAgICAgICAgICB0aGlzLnNob3dTY29yZUluU2NvcmVQYW5lbCh1c2VySW5kZXgsIGFjdGlvbi5zY29yZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0sIDEuMik7XG5cbiAgICAgICAgLy8g56ys5LqM5q2l77ya5pu05paw5oC75YiG77yIMi4056eS77yJXG4gICAgICAgIHRoaXMuc2NoZWR1bGVPbmNlKCgpID0+IHtcbiAgICAgICAgICAgIGlmICh0aGlzLmdhbWVTY29yZUNvbnRyb2xsZXIpIHtcbiAgICAgICAgICAgICAgICB0aGlzLmdhbWVTY29yZUNvbnRyb2xsZXIudXBkYXRlUGxheWVyU2NvcmUoYWN0aW9uLnVzZXJJZCwgdG90YWxTY29yZSk7XG4gICAgICAgICAgICAgICAgdGhpcy51cGRhdGVQbGF5ZXJUb3RhbFNjb3JlSW5HbG9iYWxEYXRhKGFjdGlvbi51c2VySWQsIHRvdGFsU2NvcmUpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9LCAyLjQpO1xuXG4gICAgICAgIC8vIOesrOS4ieatpe+8muWcqHBsYXllcl9nYW1lX3BmYuS4reaYvuekuuacrOWbnuWQiOeahOWKoOWHj+WIhu+8iOS4jumdnuWFiOaJi+eOqeWutuWQjOatpe+8iVxuICAgICAgICB0aGlzLnNjaGVkdWxlT25jZSgoKSA9PiB7XG4gICAgICAgICAgICB0aGlzLnNob3dTY29yZU9uUGxheWVyQXZhdGFyKGFjdGlvbi51c2VySWQsIGFjdGlvbi5zY29yZSk7XG4gICAgICAgIH0sIDAuMSk7XG4gICAgfVxuXG4gICAgb25EZXN0cm95KCkge1xuICAgICAgICAvLyDnp7vpmaTmo4vnm5jngrnlh7vkuovku7bnm5HlkKxcbiAgICAgICAgaWYgKHRoaXMuY2hlc3NCb2FyZENvbnRyb2xsZXIpIHtcbiAgICAgICAgICAgIHRoaXMuY2hlc3NCb2FyZENvbnRyb2xsZXIubm9kZS5vZmYoJ2NoZXNzLWJvYXJkLWNsaWNrJywgdGhpcy5vbkNoZXNzQm9hcmRDbGljaywgdGhpcyk7XG4gICAgICAgIH1cbiAgICB9XG5cbiAgICAvLyB1cGRhdGUgKGR0KSB7fVxufVxuIl19